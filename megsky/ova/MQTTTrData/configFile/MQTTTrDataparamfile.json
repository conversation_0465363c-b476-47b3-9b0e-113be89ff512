{"sendPeriod": 60, "body": [{"model": "ADC", "file": "JCmodel_JL.json", "Topic": "ACAcquisition/notify/spont/*/ADC/", "data": [{"dev": "0A00000000000001", "guid": "ADC_F18F5FFFB2C76970DD724CCF483DDC30"}, {"dev": "0A00000000000002", "guid": "ADC_F18F5FFFB2C76970DD724CCF483DDC31"}]}, {"model": "ADC", "file": "JCmodel_JL.json", "Topic": "ACAcquisition/notify/spont/*/ADC/", "data": [{"dev": "0A00000000000001", "guid": "ADC_F18F5FFFB2C76970DD724CCF483DDC30"}, {"dev": "0A00000000000002", "guid": "ADC_F18F5FFFB2C76970DD724CCF483DDC31"}]}], "data": [{"model": "param", "param_config": [{"name": "ARtg", "type": "Float", "unit": "A", "value": "600"}, {"name": "ARtgSnd", "type": "Float", "unit": "A", "value": "5"}, {"name": "VRtg", "type": "Float", "unit": "V", "value": "220"}, {"name": "VRtgSnd", "type": "Float", "unit": "V", "value": "220"}, {"name": "A_Zero_Drift", "type": "Float", "unit": "A", "value": "0.2"}, {"name": "V_Zero_Drift", "type": "Float", "unit": "V", "value": "5"}, {"name": "heart_cycle", "type": "Int", "unit": "s", "value": "120"}, {"name": "report_cycle", "type": "Int", "unit": "s", "value": "900"}]}, {"model": "trparam", "param_info": [{"name": "ARtg", "type": "Float", "unit": "A"}, {"name": "ARtgSnd", "type": "Float", "unit": "A"}, {"name": "A_Zero_Drift", "type": "Float", "unit": "A"}, {"name": "V_Zero_Drift", "type": "Float", "unit": "V"}, {"name": "report_cycle", "type": "Int", "unit": "s"}, {"name": "VRtg", "type": "Float", "unit": "V"}, {"name": "VRtgSnd", "type": "Float", "unit": "V"}, {"name": "ImbA_Dly", "type": "Int", "unit": "s"}, {"name": "ImbA_Lim", "type": "Float", "unit": "%"}, {"name": "ImbV_Dly", "type": "Int", "unit": "s"}, {"name": "ImbV_Lim", "type": "Float", "unit": "%"}, {"name": "PTOC_Hvld_Dly", "type": "Int", "unit": "min"}, {"name": "PTOC_Hvld_Lim", "type": "Float", "unit": "%"}, {"name": "PTOC_Ovld_Dly", "type": "Int", "unit": "min"}, {"name": "PTOC_Ovld_Lim", "type": "Float", "unit": "%"}, {"name": "PTOV_Dly", "type": "Int", "unit": "s"}, {"name": "PTOV_Lim", "type": "Float", "unit": "V"}, {"name": "PTUV_Dly", "type": "Int", "unit": "s"}, {"name": "PTUV_Lim", "type": "Float", "unit": "V"}, {"name": "PTUV_Open_Dly", "type": "Int", "unit": "s"}, {"name": "PTUV_Open_V_Lim", "type": "Float", "unit": "V"}, {"name": "PowerOff_Dly", "type": "Int", "unit": "s"}, {"name": "PowerOff_V_Lim", "type": "Float", "unit": "V"}, {"name": "heart_cycle", "type": "Int", "unit": "s"}, {"name": "A_db", "type": "Float", "unit": "%"}, {"name": "HA_db", "type": "Float", "unit": "%"}, {"name": "HphV_db", "type": "Float", "unit": "%"}, {"name": "Hz_db", "type": "Float", "unit": "%"}, {"name": "Imb_db", "type": "Float", "unit": "%"}, {"name": "IndScanTm", "type": "Int", "unit": "ms"}, {"name": "Load", "type": "Float", "unit": "kVA"}, {"name": "LoadRate_db", "type": "Int", "unit": "%"}, {"name": "PF_db", "type": "Float", "unit": "%"}, {"name": "PTOC_Dly", "type": "Int", "unit": "s"}, {"name": "PTOC_Lim", "type": "Float", "unit": "A"}, {"name": "PTUC_Open_Dly", "type": "Int", "unit": "s"}, {"name": "PTUC_Open_Lim", "type": "Float", "unit": "A"}, {"name": "PTUV_Loss_A_Lim", "type": "Float", "unit": "A"}, {"name": "PTUV_Loss_Dly", "type": "Int", "unit": "s"}, {"name": "PTUV_Loss_V_Lim", "type": "Float", "unit": "V"}, {"name": "PTUV_Open_A_Lim", "type": "Float", "unit": "A"}, {"name": "PhV_db", "type": "Float", "unit": "%"}, {"name": "PowerOff_A_Lim", "type": "Float", "unit": "A"}, {"name": "Typ_db", "type": "Int", "unit": ""}, {"name": "W_db", "type": "Float", "unit": "%"}]}]}