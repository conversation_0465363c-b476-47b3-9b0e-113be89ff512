{"VER_DESC": "FFFF-20221017-01-104param", "devlist": {"devList": "104devlist", "body": [{"devNo": "0", "port": "RMT-1", "addr": "1", "model": "TTU", "desc": "IEC104", "protocol": "ttu", "manuID": "1", "isreport": "1"}, {"devNo": "1", "model": "TTU", "port": "0", "addr": "0", "desc": "TTUshujucaiji", "protocol": "<PERSON><PERSON>", "manuID": "1", "isreport": "1"}, {"devNo": "3", "port": "HPLC", "addr": "120000000034", "model": "MCCB", "desc": "", "protocol": "kaiguan", "manuID": "1", "isreport": "1"}, {"devNo": "4", "port": "RS485-1", "addr": "************", "model": "MCCB", "desc": "", "protocol": "kaiguan", "manuID": "1", "isreport": "1"}]}, "104cfg": {"DLType": "207", "DLsAddr": "6401", "HistoryFileType": "2", "MutateReportType": "0", "ParamsAddr": "8001", "SendPrio": "0", "YCReportCyc": "0", "YCTransType": "0", "YCType": "13", "YCsAddr": "4001", "YKsAddr": "6001", "YXType": "1", "YXsAddr": "1", "bgScanTime": "0", "csuAddr": "1", "csuAddrLen": "2", "infoAddr": "1", "infoAddrLen": "3", "linkAddr": "1", "linkAddrLen": "2", "localIP": "veth-container4", "mainIP": "0.0.0.0", "mainPort": "2404", "protocolType": "2", "resIP": "***********", "t0": "30", "t1": "15", "t2": "10", "t3": "20", "transCauseLen": "2", "bSelfStartLnk": "0", "wParaK": "12", "wParaW": "8", "apl_EnableJS": "1", "apl_class1_mode": "0", "apl_control_time": "20", "apl_linkResend_cos": "0", "apl_SendSoeReconnected": "1", "apl_guiyi_ycbits": "2", "apl_bSendYCType": "0", "apl_bSendYXType": "0", "apl_bSendDDType": "0", "apl_bSendYCDataType": "0", "apl_wYxBwQueLen": "256", "apl_wYcBhQueLen": "256", "apl_wCycSendTime": "3600", "apl_wCmdMaxDelay": "5", "apl_chkycbeyond_t": "1000", "apl_chkycbeyond_b": "1", "apl_kw_reconnect_init_b": "1", "Virt_Ind1": "Ind1", "Virt_Ind2": "Ind1", "dataset": [{"datatype": "YC", "data": [{"addr": "4001", "devNo": "1", "name": "PhV_phsA", "deadZone": "8.8", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "22", "tStorage": "1", "peakCalc": "1"}, {"addr": "4002", "devNo": "1", "name": "PhV_phsB", "deadZone": "8.8", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "22", "tStorage": "1", "peakCalc": "1"}, {"addr": "4003", "devNo": "1", "name": "PhV_phsC", "deadZone": "8.8", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "22", "tStorage": "1", "peakCalc": "1"}, {"addr": "4004", "devNo": "1", "name": "SeqV_c0", "deadZone": "8.8", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "22", "tStorage": "0", "peakCalc": "0"}, {"addr": "4005", "devNo": "1", "name": "A_phsA", "deadZone": "0.2", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "0.5", "tStorage": "1", "peakCalc": "0"}, {"addr": "4006", "devNo": "1", "name": "A_phsB", "YCType": "2", "deadZone": "0.2", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "0.5", "tStorage": "1", "peakCalc": "0"}, {"addr": "4007", "devNo": "1", "name": "A_phsC", "deadZone": "0.2", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "0.5", "tStorage": "1", "peakCalc": "0"}, {"addr": "4008", "devNo": "1", "name": "A_phsN", "deadZone": "0.2", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "0.5", "tStorage": "0", "peakCalc": "0"}, {"addr": "4009", "devNo": "1", "name": "SeqA_c0", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "0.5", "tStorage": "0", "peakCalc": "0"}, {"addr": "400A", "devNo": "1", "name": "ResA", "deadZone": "0.2", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "0.5", "tStorage": "1", "peakCalc": "0"}, {"addr": "400B", "devNo": "1", "name": "TotW", "deadZone": "1.1", "ratio": "1000", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "11", "tStorage": "1", "peakCalc": "0"}, {"addr": "400C", "devNo": "1", "name": "TotVar", "deadZone": "1.1", "ratio": "1000", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "11", "tStorage": "1", "peakCalc": "0"}, {"addr": "400D", "devNo": "1", "name": "TotVA", "deadZone": "1.1", "ratio": "1000", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "11", "tStorage": "1", "peakCalc": "0"}, {"addr": "400E", "devNo": "1", "name": "TotPF", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "0.5", "tStorage": "1", "peakCalc": "0"}, {"addr": "400F", "devNo": "1", "name": "Hz", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "1", "tStorage": "0", "peakCalc": "0"}, {"addr": "4010", "devNo": "1", "name": "ImbNgA", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "1", "tStorage": "0", "peakCalc": "0"}, {"addr": "4011", "devNo": "1", "name": "TimeSynSel", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "1", "tStorage": "0", "peakCalc": "0"}, {"addr": "4012", "devNo": "1", "name": "locationSrc", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "1", "tStorage": "0", "peakCalc": "0"}, {"addr": "4013", "devNo": "1", "name": "longitude", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "1", "tStorage": "0", "peakCalc": "0"}, {"addr": "4014", "devNo": "1", "name": "laitude", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "1", "tStorage": "0", "peakCalc": "0"}, {"addr": "4015", "devNo": "1", "name": "LoadRate", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "1", "tStorage": "0", "peakCalc": "0"}, {"addr": "4016", "devNo": "1", "name": "LoadMax", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "1", "tStorage": "0", "peakCalc": "0"}, {"addr": "4017", "devNo": "1", "name": "TotResLoad", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "1", "tStorage": "0", "peakCalc": "0"}, {"addr": "4018", "devNo": "1", "name": "ResLoad_phsA", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "1", "tStorage": "0", "peakCalc": "0"}, {"addr": "4019", "devNo": "1", "name": "ResLoad_phsB", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "1", "tStorage": "0", "peakCalc": "0"}, {"addr": "401A", "devNo": "1", "name": "ResLoad_phsC", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "1", "tStorage": "0", "peakCalc": "0"}, {"addr": "401B", "devNo": "1", "name": "CpuTemp", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "5", "tStorage": "0", "peakCalc": "0"}, {"addr": "401C", "devNo": "1", "name": "traffic", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "999999999", "lowerLimit": "0", "threshold": "1024", "tStorage": "0", "peakCalc": "0"}, {"addr": "4101", "devNo": "3", "name": "PhV_phsA", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "2.2", "tStorage": "1", "peakCalc": "0"}, {"addr": "4102", "devNo": "3", "name": "PhV_phsB", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "2.2", "tStorage": "1", "peakCalc": "0"}, {"addr": "4103", "devNo": "3", "name": "PhV_phsC", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "2.2", "tStorage": "1", "peakCalc": "0"}, {"addr": "4105", "devNo": "3", "name": "A_phsA", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "0.1", "tStorage": "1", "peakCalc": "0"}, {"addr": "4106", "devNo": "3", "name": "A_phsB", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "0.1", "tStorage": "1", "peakCalc": "0"}, {"addr": "4107", "devNo": "3", "name": "A_phsC", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "0.1", "tStorage": "1", "peakCalc": "0"}, {"addr": "410A", "devNo": "3", "name": "ResA", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "0.1", "tStorage": "1", "peakCalc": "0"}, {"addr": "410B", "devNo": "3", "name": "TotW", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "0.1", "tStorage": "1", "peakCalc": "0"}, {"addr": "410E", "devNo": "3", "name": "TotPF", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "0.1", "tStorage": "1", "peakCalc": "0"}, {"addr": "4121", "devNo": "4", "name": "PhV_phsA", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "2.2", "tStorage": "1", "peakCalc": "0"}, {"addr": "4122", "devNo": "4", "name": "PhV_phsB", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "2.2", "tStorage": "1", "peakCalc": "0"}, {"addr": "4123", "devNo": "4", "name": "PhV_phsC", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "2.2", "tStorage": "1", "peakCalc": "0"}, {"addr": "4125", "devNo": "4", "name": "A_phsA", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "0.1", "tStorage": "1", "peakCalc": "0"}, {"addr": "4126", "devNo": "4", "name": "A_phsB", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "0.1", "tStorage": "1", "peakCalc": "0"}, {"addr": "4127", "devNo": "4", "name": "A_phsC", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "0.1", "tStorage": "1", "peakCalc": "0"}, {"addr": "412A", "devNo": "4", "name": "ResA", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "0.1", "tStorage": "1", "peakCalc": "0"}, {"addr": "412B", "devNo": "4", "name": "TotW", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "0.1", "tStorage": "1", "peakCalc": "0"}, {"addr": "412E", "devNo": "4", "name": "TotPF", "deadZone": "0", "ratio": "1", "TVal": "0", "upperLimit": "0", "lowerLimit": "0", "threshold": "0.1", "tStorage": "1", "peakCalc": "0"}]}, {"datatype": "YX", "data": [{"addr": "1", "devNo": "1", "name": "Ind1", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "2", "devNo": "1", "name": "Ind2", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "3", "devNo": "1", "name": "Ind3", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "4", "devNo": "1", "name": "Ind4", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "5", "devNo": "1", "name": "PowerOff_Alm", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "6", "devNo": "1", "name": "PowerOn_Alm", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "7", "devNo": "1", "name": "PTUC_Alm", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "8", "devNo": "1", "name": "PTUC_Open_Alm", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "9", "devNo": "1", "name": "PTUV_Alm", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "A", "devNo": "1", "name": "PTOV_Alm", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "B", "devNo": "1", "name": "Ovld_Alm", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "C", "devNo": "1", "name": "PTOC_Hvld_Alm", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "D", "devNo": "1", "name": "PTOC_Ovld_Alm", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "E", "devNo": "1", "name": "SeqOV_Alm", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "F", "devNo": "1", "name": "SeqOA_Alm", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "10", "devNo": "1", "name": "Res_Alm", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "11", "devNo": "1", "name": "PTUPF_Alm", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "12", "devNo": "1", "name": "ImbNgA_Alm", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "13", "devNo": "1", "name": "ImbNgA_Alm_phsN", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "14", "devNo": "1", "name": "PTUV_Open_Alm", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "15", "devNo": "1", "name": "PhsSeqV_Alm", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "16", "devNo": "1", "name": "TA2_Open", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "17", "devNo": "1", "name": "TA2_byPass", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "18", "devNo": "1", "name": "TA1_byPass", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "19", "devNo": "1", "name": "TA_dev", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "1A", "devNo": "1", "name": "Virt_Ind1", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "1B", "devNo": "1", "name": "Virt_Ind2", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "1C", "devNo": "1", "name": "RS485-3", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "1D", "devNo": "1", "name": "RS485-4", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "1F", "devNo": "2", "name": "master-slave", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "21", "devNo": "3", "name": "Dev_On_Off", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "22", "devNo": "3", "name": "SwPos", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "23", "devNo": "3", "name": "PTUV_Alm", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "24", "devNo": "3", "name": "PTOV_Alm", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "31", "devNo": "4", "name": "Dev_On_Off", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "32", "devNo": "4", "name": "SwPos", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "33", "devNo": "4", "name": "PTUV_Alm", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}, {"addr": "34", "devNo": "4", "name": "PTOV_Alm", "inv": "0", "enSOE": "1", "enCOS": "1", "bootForecast": "0", "bootFailure": "0"}]}, {"datatype": "YK", "data": [{"addr": "6001", "devNo": "1", "name": "UartSwitch1", "YKType": "0"}, {"addr": "6002", "devNo": "1", "name": "UartSwitch2", "YKType": "0"}, {"addr": "6004", "devNo": "1", "name": "Uart4Mode", "YKType": "0"}, {"addr": "6021", "devNo": "3", "name": "ActCtrlAllCall", "YKType": "0"}, {"addr": "6022", "devNo": "4", "name": "ActCtrlAllCall", "YKType": "0"}, {"addr": "6023", "devNo": "3", "name": "Remote_split_closer", "YKType": "0"}, {"addr": "6024", "devNo": "4", "name": "Remote_split_closer", "YKType": "0"}]}, {"datatype": "DD", "data": [{"addr": "6401", "devNo": "1", "name": "SupWh", "ratio": "1", "threshold": "2.2", "tStorage": "1"}, {"addr": "6402", "devNo": "1", "name": "SupVarh", "ratio": "1", "threshold": "2.2", "tStorage": "1"}, {"addr": "6403", "devNo": "1", "name": "RevWh", "ratio": "1", "threshold": "2.2", "tStorage": "1"}, {"addr": "6404", "devNo": "1", "name": "RevVarh", "ratio": "1", "threshold": "2.2", "tStorage": "1"}]}, {"datatype": "PARAM", "data": [{"addr": "8001", "devNo": "0", "name": "DeviceType", "PType": "String", "ratio": "1", "sn": "0", "val": "SCU", "unit": ""}, {"addr": "8002", "devNo": "0", "name": "DeviceOS", "PType": "String", "ratio": "1", "sn": "0", "val": "Linux T3 5.10.0", "unit": ""}, {"addr": "8003", "devNo": "0", "name": "DeviceManufacturer", "PType": "String", "ratio": "1", "sn": "0", "val": "XXXX01", "unit": ""}, {"addr": "8004", "devNo": "0", "name": "Hard<PERSON><PERSON>", "PType": "String", "ratio": "1", "sn": "0", "val": "HV01.01", "unit": ""}, {"addr": "8005", "devNo": "0", "name": "SoftVer", "PType": "String", "ratio": "1", "sn": "0", "val": "SV01.001", "unit": ""}, {"addr": "8006", "devNo": "0", "name": "SoftVerChkCode", "PType": "UShort", "ratio": "1", "sn": "0", "val": "1001", "unit": ""}, {"addr": "8007", "devNo": "0", "name": "CommProtocol", "PType": "String", "ratio": "1", "sn": "0", "val": "6", "unit": ""}, {"addr": "8008", "devNo": "0", "name": "DeviceModel", "PType": "String", "ratio": "1", "sn": "0", "val": "SCU01", "unit": ""}, {"addr": "8009", "devNo": "0", "name": "DeviceSN", "PType": "String", "ratio": "1", "sn": "0", "val": "SCU1202SC001202108200006", "unit": ""}, {"addr": "800A", "devNo": "0", "name": "DeviceMAC", "PType": "String", "ratio": "1", "sn": "0", "val": "MAC:7a:7f:75:37:10:3c", "unit": ""}, {"addr": "8020", "devNo": "1", "name": "A_db", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.1", "unit": ""}, {"addr": "8021", "devNo": "1", "name": "PhV_db", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.1", "unit": ""}, {"addr": "8022", "devNo": "1", "name": "TotW_db", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.1", "unit": ""}, {"addr": "8023", "devNo": "1", "name": "A_Zero_Drift", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.04", "unit": ""}, {"addr": "8024", "devNo": "1", "name": "V_Zero_Drift", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.04", "unit": ""}, {"addr": "8025", "devNo": "1", "name": "Power_Zero_Drift", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.01", "unit": ""}, {"addr": "8026", "devNo": "1", "name": "Anti_Shake_OpDLTmms", "PType": "Uint", "ratio": "1", "sn": "0", "val": "200", "unit": ""}, {"addr": "8027", "devNo": "1", "name": "TimeSyn_Sel", "PType": "Uint", "ratio": "1", "sn": "0", "val": "0", "unit": ""}, {"addr": "8028", "devNo": "1", "name": "TimeSyn_Cyc", "PType": "Float", "ratio": "1", "sn": "0", "val": "5", "unit": ""}, {"addr": "8029", "devNo": "1", "name": "TimeSyn_Switch", "PType": "UShort", "ratio": "1", "sn": "0", "val": "1", "unit": ""}, {"addr": "802A", "devNo": "1", "name": "LocationMode", "PType": "Uint", "ratio": "1", "sn": "0", "val": "0", "unit": ""}, {"addr": "802B", "devNo": "1", "name": "VRtg", "PType": "Float", "ratio": "1", "sn": "0", "val": "220", "unit": ""}, {"addr": "802C", "devNo": "1", "name": "VRtgSnd", "PType": "Float", "ratio": "1", "sn": "0", "val": "220", "unit": ""}, {"addr": "802D", "devNo": "1", "name": "Artg", "PType": "Float", "ratio": "1", "sn": "0", "val": "500", "unit": ""}, {"addr": "802E", "devNo": "1", "name": "ARtgSnd", "PType": "Float", "ratio": "1", "sn": "0", "val": "5", "unit": ""}, {"addr": "802F", "devNo": "1", "name": "zeroLineCTPrimaryRating", "PType": "Float", "ratio": "1", "sn": "0", "val": "500", "unit": ""}, {"addr": "8030", "devNo": "1", "name": "zeroLineCTSecondaryRating", "PType": "Float", "ratio": "1", "sn": "0", "val": "5", "unit": ""}, {"addr": "8031", "devNo": "1", "name": "phaseCTSensorPolarity", "PType": "Boolean", "ratio": "1", "sn": "0", "val": "1", "unit": ""}, {"addr": "8032", "devNo": "1", "name": "zeroLineCTTransducerPolarity", "PType": "Boolean", "ratio": "1", "sn": "0", "val": "1", "unit": ""}, {"addr": "8033", "devNo": "1", "name": "DeviceLoad", "PType": "Float", "ratio": "1", "sn": "0", "val": "200", "unit": ""}, {"addr": "8034", "devNo": "1", "name": "RtgA", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.3", "unit": ""}, {"addr": "8035", "devNo": "1", "name": "LoadRate_CalcCyc", "PType": "Float", "ratio": "1", "sn": "0", "val": "1", "unit": ""}, {"addr": "8036", "devNo": "1", "name": "ResLoad_CalcCyc", "PType": "Float", "ratio": "1", "sn": "0", "val": "1", "unit": ""}, {"addr": "8037", "devNo": "1", "name": "port_101", "PType": "Uint", "ratio": "1", "sn": "0", "val": "0", "unit": ""}, {"addr": "8038", "devNo": "1", "name": "type_101", "PType": "Uint", "ratio": "1", "sn": "0", "val": "0", "unit": ""}, {"addr": "8200", "devNo": "3", "name": "switchAddr", "PType": "String", "ratio": "1", "sn": "0", "val": "120000000034", "unit": ""}, {"addr": "8201", "devNo": "3", "name": "baudRate", "PType": "Int", "ratio": "1", "sn": "0", "val": "9600", "unit": ""}, {"addr": "8202", "devNo": "3", "name": "protType", "PType": "UShort", "ratio": "1", "sn": "0", "val": "2", "unit": ""}, {"addr": "8203", "devNo": "3", "name": "port", "PType": "UShort", "ratio": "1", "sn": "0", "val": "0", "unit": ""}, {"addr": "8210", "devNo": "4", "name": "switchAddr", "PType": "String", "ratio": "1", "sn": "0", "val": "************", "unit": ""}, {"addr": "8211", "devNo": "4", "name": "baudRate", "PType": "Int", "ratio": "1", "sn": "0", "val": "9600", "unit": ""}, {"addr": "8212", "devNo": "4", "name": "protType", "PType": "UShort", "ratio": "1", "sn": "0", "val": "2", "unit": ""}, {"addr": "8213", "devNo": "4", "name": "port", "PType": "UShort", "ratio": "1", "sn": "0", "val": "1", "unit": ""}, {"addr": "8820", "devNo": "1", "name": "PowOff_Enable", "PType": "Boolean", "ratio": "1", "sn": "0", "val": "1", "unit": ""}, {"addr": "8821", "devNo": "1", "name": "PowerOff_V_Lim", "PType": "Float", "ratio": "1", "sn": "0", "val": "110", "unit": ""}, {"addr": "8822", "devNo": "1", "name": "PowerOff_Dly", "PType": "Float", "ratio": "1", "sn": "0", "val": "0", "unit": ""}, {"addr": "8823", "devNo": "1", "name": "PowOn_Enable", "PType": "Boolean", "ratio": "1", "sn": "0", "val": "1", "unit": ""}, {"addr": "8824", "devNo": "1", "name": "PowerOn_V_Lim", "PType": "Float", "ratio": "1", "sn": "0", "val": "154", "unit": ""}, {"addr": "8825", "devNo": "1", "name": "PowerOn_Dly", "PType": "Float", "ratio": "1", "sn": "0", "val": "0", "unit": ""}, {"addr": "8826", "devNo": "1", "name": "PTUA_Enable", "PType": "Boolean", "ratio": "1", "sn": "0", "val": "0", "unit": ""}, {"addr": "8827", "devNo": "1", "name": "PTUA_Dly", "PType": "Float", "ratio": "1", "sn": "0", "val": "5", "unit": ""}, {"addr": "8828", "devNo": "1", "name": "PTUA_Loss_Dly", "PType": "Float", "ratio": "1", "sn": "0", "val": "5", "unit": ""}, {"addr": "8829", "devNo": "1", "name": "PTUV_Enable", "PType": "Boolean", "ratio": "1", "sn": "0", "val": "0", "unit": ""}, {"addr": "882A", "devNo": "1", "name": "PTUV_Lim", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.93", "unit": ""}, {"addr": "882B", "devNo": "1", "name": "PTUV_Dly", "PType": "Float", "ratio": "1", "sn": "0", "val": "5", "unit": ""}, {"addr": "882C", "devNo": "1", "name": "PTOV_Enable", "PType": "Boolean", "ratio": "1", "sn": "0", "val": "0", "unit": ""}, {"addr": "882D", "devNo": "1", "name": "PTOV_Lim", "PType": "Float", "ratio": "1", "sn": "0", "val": "1.07", "unit": ""}, {"addr": "882E", "devNo": "1", "name": "PTOV_Dly", "PType": "Float", "ratio": "1", "sn": "0", "val": "5", "unit": ""}, {"addr": "882F", "devNo": "1", "name": "Load_Enable", "PType": "Boolean", "ratio": "1", "sn": "0", "val": "0", "unit": ""}, {"addr": "8830", "devNo": "1", "name": "Load_Lim", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.5", "unit": ""}, {"addr": "8831", "devNo": "1", "name": "Load_Dly", "PType": "Float", "ratio": "1", "sn": "0", "val": "5", "unit": ""}, {"addr": "8832", "devNo": "1", "name": "PTOC_Hvld_Enable", "PType": "Boolean", "ratio": "1", "sn": "0", "val": "0", "unit": ""}, {"addr": "8833", "devNo": "1", "name": "PTOC_Hvld_Lim", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.7", "unit": ""}, {"addr": "8834", "devNo": "1", "name": "PTOC_Hvld_Dly", "PType": "Float", "ratio": "1", "sn": "0", "val": "5", "unit": ""}, {"addr": "8835", "devNo": "1", "name": "PTOC_Ovld_Enable", "PType": "Boolean", "ratio": "1", "sn": "0", "val": "0", "unit": ""}, {"addr": "8836", "devNo": "1", "name": "PTOC_Ovld_Lim", "PType": "Float", "ratio": "1", "sn": "0", "val": "1", "unit": ""}, {"addr": "8837", "devNo": "1", "name": "PTOC_Ovld_Dly", "PType": "Float", "ratio": "1", "sn": "0", "val": "5", "unit": ""}, {"addr": "8838", "devNo": "1", "name": "Seqc0OV_Enable", "PType": "Boolean", "ratio": "1", "sn": "0", "val": "0", "unit": ""}, {"addr": "8839", "devNo": "1", "name": "Seqc0OV_Lim", "PType": "Float", "ratio": "1", "sn": "0", "val": "120", "unit": ""}, {"addr": "883A", "devNo": "1", "name": "Seqc0OV_Dly", "PType": "Float", "ratio": "1", "sn": "0", "val": "5", "unit": ""}, {"addr": "883B", "devNo": "1", "name": "Seqc0OC_Enable", "PType": "Boolean", "ratio": "1", "sn": "0", "val": "0", "unit": ""}, {"addr": "883C", "devNo": "1", "name": "Seqc0OC_Lim", "PType": "Float", "ratio": "1", "sn": "0", "val": "6", "unit": ""}, {"addr": "883D", "devNo": "1", "name": "Seqc0OC_Dly", "PType": "Float", "ratio": "1", "sn": "0", "val": "5", "unit": ""}, {"addr": "883E", "devNo": "1", "name": "ResA_Enable", "PType": "Boolean", "ratio": "1", "sn": "0", "val": "0", "unit": ""}, {"addr": "883F", "devNo": "1", "name": "ResA_Lim", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.5", "unit": ""}, {"addr": "8840", "devNo": "1", "name": "ResA_Dly", "PType": "Float", "ratio": "1", "sn": "0", "val": "0", "unit": ""}, {"addr": "8841", "devNo": "1", "name": "PTUPF_Enable", "PType": "Boolean", "ratio": "1", "sn": "0", "val": "0", "unit": ""}, {"addr": "8842", "devNo": "1", "name": "PTUPF_Lim", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.75", "unit": ""}, {"addr": "8843", "devNo": "1", "name": "PTUPF_Dly", "PType": "Float", "ratio": "1", "sn": "0", "val": "5", "unit": ""}, {"addr": "8844", "devNo": "1", "name": "ImbA_Enable", "PType": "Boolean", "ratio": "1", "sn": "0", "val": "0", "unit": ""}, {"addr": "8845", "devNo": "1", "name": "Imb_db", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.4", "unit": ""}, {"addr": "8846", "devNo": "1", "name": "ImbA_Lim", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.15", "unit": ""}, {"addr": "8847", "devNo": "1", "name": "ImbNgA_phsN_Lim", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.25", "unit": ""}, {"addr": "8848", "devNo": "1", "name": "ImbA_Dly", "PType": "Float", "ratio": "1", "sn": "0", "val": "5", "unit": ""}, {"addr": "8849", "devNo": "1", "name": "PhvImb_strVal", "PType": "Float", "ratio": "1", "sn": "0", "val": "0", "unit": ""}, {"addr": "884A", "devNo": "1", "name": "LoadImb_strVal", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.4", "unit": ""}, {"addr": "884B", "devNo": "1", "name": "PTOC_Hvld_Lim_phs", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.7", "unit": ""}, {"addr": "884C", "devNo": "1", "name": "PTUA_Loss_V_Lim", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.1", "unit": ""}, {"addr": "884D", "devNo": "1", "name": "PTUA_Loss_A_Lim", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.05", "unit": ""}, {"addr": "884E", "devNo": "1", "name": "PTUA_Lim", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.06", "unit": ""}, {"addr": "884F", "devNo": "1", "name": "LoadRate_db", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.05", "unit": ""}, {"addr": "8850", "devNo": "1", "name": "PTUV_Open_V_Lim", "PType": "Float", "ratio": "1", "sn": "0", "val": "30", "unit": ""}, {"addr": "8851", "devNo": "1", "name": "PTUV_Open_A_Lim", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.3", "unit": ""}, {"addr": "8852", "devNo": "1", "name": "PTUV_Open_Dly", "PType": "Float", "ratio": "1", "sn": "0", "val": "1", "unit": ""}, {"addr": "8853", "devNo": "1", "name": "PTUA_U_Lim", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.1", "unit": ""}, {"addr": "8854", "devNo": "1", "name": "PTUA_H_Lim", "PType": "Float", "ratio": "1", "sn": "0", "val": "0.1", "unit": ""}]}]}}