#!/bin/sh

APPTarPath=/usr/local/extapps/DTAnalyzer/bin
dataShardpath=/data/app
logShardpath=/log/app
backuppath=/backup

devShardPath=/dev
devTmpShardPath=/tmp/dev
appDataShardpath=/tmp/data
appLogShardpath=/run/log
halpath=/lib/hal_lib

mkdir -p $appDataShardpath
mkdir -p $appLogShardpath
mkdir -p $backuppath
mkdir -p $dataShardpath/DTAnalyzer/commFile
mkdir -p $dataShardpath/DTAnalyzer/configFile
mkdir -p $dataShardpath/DTAnalyzer/logFile
mkdir -p $logShardpath

rm -rf /backup/app_back/*

# 配置文件存在，则拷贝
if [  -f "$APPTarPath/DTAnalyzerConfig.json" ];then
	cp -fr $APPTarPath/DTAnalyzerConfig.json $dataShardpath/DTAnalyzer/configFile/
fi

# 先停止APP并卸载容器
appm -S -c pdContainer -n DTAnalyzer
#container uninstall baseContainer 
docker container ls -a | grep pdContainer > /dev/null
#如果不存在创建容器
if [ $? -ne 0 ];then    
	echo "pdContainer is not existed, we will docker creat it and install app!!!"
	container install pdContainer $APPTarPath/DTAnalyzer.tar -v $halpath:$halpath -v $devShardPath:$devShardPath -v $dataShardpath:$dataShardpath \
	-v $devTmpShardPath:$devTmpShardPath -v $appDataShardpath:$appDataShardpath -v $logShardpath:$logShardpath  -v $backuppath:$backuppath \
	-v $appLogShardpath:$appLogShardpath -v $dataDevInfoShardpath:$dataDevInfoShardpath -dev-dir -mem 400m -disk 200m
else    
	echo "pdContainer is existed, we will uninstall app!!!"
	appm -u  -c pdContainer -n DTAnalyzer
	echo "install app!!!"
	appm -i  -c pdContainer -n DTAnalyzer -f $APPTarPath/DTAnalyzer.tar
fi

#appm -s -c baseContainer -n DTAnalyzer

#ps aux |grep DTAnalyzer | grep -v grep > /dev/null
#如果不存在创建容器
#if [ $? -ne 0 ];then    
#	echo "start DTAnalyzer fail, re-install app!!!"
#	appm -i  -c baseContainer -n DTAnalyzer -f $APPTarPath/DTAnalyzer.tar
#fi 
