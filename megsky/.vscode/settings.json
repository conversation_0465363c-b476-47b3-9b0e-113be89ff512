{"files.associations": {"iostream": "cpp", "deque": "cpp", "string": "cpp", "vector": "cpp", "array": "cpp", "*.tcc": "cpp", "bitset": "cpp", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "cstdarg": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "list": "cpp", "unordered_map": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "istream": "cpp", "limits": "cpp", "new": "cpp", "ostream": "cpp", "numeric": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "cinttypes": "cpp", "utility": "cpp", "typeinfo": "cpp", "random": "cpp", "filesystem": "cpp", "chrono": "cpp", "codecvt": "cpp", "ratio": "cpp", "atomic": "cpp", "bit": "cpp", "compare": "cpp", "concepts": "cpp", "cstddef": "cpp", "map": "cpp", "unordered_set": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numbers": "cpp", "mutex": "cpp", "condition_variable": "cpp", "semaphore": "cpp", "stop_token": "cpp", "thread": "cpp"}}