#ifndef _LINECAP_DEFINES_H
#define _LINECAP_DEFINES_H
#include "linecap_buffer.h"
using linecap_buff = LINECAP::buffer;

#define LINECAP_BUFF_SET_VALUE(dst, src, len, offset) \
	memcpy(dst, src, len); \
	offset += len;

#define LINECAP_BUFF_APPEND(dst, src, len) \
    dst.append((char*)src, len);

#define LINECAP_BUFF_APPEND_TLV(dst,type,src, len) \
    {dst.append((char*)type, sizeof(char)); \
    dst.append((unsigned char*)len, sizeof(char)); \
    dst.append((char*)src, len);}

#define DTU_USER() \
    char outputbuf[128] = {};
#ifdef _WIN32
#define DTU_THROW(...) \
    sprintf_s(outputbuf, __VA_ARGS__); \
    throw std::runtime_error(std::string(outputbuf));
#else
#define DTU_THROW(...) \
    sprintf(outputbuf, __VA_ARGS__); \
    throw std::runtime_error(std::string(outputbuf));
#endif


#ifndef _WIN32
#define LOBYTE(w)           ((uint8_t)(((unsigned long)(w)) & 0xff))
#define HIBYTE(w)           ((uint8_t)((((unsigned long)(w)) >> 8) & 0xff))
#define MAKEWORD(a, b)      ((uint16_t)(((uint8_t)(((unsigned long)(a)) & 0xff)) | ((uint16_t)((uint8_t)(((unsigned long)(b)) & 0xff))) << 8))
#define MAKELONG(a, b)      ((long)(((uint16_t)(((unsigned long)(a)) & 0xffff)) | ((unsigned long)((uint16_t)(((unsigned long)(b)) & 0xffff))) << 16))
#else
#endif
#endif