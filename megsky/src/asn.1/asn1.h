#ifndef _ASN1_
#define _ASN1_

#include <string>
// #include "public_struct.h"
#include "basic_def.h"
#include "linecap_buffer.h"
#include "linecap_defines.h"
// #include "megsky_mqtt.h"
using namespace LINECAP;

namespace asn1
{

    class asn_basic
    {
    public:
        asn_basic() {}
        asn_basic(const asn_basic &src);
        asn_basic &operator=(const asn_basic &src);
        // 低字节在前 高字节在后

        char PRM;                 // 启动标志位（ PRM=1，表示启动）；
        char PRIORITY;            // ：优先级（数值越小优先级越高， 0 为最高优先级）；
        char INDEX;               // ：消息序号（从 0 循环递增，响应消息同请求消息保持一致）；
        short LABEL;              // ：消息标签（发送方附加标签，响应时带回）；Q/GDW XXXXX—2021
        std::string SOURCE;       // ：消息发送方名称，字符串，以 0 结尾， 命名规范见 6.1；
        std::string DESTINATION;  // ：消息接收方名称，字符串，以 0 结尾， 命名规范见 6.1；
        msg_tag MsgTag;           // ’s—TAG：消息标识；
        unsigned char MSG_Length; // ’s—Length：消息有效载荷长度，采用可变长度编码（ A-XDR）；
        linecap_buff MSG_Payload; // ’s—Payload：有效载荷，即消息数据单元
        linecap_buff serialize();
        bool unserialize(const linecap_buff &src);
        bool unserialize(char*srcs,int len);
        // int msg_publish();

    private:
    };

    class DIINFO
    {
    public:
        // 低字节在前 高字节在后
        char data_no[4];           // 数据项标识
        unsigned char attributeid; // 属性标识
        linecap_buff encoder();
        bool decoder(const linecap_buff &src);
        uint8_t size;

    private:
    };

    class data
    {
    public:
        // 低字节在前 高字节在后
        DIINFO DATA_info;           // 数据项标识
        unsigned short dataclassid; // 数据类标识
        // std::vector<unsigned char> databuffer;//数据缓冲
        linecap_buff databuffer; // 数据缓冲

        linecap_buff deal_data(asn1::DIINFO &fo,int type,char flage,int value);
        linecap_buff deal_vt(asn1::DIINFO &fo, char flage, double value);
        linecap_buff deal_vt(asn1::DIINFO &fo, char flage, int value);
        void asn1_send_pack_data(asn1::data &da_s, asn1::DIINFO fo, unsigned char dataclassid, linecap_buff datas);

        linecap_buff encoder();
        bool decoder(const linecap_buff &src);
        int buff_to_value();
        uint8_t size;

    private:
    };

    class write_data
    {
    public:
        // 低字节在前 高字节在后
        unsigned char log_dev_number;
        unsigned short information_number; //
        std::vector<data> dataset;         // 数据集合
        linecap_buff encoder();
        bool decoder(const linecap_buff &src);
        uint8_t size;

    private:
        linecap_buff get_dataset();
    };
    class write_data_ack
    {
    public:
        bool decoder(const linecap_buff &src);
        linecap_buff encoder();

    public:
        uint8_t station;
    };
    void asn1_send_data(write_data &data);

} // namespace asn1

#endif