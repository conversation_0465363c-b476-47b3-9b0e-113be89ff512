#ifndef	_SOCKET_COMM_H_
#define	_SOCKET_COMM_H_

// ========================Socket通讯包装类============================
// 调用者使用：
// 1、创建对象
// 2、connect()，非阻塞方式，马上返回，可能连接未完成
// 3、通道轮寻到此处，IsConnected()，若没有连接，再调用connect()，
//    程序中用select()判断连接是否成功， 
//
///////////////////////////////////////////////////////////////////////
#include "stdio.h"
#include "tzc_std.h"
#if defined(WIN32)
	#include <WinSock.h>
#else
	#include <strings.h>
	#include <fcntl.h>
	#include <sys/types.h>
	#include <unistd.h>
	#include <sys/socket.h>
	#include <sys/wait.h>
	#include <netinet/in.h>
	#include <arpa/inet.h>
	#include <errno.h>
#endif

// socket地址结构
typedef struct _s_socket_param
{
	unsigned char	type;				// 0:TCP; 1:UDP;2:TCPSERVER
	unsigned short  portNumber;			// 端口号
	char			IPAddress[16];		// IP地址，如"***************"
}s_socket_param;

// 客户端连接参数
typedef struct _s_link_sock
{
	int				s;					// socket号
	s_socket_param	param;				// 远端客户端连接参数
}s_link_sock;

enum SOCKET_CONNECT_STATE{
	SOCKET_NOTCONNECTTED = -1,
	SOCKET_CONNECTTING,
	SOCKET_CONNECTNORMAL
};
// 网络通信协议类型
enum 
{
	Link_Unknown = -1,
	TCP_TYPE = 0,
	UDP_TYPE = 1,
	TCPSERVER_TYPE = 2,
};

class CSocketComm
{
public:
	~CSocketComm();

	// 构造函数
	CSocketComm();
	CSocketComm(s_socket_param sockaddr,bool ServerSocketFlag);
	CSocketComm(int sock,s_socket_param param);
public:
	//客户端SOCKET设置
	void SetSocketParam(s_socket_param sockaddr, bool ServerSocketFlag);

	// 监听&Link SOCKET设置
	void SetLinkSocket(int sock,s_socket_param param);

	// 建立连接，返回：1：连接成功；0：正在连接；－1：连接失败
	int Connect();

	// 设定监听多少个客户端，并开始监听
	bool Listen(short clientCount);

	// 创建一个对象和客户端连接，只用于TCP服务器
	CSocketComm* Accept();

	//返回客户端连接的SOCKET和参数
	bool Accept(s_link_sock & link);
	
	//关闭SOCKET
	static void CloseSocket(int sock_id);

	// 断开连接
	bool Close();

	// 是否已经连接
	unsigned char GetConnectState()
	{
		return m_ConnectState;
	}

	bool isAlreadyListen();

	// 接收数据，返回：接收到数据字节个数，若返回-1，表示连接中断
	int ReadData(unsigned char* data, int count);

	// 发送数据，返回：实际发送数据字节个数，若返回-1，表示连接中断
	int WriteData(unsigned char* data, int count);	
	
	CIIString getIP();
	
	s_socket_param GetClientParam();

	int GetSocket();
private:
	sockaddr_in		m_sockaddr;
	int				m_socket;

	unsigned char	m_Type;	// 0:TCP; 1:UDP
	fd_set	m_readSet;		//读记录集
	fd_set  m_writeSet;		//写记录集

	fd_set  m_exceptSet;	//异常
	timeval	m_tTimeout;		//接收线程阻塞间隔，兼作定时器

	bool m_isAlreadyListen;

	//socket类型
	int m_sockettype;		// 0:clientsocket 1:serversocket
	// 连接状态
	int	m_ConnectState;
	// 对方的信息
	s_socket_param m_clientparam;

private:
	// 将Socket地址结构转到标准结构
	void ConvertSockAddr(s_socket_param addr1, sockaddr_in& addr2,bool ListenFlag);
	// 初始化读写记录
	void InitRWSet(long nSec,long uSec);
	//初始化socket
	int InitSocket();
};
#endif
