//=========================================================================================
// JYF-ISG 智能远动机项目，需要考虑串口底层收发机制，设计Linux系统下的串口收发处理类
//
// 尚未测试
//
// suhuaiguang 2014-5
//=========================================================================================

#include "pub_std.h"

class CPosixSerialComDevice;

//=========================================================================================
//串口通信驱动线程类
//=========================================================================================
class CPosixSerialComDevThread : public CSL_thread
{
public:
	CPosixSerialComDevThread(void);
	virtual~CPosixSerialComDevThread(void);
	void SetObject(CPosixSerialComDevice * p);

	virtual void run(void);
	virtual std::string ThreadName(void) const;
	virtual void RecordLog(const std::string & sMsg);

private:
	CPosixSerialComDevice * m_pObject;
};


//=========================================================================================
//POSIX系统的串口通信设备类
//=========================================================================================
class CPosixSerialComDevice
{
public:
	CPosixSerialComDevice(CSerialDevInterface * pDI);
	virtual~CPosixSerialComDevice(void);

public:
    void SetParam(const CSerialCommParam & r);
	bool IsOpen(void);
    bool Open(void);
    bool Close(void);
    bool Set(void);

    bool StartWatch(void);
    bool CloseWatch(void);

	void Reset(void);
    bool IsSendComplete(void);
	void NotifySendData(void);

private:
	std::string Name(void) const;
	void ReadData(void);
	void SendData(void);

	void thread_prev(void);
	void thread_func(void);
	void thread_exit(void);

private:
	CSerialDevInterface *	m_pSDI;			//外部串口设备接口对象
	CSerialCommParam		m_param;		//串口参数
	bool					m_bOpen;
	bool					m_bSelfOpen;	//自己打开串口的标志
	int						m_nComFd;		//串口文件句柄

	enum {RECV_BUF_LEN = 2048, SEND_BUF_LEN = 2048};
	Jint32					m_nPreSendNumber;		//准备发送的数据字节数
	char					m_cSend[SEND_BUF_LEN];
	char					m_cRecv[RECV_BUF_LEN];

	bool						m_bRunning;
	CPosixSerialComDevThread	m_thread;

	friend class CPosixSerialComDevThread;
};
//=========================================================================================
//POSIX系统的串口设备操作符管理类
//=========================================================================================

class CPosixSerialFdManager
{
public:
	static CPosixSerialFdManager & Instance(void);
	void SetSerialComDevice(const std::string & sCOM, Jint32 nCOMFd);
	bool GetSerialComDevice(const std::string & sCOM, Jint32 & nCOMFd);

private:
	CPosixSerialFdManager(void);
	virtual~CPosixSerialFdManager(void);

private:
	typedef std::map<std::string, Jint32> COMDEVICES;
	CIIMutex		m_lock;
	COMDEVICES		m_comdevs;
};
