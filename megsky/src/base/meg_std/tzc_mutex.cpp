//====================================================================
// 文件名: tzc_mutex_unix.cpp
//
// 文件描述:
// ------------------------------------------------------------------
// 跨平台通用接口互斥类unix平台实现
// ------------------------------------------------------------------
//
// 时间: 2020.9
// 编程: 田振超
// ------------------------------------------------------------------
// 修改说明(请按格式说明)...
//====================================================================

#include <pthread.h>
#include <string.h>
#include "tzc_std.h"

struct SIIMutexPrivate
{
	pthread_mutex_t m_mutex;
};

CIIMutex::CIIMutex()
{
	pthread_mutexattr_t tMutexAttr;

	m_pMutexPrivate = new SIIMutexPrivate;

	pthread_mutexattr_init(&tMutexAttr);

	pthread_mutexattr_settype(&tMutexAttr, PTHREAD_MUTEX_RECURSIVE);
	pthread_mutex_init(&m_pMutexPrivate->m_mutex, &tMutexAttr);

	pthread_mutexattr_destroy(&tMutexAttr);
}

CIIMutex::~CIIMutex()
{
	pthread_mutex_destroy(&m_pMutexPrivate->m_mutex);
	delete m_pMutexPrivate;
}

bool CIIMutex::TryLock()
{
	return (pthread_mutex_trylock(&m_pMutexPrivate->m_mutex) == 0);
}

void CIIMutex::Lock()
{
	pthread_mutex_lock(&m_pMutexPrivate->m_mutex);
}

void CIIMutex::Unlock()
{
	pthread_mutex_unlock(&m_pMutexPrivate->m_mutex);
}
