#include "pub_ipaddr.h"
#include "tzc_std.h"
#include <stdlib.h>

using namespace std;

//============================================================================
//IP地址+端口
//============================================================================
CIIIPAddressPort::CIIIPAddressPort()
: m_b1(0), m_b2(0), m_b3(0), m_b4(0)
, m_port(0)
{
}

CIIIPAddressPort::CIIIPAddressPort(const string & strIPAddr)
{
	int cnt(0);
	string strByte[5];
	for(size_t i=0; i < strIPAddr.size(); i++){
		if(strIPAddr[i] == '.' || strIPAddr[i] == ':') {
			cnt++;
			if(cnt>4)break;
		}else{
			strByte[cnt] += strIPAddr[i];
		}
	}

	//ip地址
	m_b1 = atoi(strByte[0].c_str());
	m_b2 = atoi(strByte[1].c_str());
	m_b3 = atoi(strByte[2].c_str());
	m_b4 = atoi(strByte[3].c_str());

	//端口
	m_port = atoi(strByte[4].c_str());
}

CIIIPAddressPort::CIIIPAddressPort(const char * pIP, unsigned short port)
: m_port(port)
{
	int cnt(0);
	string strByte[4];
	string strIPAddr(pIP);

	for(size_t i=0; i < strIPAddr.size(); i++){
		if(strIPAddr[i] == '.') {
			cnt++;
			if(cnt>3)break;
		}else{
			strByte[cnt] += strIPAddr[i];
		}
	}

	//ip地址
	m_b1 = atoi(strByte[0].c_str());
	m_b2 = atoi(strByte[1].c_str());
	m_b3 = atoi(strByte[2].c_str());
	m_b4 = atoi(strByte[3].c_str());
}

CIIIPAddressPort::CIIIPAddressPort(Juint32 dw, Juint16 port)
: m_port(port)
{
	DW(dw);
}

CIIIPAddressPort::CIIIPAddressPort(unsigned char b1, unsigned char b2, unsigned char b3, unsigned char b4, unsigned short port)
: m_b1(b1), m_b2(b2), m_b3(b3), m_b4(b4)
, m_port(port)
{
}

CIIIPAddressPort::CIIIPAddressPort(const CIIIPAddressPort & ip)
: m_b1(ip.m_b1), m_b2(ip.m_b2), m_b3(ip.m_b3), m_b4(ip.m_b4)
, m_port(ip.m_port)
{
}

string CIIIPAddressPort::ToString() const
{
	string strRet;
	CIIString iisTemp;

	if (0 == m_b1)
		strRet.append("0.");
	else {
		iisTemp.Format("%d.", m_b1);
		strRet.append(iisTemp.GetBuf());
	}

	if (0 == m_b2)
		strRet.append("0.");
	else {
		iisTemp.Format("%d.", m_b2);
		strRet.append(iisTemp.GetBuf());
	}

	if (0 == m_b3)
		strRet.append("0.");
	else {
		iisTemp.Format("%d.", m_b3);
		strRet.append(iisTemp.GetBuf());
	}

	if (0 == m_b4)
		strRet.append("0");
	else {
		iisTemp.Format("%d", m_b4);
		strRet.append(iisTemp.GetBuf());
	}

	//端口
	iisTemp.Format(":%d", m_port);
	strRet.append(iisTemp.GetBuf());

	return strRet;
}

string CIIIPAddressPort::IPAddr(void) const
{
	CIIString iisTemp;
	iisTemp.Format("%d.%d.%d.%d", m_b1, m_b2, m_b3, m_b4);
	return string(iisTemp.GetBuf());
}

bool CIIIPAddressPort::ModifySubNetAddr(const CIIIPAddressPort & ip)
{
	bool b(false);
	if (ip.B1() > 0) {
		b = true;
		if (ip.B1() > 0 && ip.B1() < 128) {
			m_b1 = ip.B1();
		}else if (ip.B1() >= 128 && ip.B1() < 192) {
			m_b1 = ip.B1();
			m_b2 = ip.B2();
		}else if (ip.B1() >= 192 && ip.B1() < 255) {
			m_b1 = ip.B1();
			m_b2 = ip.B2();
			m_b3 = ip.B3();
		}else {
			b = false;
		}
	}

	//端口
	m_port = ip.Port();

	return b;
}

bool CIIIPAddressPort::IsInEqualSubNet(const CIIIPAddressPort & ip)
{
	bool b(false);
	if (ip.B1() > 0 && ip.B1() == this->B1()) {
		if (m_b1 > 0 && m_b1 < 128) {
			b = (ip.B1() == m_b1);
		}else if (m_b1 >= 128 && m_b1 < 192) {
			b = (ip.B1() == m_b1 && ip.B2() == m_b2);
		}else if (m_b1 >= 192 && m_b1 < 255) {
			b = (ip.B1() == m_b1 && ip.B2() == m_b2 && ip.B3() == m_b3);
		}
	}
	return b;
}

bool CIIIPAddressPort::operator < (const CIIIPAddressPort & ip) const
{
	string s1 = this->ToString();
    string s2 = ip.ToString();
    return s1<s2;
}

void CIIIPAddressPort::operator =  (const CIIIPAddressPort & ip)
{
	m_b1 = ip.m_b1;
	m_b2 = ip.m_b2;
	m_b3 = ip.m_b3;
	m_b4 = ip.m_b4;

	//端口
	m_port = ip.m_port;
}

bool CIIIPAddressPort::operator == (const CIIIPAddressPort & ip)
{
	string str = ip.ToString();
	if (0 == str.compare(this->ToString()))
		return true;
	return false;
}

void CIIIPAddressPort::operator += (unsigned char b)
{
	if (m_b1 >= 128 && m_b1 < 192) {
		if (m_b4 < 254) {
			m_b4 += b;
			if (m_b4 >= 254) {
				m_b4 = 1;
				if (m_b3 < 254)
					m_b3 += b;
			}
		}else {
			m_b4 = 1;
			if (m_b3 < 254)
				m_b3 += b;
		}
	}
	else if (m_b1 >= 192) {
		if (m_b4 < 254)
			m_b4 += b;
	}
}

CIIIPAddressPort CIIIPAddressPort::operator + (unsigned char b)
{
	CIIIPAddressPort tmpAddress(*this);
	tmpAddress += b;
	return tmpAddress;
}

Juint32 CIIIPAddressPort::DW() const
{
	unsigned long ulTmp(0);

	ulTmp |= m_b1;
	ulTmp <<= 8;

	ulTmp |= m_b2;
	ulTmp <<= 8;

	ulTmp |= m_b3;
	ulTmp <<= 8;

	ulTmp |= m_b4;

	return ulTmp;
}

void CIIIPAddressPort::DW(Juint32 dw)
{
	unsigned long ulTmp(dw);
	
	m_b4 = (unsigned char)ulTmp;
	ulTmp >>= 8;

	m_b3 = (unsigned char)ulTmp;
	ulTmp >>= 8;

	m_b2 = (unsigned char)ulTmp;
	ulTmp >>= 8;

	m_b1 = (unsigned char)ulTmp;
}

Juint16 CIIIPAddressPort::Port() const
{
	return m_port;
}

void CIIIPAddressPort::Port(Juint16 ul)
{
	m_port = ul;
}

void CIIIPAddressPort::ZeroPort()
{
	m_port = 0;
}

//IP地址&端口
CStream& operator<<(CStream& stream, const CIIIPAddressPort& item)
{
	stream << item.DW();
	stream << item.Port();
	return stream;
}

CStream& operator>>(CStream& stream, CIIIPAddressPort& item)
{
	Juint32 ip(0);
	stream >> ip;
	item.DW(ip);

	Juint16 port(0);
	stream >> port;
	item.Port(port);

	return stream;
}

Juint32 WriteToStreamLength(const CIIIPAddressPort& item)
{
	return WriteToStreamLength(item.DW())
		+ WriteToStreamLength(item.Port());
}
