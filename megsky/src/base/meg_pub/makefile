##############################################################################
# Copyright (c) 2020 山东梅格彤天电气有限公司 http://www.megsky.com
#
# @file    : Makefile
# @brief   : STD library Makefile
# @note
#
# 
##############################################################################

TZCDIR			 = ../../..
APPNAME			 = meg_pub
CROSS_COMPILE	?= aarch64-linux-gnu-
#CROSS_COMPILE	?= /usr/local/arm_scu/gcc-linaro-7.3.1-2018.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-
TARGET  		?= $(TZCDIR)/lib/libmegpub.so


CC 				:= $(CROSS_COMPILE)gcc

INCDIRS		:=  $(TZCDIR)/src/include \
				$(TZCDIR)/src/include/tzc_std \
				/usr/include/uuid

SRCDIRS		:=  $(TZCDIR)/src/base/$(APPNAME) \
				

INCLUDE		:= $(patsubst %, -I %, $(INCDIRS))

CPPFILES	:= $(foreach dir, $(SRCDIRS), $(wildcard $(dir)/*.cpp))
CPPFILENDIR	:= $(notdir  $(CPPFILES))
CPPOBJS		:= $(patsubst %, $(TZCDIR)/obj/$(APPNAME)/%, $(CPPFILENDIR:.cpp=.o))

CFILES		:= $(foreach dir, $(SRCDIRS), $(wildcard $(dir)/*.c))
CFILENDIR	:= $(notdir  $(CFILES))
COBJS		:= $(patsubst %, $(TZCDIR)/obj/$(APPNAME)/%, $(CFILENDIR:.c=.o))

OBJS		:= $(CPPOBJS) $(COBJS)

CFLAGS  	:= -fPIC -shared $(INCLUDE) $(ADDED_CFLAGS) 
LDFLAGS		:=-L$(TZCDIR)/lib -L$(TZCDIR)/lib/third_lib -L/usr/lib -lpthread  -ltzcstd -luuid 

VPATH		:= $(SRCDIRS)

.PHONY : clean packet


$(TARGET) : $(OBJS)
	$(CC) $(CFLAGS) $(OBJS) $(LDFLAGS) -o $@ 

$(CPPOBJS):$(TZCDIR)/obj/$(APPNAME)/%.o: %.cpp
	$(CC) -c $(CFLAGS) $(INCLUDE) $< -o $@ 

$(COBJS):$(TZCDIR)/obj/$(APPNAME)/%.o: %.c
	$(CC) -c $(CFLAGS) $(INCLUDE) $< -o $@ 

pac :
	objdump -d $(APPNAME) > MM.s
	
clean :
	rm -rf $(OBJS)
	rm -rf $(TARGET)

