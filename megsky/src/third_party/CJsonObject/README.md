English | [中文](/README_cn.md)

[![](https://travis-ci.org/Bwar/CJsonObject.svg?branch=master)](https://travis-ci.org/Bwar/CJsonObject) [![Author](https://img.shields.io/badge/<EMAIL>?style=flat)](<EMAIL>) [![License](https://img.shields.io/github/license/mashape/apistatus.svg)](LICENSE)<br/>

[![](CJsonObject)](https://raw.githubusercontent.com/Bwar/bwar.github.io/master/style/images/logo-CJsonObject.png) <br/>

CJsonObject is a newly developed C++ version based on an older version of cJSON. The biggest advantage of CJsonObject is that it is light, simple and easy to use, and the development efficiency is very high. CJsonObject is much simpler and easier to use than cJSON.

Bwar's first use of cJSON was the development of a mobile push project in 2013. At that time, although cJSON was useful, but it was easy to forget about free the memory allocated by cJSON. In 2014, Bwar use cJSON again when developing another project. In order to improve the ease of use of cJSON and improve the development efficiency, cJSON was encapsulated and 64-bit integers were supported. In the development of CJsonObject, cJSON was modified slightly.

CJsonObject has verified its stability in several successful case for 5 years. At the same time, CJsonObject integrated into [Nebula](https://github.com/Bwar/Nebula) and was widely used.

Here is a wiki [FAQ](https://github.com/Bwar/CJsonObject/wiki/FAQ) in chinese.
