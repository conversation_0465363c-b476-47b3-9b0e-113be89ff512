##############################################################################
# Copyright (c) 2021 ɽ��÷��ͮ��������޹�˾ http://www.megsky.com
#
# @file    : Makefile
# @brief   : Data board library Makefile
# @note
#

##############################################################################

TZCDIR			 = ../..
APPNAME			 = IotAgent
CROSS_COMPILE	?= aarch64-linux-gnu-
TARGET  		?= $(TZCDIR)/bin/$(APPNAME)



CC 				:= $(CROSS_COMPILE)g++

INCDIRS		:=  $(TZCDIR)/src/include \
				$(TZCDIR)/src/include/roma \
				$(TZCDIR)/src/include/tzc_std \
				$(TZCDIR)/src/include/meg_pub \
				$(TZCDIR)/src/third_party/CJsonObject \

SRCDIRS		:=  $(TZCDIR)/src/$(APPNAME)  \
				$(TZCDIR)/src/third_party/CJsonObject \


INCLUDE		:= $(patsubst %, -I %, $(INCDIRS))

CPPFILES	:= $(foreach dir, $(SRCDIRS), $(wildcard $(dir)/*.cpp))
CPPFILENDIR	:= $(notdir  $(CPPFILES))
CPPOBJS		:= $(patsubst %, $(TZCDIR)/obj/$(APPNAME)/%, $(CPPFILENDIR:.cpp=.o))

CFILES		:= $(foreach dir, $(SRCDIRS), $(wildcard $(dir)/*.c))
CFILENDIR	:= $(notdir  $(CFILES))
COBJS		:= $(patsubst %, $(TZCDIR)/obj/$(APPNAME)/%, $(CFILENDIR:.c=.o))

OBJS		:= $(CPPOBJS) $(COBJS)

CFLAGS  	:= -Wall -Wl,-rpath=$(TZCDIR)/lib:$(TZCDIR)/lib/third_lib
LDFLAGS		:=-L$(TZCDIR)/lib -L$(TZCDIR)/lib/third_lib  -L/usr/lib -lpthread -lpaho-mqtt3c -ltzcstd -lmegpub -lssl -lcrypto -lnsl -lpaho-mqtt3as -lHWMQTT -lcjson -lsecurec -lssl -lcrypto

VPATH		:= $(SRCDIRS)

.PHONY : clean packet


$(TARGET) : $(OBJS)
	$(CC) $(CFLAGS) $(OBJS) $(LDFLAGS) -o $@ 

$(CPPOBJS):$(TZCDIR)/obj/$(APPNAME)/%.o: %.cpp
	$(CC) -c $(CFLAGS) $(INCLUDE) $< -o $@ 

$(COBJS):$(TZCDIR)/obj/$(APPNAME)/%.o: %.c
	$(CC) -c $(CFLAGS) $(INCLUDE) $< -o $@ 

pac :
	objdump -d $(APPNAME)  > MM.s
	
clean :
	rm -rf $(OBJS)
	rm -rf $(TARGET)

packet :
	rm -f $(TZCDIR)/ova/$(APPNAME)/bin/$(APPNAME)
	rm -f $(TZCDIR)/ova/MQTTIoT.tar
	# rm -f $(TZCDIR)/ova/$(APPNAME)/packet/*.deb
	cp $(TARGET) $(TZCDIR)/ova/$(APPNAME)/bin/$(APPNAME)
	cd $(TZCDIR)/ova/&& tar -zcvf MQTTIoT.tar.gz MQTTIoT/ && tar -cvf MQTTIoT.tar MQTTIoT.tar.gz && rm -f MQTTIoT.tar.gz

pack :
	rm -f $(TZCDIR)/ova/$(APPNAME)/bin/$(APPNAME)
	rm -f $(TZCDIR)/ova/IotAgent.tar
	# rm -f $(TZCDIR)/ova/$(APPNAME)/packet/*.deb
	cp $(TARGET) $(TZCDIR)/ova/$(APPNAME)/bin/$(APPNAME)
	cd $(TZCDIR)/ova/&& tar -zcvf IotAgent.tar IotAgent/ 
