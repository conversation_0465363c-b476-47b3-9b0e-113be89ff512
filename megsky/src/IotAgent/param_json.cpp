#include <openssl/md5.h>
#include "mqtt_pub_inter.h"
#include "task_thread.h"
#include "param_json.h"
gateway_connect_s  m_lianwang;

CFileObj::CFileObj(void)
{

}

CFileObj::~CFileObj()
{
}


CParamThread::CParamThread(void)
: m_pMng(NULL)
{

}

CParamThread::~CParamThread()
{
}

void CParamThread::SetPackageAcquireManager(CParamManager * p)
{
	m_pMng = p;
}

void CParamThread::run(void)
{
	m_pMng->thread_prev();
	while (CSL_thread::IsEnableRun())
	{
		m_pMng->thread_func();
	}
	m_pMng->thread_exit();
}

std::string CParamThread::ThreadName(void) const
{
	return std::string("MQTT 内部Broker线程");
}

void CParamThread::RecordLog(const std::string & sMsg)
{

}

//***************************************************************
//报文获取管理者
//***************************************************************
CParamManager& CParamManager::CreateInstance()
{
	static CParamManager Mng;
	return Mng;
}


bool CParamManager::Init(void)
{
	bool bRet(false);
    // m_FileObjs.clear();
    while(!ReadJsonFile()) {
        ii_sleep(1000 * 10);
        
    }

    // if (!ReadModelFile()) {
    //     return false;
    // }

	// bRet = m_Thread.start();
	return true;
}

void CParamManager::Exit(void)
{	

	m_Thread.close();
	m_Thread.wait_for_end();
}

void CParamManager::thread_prev(void)
{

}

void CParamManager::thread_func(void)
{
	OnRun();
}

void CParamManager::thread_exit(void)
{

}
void CParamManager::OnRun(void)
{
    //向华为esdk发送数据和心跳
    if (m_T.CalOvertime()) {
        //CParamJsonManager::CreateInstance().MqttGetDevEsn(CFG_APP_NAME "/get/request/esdk/deviceInfo");
        m_T.StartCount();
    }

    ii_sleep(100);
}

void CParamManager::Start(void)
{

}

void CParamManager::Stop(void)
{


}


void copyFile(const char* srcPath, const char* destPath) {
    FILE* srcFile = fopen(srcPath, "rb");
    if (srcFile == NULL) {
        mskprintf("Source file not found.\n");
        return;
    }

    FILE* destFile = fopen(destPath, "wb");
    if (destFile == NULL) {
        mskprintf("Failed to create destination file.\n");
        fclose(srcFile);
        return;
    }

    char buffer[1024];
    size_t bytesRead;

    while ((bytesRead = fread(buffer, 1, sizeof(buffer), srcFile)) > 0) {
        fwrite(buffer, 1, bytesRead, destFile);
    }

    fclose(srcFile);
    fclose(destFile);
    printf("File copied successfully.\n");
}
#define IEC_PARAMS_FILEPATH "/data/app/SCMQTTIoT/configFile/paramFile.json"
#define CONFIGFILE_MOREN "/usr/local/extapps/MQTTIoT/configFile/paramFile.json"

bool CParamManager::ReadJsonFile()
{

FILE* file = fopen(IEC_PARAMS_FILEPATH, "rb");
    // if (file == NULL) {
    //     mskprintf("switchCollect_para.json not found, copying default config file...\n");
    //     copyFile(CONFIGFILE_MOREN, IEC_PARAMS_FILEPATH);
    // } else {
    //     fclose(file);
    //     mskprintf("switchCollect_para.json found..\n");
    // }

    CIIFile iiFile(IEC_PARAMS_FILEPATH);
    int filesize = iiFile.GetLength();
    mskprintf("filesize == (%d).\n", filesize);
    if (filesize == 0) {
        return false;
    }
    Jchar dataBuf[filesize] = { 0 };

    if (iiFile.Open("r+"))	//file exist
    {
        Jboolean bRead = iiFile.Read(dataBuf, filesize);
        if (!bRead) {
            IEC_LOG_RECORD(eErrType, "file read" IEC_MQTTFILE_FILEPATH "failed.");
            return false;
        }
    }
    iiFile.Close();

    mskprintf("dataBuf == (%s).\n", dataBuf);
    neb::CJsonObject oJson;
    if (!oJson.Parse(dataBuf))
    {
        IEC_LOG_RECORD(eErrType, "json_loadb real_data.msg_send failed.");
        return false;
    }

    if (!oJson.Get("HOST", m_lianwang.g_gatewayAddr)) {
        IEC_LOG_RECORD(eErrType,"file:" IEC_MQTTFILE_FILEPATH "read body sendPeriod failed.");
        return false;
    }

    if (!oJson.Get("PORT", m_lianwang.g_gatewayPort)) {
        IEC_LOG_RECORD(eErrType,"file:" IEC_MQTTFILE_FILEPATH "read body sendNum failed.");
    }

    if (!oJson.Get("PASSWORD", m_lianwang.g_gatewayPaawd)) {
        IEC_LOG_RECORD(eErrType,"file:" IEC_MQTTFILE_FILEPATH "read body yxcycle failed.");
    }
    if (!oJson.Get("workPath", m_lianwang.g_workPath)) {
        IEC_LOG_RECORD(eErrType,"file:" IEC_MQTTFILE_FILEPATH "read body yxcycle failed.");
    }
    if (!oJson.Get("keepAlivetime", m_lianwang.g_keepAlivetime)) {
        IEC_LOG_RECORD(eErrType,"file:" IEC_MQTTFILE_FILEPATH "read body yxcycle failed.");
    }
    // if (!oJson.Get("USERNAME", m_lianwang.g_gatewayDeviceId)) {
    //     IEC_LOG_RECORD(eErrType,"file:" IEC_MQTTFILE_FILEPATH "read body yxcycle failed.");
    // }
    if (!oJson.Get("Connectionmethod", m_lianwang.g_Connectionmethod)) {
        IEC_LOG_RECORD(eErrType,"file:" IEC_MQTTFILE_FILEPATH "read body yxcycle failed.");
    }
    if (!oJson.Get("destIp", m_lianwang.destIp)) {
        IEC_LOG_RECORD(eErrType,"file:" IEC_MQTTFILE_FILEPATH "read body yxcycle failed.");
    }
    if (!oJson.Get("netMask", m_lianwang.netMask)) {
        IEC_LOG_RECORD(eErrType,"file:" IEC_MQTTFILE_FILEPATH "read body yxcycle failed.");
    }
    if (!oJson.Get("gateway", m_lianwang.gateway)) {
        IEC_LOG_RECORD(eErrType,"file:" IEC_MQTTFILE_FILEPATH "read body yxcycle failed.");
    }
    if (!oJson.Get("interface", m_lianwang.interface)) {
        IEC_LOG_RECORD(eErrType,"file:" IEC_MQTTFILE_FILEPATH "read body yxcycle failed.");
    }
    mskprintf("m_lianwang:%s.\n",m_lianwang.g_gatewayAddr.c_str());
    mskprintf("m_lianwang:%d.\n",m_lianwang.g_gatewayPort);
    mskprintf("m_lianwang:%s.\n",m_lianwang.g_gatewayPaawd.c_str());
    mskprintf("m_lianwang:%s.\n",m_lianwang.g_workPath.c_str());
    mskprintf("m_lianwang:%s.\n",m_lianwang.g_keepAlivetime.c_str());
    
    mskprintf("canshuduquchenggong\n");




    

    return true;
}
bool CParamManager::ReadModelFile()
{
    CTaskManager::CreateInstance().m_dataObj.clear();
    std::list<CFileObj>::iterator iter = m_FileObjs.begin();
    for (; iter != m_FileObjs.end(); ++iter)
    {
        CFileObj obj = *iter;
        std::string filename = IEC_CFG_PATH + obj.m_file;
        CIIFile iiFile;
        iiFile.SetFileName(filename.c_str());
        int filesize = iiFile.GetLength();
        mskprintf("filesize == (%d).\n", filesize);
        if (filesize == 0) {
            IEC_LOG_RECORD(eErrType, "file read %s filesize = %d.", filename.c_str(),filesize);
            return false;
        }
        Jchar dataBuf[filesize] = { 0 };

        if (iiFile.Open("r+"))	//file exist
        {
            Jboolean bRead = iiFile.Read(dataBuf, filesize);
            if (!bRead) {
                IEC_LOG_RECORD(eErrType, "file read %s failed.", filename.c_str());
                return false;
            }
        }
        iiFile.Close();
        mskprintf("dataBuf == (%s).\n", dataBuf);
        neb::CJsonObject oJson;
        if (!oJson.Parse(dataBuf))
        {
            IEC_LOG_RECORD(eErrType, "json_loadb real_data.msg_send failed.");
            return false;
        }

        std::list<guid_body_s>::iterator it = obj.m_devs.begin();
        for (; it != obj.m_devs.end(); ++it)
        {
            guid_body_s guid_obj = *it;
            CDataObj *dataObj = NULL;
            dataObj = new CDataObj();
            oJson.Get("deviceType", dataObj->m_deviceType);
            oJson.Get("manufacturerId", dataObj->m_manufacturerId);
            oJson.Get("manufacturerName", dataObj->m_manufacturerName);
            oJson.Get("protocolType", dataObj->m_protocolType);
            oJson.Get("model", dataObj->m_model);
            
            dataObj->m_topic = obj.m_Topic + guid_obj.guid;
            dataObj->m_guid = guid_obj;
            mskprintf("ReadModelFile deviceType:%s.\n", dataObj->m_deviceType.c_str());
            mskprintf("ReadModelFile manufacturerId:%s.\n", dataObj->m_manufacturerId.c_str());
            mskprintf("ReadModelFile protocolType:%s.\n", dataObj->m_protocolType.c_str());
            mskprintf("ReadModelFile manufacturerName:%s.\n", dataObj->m_manufacturerName.c_str());
            mskprintf("ReadModelFile model:%s.\n", dataObj->m_model.c_str());
            mskprintf("ReadModelFile topic:%s.\n", dataObj->m_topic.c_str());
            mskprintf("ReadModelFile m_guid:%s.\n", guid_obj.guid.c_str());
            mskprintf("ReadModelFile dev:%s.\n", guid_obj.dev.c_str());

            neb::CJsonObject obj_services;
            if (!oJson.Get("services", obj_services)) {
                IEC_LOG_RECORD(eErrType, "file: %s read services object failed.", obj.m_file.c_str());
                return false;
            }

            if (!obj_services.IsArray()) {
                IEC_LOG_RECORD(eErrType, "file: %s read services object  object failed. not array.", obj.m_file.c_str());
                return false;
            }

            int servicesSize = obj_services.GetArraySize();
            for (int i = 0; i < servicesSize; i++)
            {
                std::string serviceId;
                neb::CJsonObject service_obj;
                if (obj_services.Get(i, service_obj)) {
                    service_obj.Get("serviceId", serviceId);
                    mskprintf("ReadModelFile serviceId:%s.\n", serviceId.c_str());
                    neb::CJsonObject obj_properties;
                    if (!service_obj.Get("properties", obj_properties)) {
                        IEC_LOG_RECORD(eErrType, "file: %s read properties object failed.", obj.m_file.c_str());
                        return false;
                    }

                    if (!obj_properties.IsArray()) {
                        IEC_LOG_RECORD(eErrType, "file: %s read properties object  object failed. not array.", obj.m_file.c_str());
                        return false;
                    }

                    int propertieSize = obj_properties.GetArraySize();
                    CNameObj name_obj;
                    CNameObj Sendname_obj;
                    name_obj.m_names.clear();
                    Juint32 j = 0;
                    for (int i = 0; i < propertieSize; i++)
                    {
                        std::string name;
                        neb::CJsonObject propertie_obj;
                        if (obj_properties.Get(i, propertie_obj)) {
                            propertie_obj.Get("name", name);
                            mskprintf("ReadModelFile name:%s.\n", name.c_str());
                            name_obj.m_names.push_back(name);
                            Sendname_obj.m_names.push_back(name);
                            if (j > m_sendNum) {
                                Sendname_obj.m_serviceName = serviceId;
                                dataObj->m_SendNameObjs.push_back(Sendname_obj);
                                Sendname_obj.m_names.clear();
                                j = 0;
                            } else if (i == (propertieSize - 1)) {
                                Sendname_obj.m_serviceName = serviceId;
                                dataObj->m_SendNameObjs.push_back(Sendname_obj);
                                Sendname_obj.m_names.clear();
                            }
                        }
                        j++;
                    }
                    name_obj.m_serviceName = serviceId;
                    dataObj->m_serviceIds[serviceId] = name_obj;
                }
            }
            CTaskManager::CreateInstance().m_dataObj.push_back(dataObj);
        }
    }    

    return true;
}

std::string CParamManager::GetFirsAppName(std::string topic)
{
    std::string str;
    size_t index = topic.find_first_of("/", 0);
    if (index != std::string::npos) {
        str = topic.substr(0, index);
    }
    return str;
}


std::string CParamManager::CreatMd5(std::string str)
{
    MD5_CTX md5;
    std::string out;
    unsigned char md[16] = { 0 };

    if (!MD5_Init(&md5)) {
        mskprintf("MD5_Init error\n");
        return "";
    }
    if (!MD5_Update(&md5, str.c_str(), strlen(str.c_str()))) {
        mskprintf("MD5_Update error\n");
        return "";
    }
    if (!MD5_Final(md, &md5))
    {
        mskprintf("MD5_Final error\n");
        return "";
    }
    for (int i = 0; i < 16; i++) {
        char outc[3] = { 0 };
        sprintf(outc,"%02X", md[i]);
        std::string outb = outc;
        out = out + outb;
    }
    return out;
}
CParamManager::CParamManager()
{
	m_Thread.SetPackageAcquireManager(this);
	m_T.SetParam(30* 1000); // 默认30秒
    m_T.StartCount();
    m_sendNum = 20;
}

CParamManager::~CParamManager()
{

}



