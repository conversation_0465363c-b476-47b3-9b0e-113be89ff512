

/*=====================================================================
 * �ļ���task_thread.h
 *
 * ������mqtt���ĺͷ����̴߳���
 *
 * ���ߣ�����			2021��9��27��10:28:12
 * 
 * �޸ļ�¼��
 =====================================================================*/


#ifndef TASK_THREAD_H
#define TASK_THREAD_H

#include "public_struct.h"
#include "pub_string.h"
#include "pub_thread.h"
#include "pub_logfile.h"
#include "queue_model.h"
#include "CJsonObject.h"

#ifdef __cplusplus
extern "C" {
#endif

class CNameObj      // ��һ���յ���������֮��ʼ����
{
public:
    CNameObj();
    virtual~CNameObj();

    //CNameObj& operator = (const CNameObj&);
    //CNameObj(const CNameObj&);
public:
    std::string              m_serviceName;
    std::list<std::string>   m_names;
    std::list<std::string>   m_Spontnames;
};


class CDataObj      // ��һ���յ���������֮��ʼ����
{
public:
    CDataObj();
    virtual~CDataObj();

public:
    std::string                                     m_topic;
    std::string                                     m_deviceType;
    std::string                                     m_manufacturerId;
    std::string                                     m_manufacturerName;
    std::string                                     m_model;
    std::string                                     m_protocolType;
    guid_body_s                                     m_guid;
    std::map<std::string, CNameObj>                 m_serviceIds;
    CNameObj                                        m_SpontObj;
    std::list<CNameObj>                             m_SendNameObjs;     // ��֡���͵�����
    SMTimeInfo                                      m_startTime;
    bool                                            m_startFlag;
    std::map<std::string, std::string>              m_RealDatas;        // ȫ������name value nameΨһ
};

//====================================================================================
//�߳�
//=====================================================================================
class CTaskManager;
class CTaskThread : public CSL_thread
{
public:
    CTaskThread();
    virtual~CTaskThread();
    void SetPackageAcquireManager(CTaskManager * p);

    virtual void run(void);
    virtual std::string ThreadName(void) const;
    virtual void RecordLog(const std::string & sMsg);

private:
    CTaskManager * m_pMng;
};

//=====================================================================================
//������
//=====================================================================================
    void HandleLoginSuccess(void* context, int messageId, int code, char *message);
    void HandleLoginFailure(void* context, int messageId, int code, char *message);
    void HandleConnectionLost(void* context, int messageId, int code, char *message);
    void HandleLogoutSuccess(void* context, int messageId, int code, char *messag);
    void HandleLogoutFailure(void* context, int messageId, int code, char *message);
    void HandleSubscribeSuccess(void* context, int messageId, int code, char *message);
    void HandleSubscribeFailure(void* context, int messageId, int code, char *message);
    void HandlePublishSuccess(void* context, int messageId, int code, char *message);
    void HandlePublishFailure(void* context, int messageId, int code, char *message);
    void HandleCommandArrived(void* context, int messageId, int code, char *message);
    void HandleSubDeviceAddResult(void* context, int messageId, int code, char *message);
    void HandleSubDeviceDeleteResult(void* context, int messageId, int code, char *message);
    void HandleSubDeviceUpdateResult(void* context, int messageId, int code, char *message);
    void HandleDeviceQueryResult(void* context, int messageId, int code, char *message);

class CTaskManager
{
public:
    static CTaskManager & CreateInstance();

    bool Init(void);
    void Exit(void);

    void thread_prev(void);
    void thread_func(void);
    void thread_exit(void);

    void OnRun(void);
    void Start(void);
    void Stop(void);

    
    void Test_GW_CustomTopic_Rerort(char *deviceId);
    void Test_SubDev_Add(void);

    bool UnpackData(mqtt_data_info_s &real_data);
    bool UnpackRealData(CDataObj *dobj,neb::CJsonObject obj);
    std::string CreatMd5(std::string str);
    uint8_t login_flag = 0;
    
    CTaskManager();
    ~CTaskManager();

    CTaskManager& operator = (const CTaskManager&);
    CTaskManager(const CTaskManager&);
private:

    std::string UnpackToken(neb::CJsonObject obj);                           // ��ȡTokenֵ
    void MqttPackEsn();
    // �������ݡ�
    void Unpacklaunch(neb::CJsonObject obj);
    void Unpackdataup(neb::CJsonObject obj);
    void Unpaquery(neb::CJsonObject obj);
    void Unpacoamdresponse(neb::CJsonObject obj);
    void Unpadatas(neb::CJsonObject obj);
    void MqttPackOnline(CDataObj* obj);
    void MqttPackData(CDataObj* obj);
    void MqttPackSpontData(CDataObj * obj);
    bool IsFindDiscrete(CDataObj * obj, std::string name, std::string & serveiceName);
private:
    // ��ȡ�ַ��� 
    std::string GetFirsAppName(std::string topic);
    std::string GetLastSegment(std::string topic);
    std::string GetAnyIndexName(std::string topic,int met_id);
    bool GetStringVlaue(neb::CJsonObject obj,const char* key,C64String &str);
public:
    std::list<CDataObj*>                     m_dataObj;
private:
    typedef enum
    {
        OPS_NULL,
        OPS_INIT,
        OPS_INIT_OK,
        OPS_REG,
        OPS_REG_OK,
        OPS_IDLE,
    }machine_ops;
    machine_ops     n_ops;
    Juint32                                 m_second; //�ϴε���
    CEPTime                              m_baseTime; //��׼ʱ��
    CIIMutex	                   		 	m_cs;               //  ����
    CTaskThread           		            m_Thread;           //  �߳�     
    std::string                             m_esn;
    CTimerCnt								m_T;				// ��ʱ�� ����
    CTimerCnt								m_Data_T;				// ��ʱ�� ����
    CTimerCnt								m_ESN_T;	
    CTimerCnt								m_AC_T;			
    friend class CTaskThread;
   
    
   
    
};

#ifdef __cplusplus
}
#endif

#endif
