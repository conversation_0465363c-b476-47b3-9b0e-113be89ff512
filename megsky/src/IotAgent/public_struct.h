/*=====================================================================
 * 文件：public_struct.h
 *
 * 描述：公共定义的头文件定义
 *
 * 作者：田振超			2021年5月14日13:54:32
 * 
 * 修改记录：
 =====================================================================*/

#ifndef PUBLIC_STRUCT_H
#define PUBLIC_STRUCT_H

#include <pthread.h>
#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <string.h>
#include <time.h>
#include <sys/time.h>
#include <signal.h>
#include <semaphore.h>
#include <sys/ipc.h>
#include <sys/msg.h>
#include "pub_std.h"
#include <errno.h>

#ifdef __cplusplus
extern "C" {
#endif

/* MQTT订阅发布相关 */
#define QOS         0
#define TIMEOUT     10000L
#define MSG_ARRVD_MAX_LEN   1024*1024
#define PATH_MAX 1024

const Juint32	QUEUE_LEN = 8192;

#define TCP_CLIENT_T 0
#define TCP_SERVER_T 1

void printf_enable(bool b);
bool get_printf_enable();
void mskprintf(const char * format, ...);
int memcpy_safe(void *s1, int s1_size, void *s2, int s2_size, int n);
void LogWrite(ELogType eLogType, const char * format, ...);

#define CFG_USING_LOG
#define CFG_USING_LOG_COLOR

#ifdef CFG_USING_LOG

#ifdef CFG_USING_LOG_COLOR
#define MG_DEBUG(fmt, ...) mskprintf(fmt, ##__VA_ARGS__)
#define MG_PRINT(fmt, ...) mskprintf(fmt, ##__VA_ARGS__)
#define MG_LOG(fmt, ...) mskprintf(fmt " \n", ##__VA_ARGS__)
#define MG_LOG_I(fmt, ...) mskprintf("\033[32;40m" fmt "\033[0m \n", ##__VA_ARGS__)
#define MG_LOG_D(fmt, ...) mskprintf("\033[33;40m" fmt "\033[0m \n", ##__VA_ARGS__)
#define MG_LOG_E(fmt, ...) mskprintf("\033[31;40m" fmt "\033[0m \n", ##__VA_ARGS__)
#else
#define MG_DEBUG(fmt, ...) mskprintf(fmt, ##__VA_ARGS__)
#define MG_PRINT(fmt, ...) mskprintf(fmt, ##__VA_ARGS__)
#define MG_LOG(fmt, ...) mskprintf(fmt " \n", ##__VA_ARGS__)
#define MG_LOG_I(fmt, ...) mskprintf(fmt " \n", ##__VA_ARGS__)
#define MG_LOG_D(fmt, ...) mskprintf(fmt " \n", ##__VA_ARGS__)
#define MG_LOG_E(fmt, ...) mskprintf(fmt " \n", ##__VA_ARGS__)
#endif

#else
#define MG_DEBUG(fmt, ...)
#define MG_PRINT(fmt, ...)
#define MG_LOG(fmt, ...)
#define MG_LOG_I(fmt, ...)
#define MG_LOG_D(fmt, ...)
#define MG_LOG_E(fmt, ...)

#endif

//宏实现(对象类名)
#define IEC_LOG_RECORD(logtype, format, arg...) LogWrite(logtype,format, ##arg);

#define IEC_ASSERT(EXPR)                                                   \
    if (!(EXPR))                                                           \
    {                                                                      \
        IEC_LOG_RECORD(eErrType, "(%s) has assert failed at %s,line:%d\n", \
                       #EXPR, __FILE__, __LINE__);                         \
        return;                                                            \
    }
/* 基本数据类型和常量定义 */
#define TOKEN_RELEASE 0
#define TOKEN_SHUT 1


#define MQTT_OK 0
#define MQTT_ERR 1
#define VOS_OK 0
#define VOS_ERR 1
#define DEVICE_DELETE       2
#define DEVICE_ONLINE       1
#define DEVICE_OFFLINE      0

#define DEV_INFO_MSG_BUFFER_LEN 1024
#define DATA_BUF_F256_SIZE 256
#define SG_KEEP_ALIVE_INTERVAL (60)
#define MQTT_DISCONNECT_TIMEOUT 10000
//#define FILE_FRAME_SIZE 2048

#define LOG_FILE_SIZE 5 //单位MB

#define DATACENTREVERSION "2.1"
#define COMPATIBLEVERSION "1.0"
// 打印控制开关
#define IEC_PRINT_FILEPATH "/mnt/external_storage/enable.print"
//#define IEC_PRINT_FILEPATH "/root/enable.print"

#define IEC_CFG_PATH    "/mnt/internal_storage/MskIot/"
#define IEC_EXCFG_PATH    "/mnt/external_storage/MskIot/"
//#define IEC_CFG_PATH "/etc/custom/


#define IEC_MQTT_FILENAME "mqttIot_config.json"
#define IEC_TASKRECORD_FILENAME "taskRecord.json"

/* 配置文件路径 */

#define IEC_MQTTFILE_FILEPATH IEC_CFG_PATH IEC_MQTT_FILENAME
#define IEC_TASKRECORD_FILEPATH IEC_CFG_PATH IEC_TASKRECORD_FILENAME

//#define DATABASE_PATH "/mnt/nand/model.db"
#define DATABASE_PATH IEC_EXCFG_PATH "model.db"
/* APP日志存储路径 */
//#define IEC_LOG_PATH "/root/mskIEC104.log"
#define IEC_LOG_PATH IEC_EXCFG_PATH "MskIot.log"

#define IEC_HISTORY_PATH "/mnt/external_storage/"

/* 此APP名称 */
#define CFG_APP_NAME "MskIot"

/* 数据中心APP名称 */
#define CFG_DATA_CENTER_NAME "database"

/* 设备重启延时时间，表示在多少秒之后重启，取值范围1~60s */
#define CFG_RST_DELAY 1

#define FLAG_NULL   0
#define FLAG_FAULT  1
#define FLAG_RECOVER  2

#define QUERY_PARAM   0
#define QUERY_DD  1
#define QUERY_YCYXOCT  2

#define EVENT_MORMAL   0
#define EVENT_NULL   1

#define REGISTER   0
#define UNREGISTER   1

/*Start================MQTT定义=======================*/

/* MQTT消息通用部分结构体 */
typedef struct mqtt_header
{
    char        token[40];
    char        timestamp[32];
}mqtt_header_s;

typedef struct mqtt_data_info
{
    Juint32 msg_send_lenth;
	char msg_send[MSG_ARRVD_MAX_LEN];
    char pubtopic[256];
    int  retained;                  // 可保留的
}mqtt_data_info_s;

/*End================设备信息定义=======================*/

#define F_DESC(x) 1
#define SEM_MAX_WAIT_TIMES 20
#define MAX_QUE_DEPTH 4096
#define MSECOND 1000000
#define TEN_MSECOND 100000
#define REQUEST_TYPE 77
#define JSON_BUF_SIZE 256

typedef unsigned char method_t; //方法类型

//请求报文头
typedef struct mqtt_request_header
{
	Jint32              mid;
    std::string         deviceId;        
    std::string         timestamp;        
    Jint32              expire;        
    std::string         type;   
    /* param */         
}mqtt_request_header_s;

//应答报文头
typedef struct mqtt_reply_header
{
	Jint32              mid;
    std::string         deviceId;        
    std::string         timestamp;       
    std::string         type;  
    /* param */
    Juint16             code;
    std::string         msg;
}mqtt_reply_header_s;

// 模型内容
typedef struct model_content
{
    Juint32             id;
    std::string         name;
    std::string         type;
    std::string         unit;
    std::string         deadzone;
    std::string         ratio;
    std::string         isReport;
    std::string         userdefine;
}model_content_s;

// 设备内容
typedef struct dev_content
{
    Juint32             id;
    std::string         model;  // 拼接
    std::string         port;  // 拼接
    std::string         addr;  // 拼接
    std::string         desc;  // 拼接
    std::string         manuID;
    std::string         manuName;
    std::string         ProType;
    std::string         deviceType;
    std::string         isReport;
    std::string         nodeID;
    std::string         productID;
    std::string         guid;       //拼接产生guid
}dev_content_s;

// 参数内容
typedef struct dev_param
{
    Juint32             id;
    std::string         name;
    std::string         val;
    std::string         unit;
    std::string         datatype;
}dev_param_s;

 typedef struct equipment_Add
    {
       std::string          manufacturerId;
       std::string          manufacturerName;
       std::string          deviceType;
       std::string          model;
       std::string          protocolType;
       std::string          state;
       std::string          event_time;
       std::string          devName ;
       std::string          devDesc;
       std::string          subName ;

    }equipment_Add_s;
    typedef struct data_Report
    {
       std::string          serviceId;
       std::string          serviceProperties;
       
    }data_Report_s;

    typedef struct command_issued
    {
       std::string          msgType;
       Juint32              mid;
       std::string          cmd;
       std::string          paras;
       std::string          serviceId;
       std::string          deviceId;
       
    }command_issued_s;
    typedef struct Registration_Information
    {
        std::string        dev;
        std::string        Deviceid;
        std::string        nodid;
    }Registration_Information_s;

typedef struct topic_cunchu
    {
        std::string        name;
        std::string        dev_topic;
    }topic_cunchu_s;

typedef struct enroll_type
    {
        std::string         iot_nodid;
        std::string        manuid;
        std::string        model;
        std::string        dev;
    }enroll_type_s;
    typedef struct nodID_jishu
    {
        std::string        model;
        std::string        dev;
        std::string        iot_nodid;
    }nodID_jishu_s;
// GUID内容
typedef struct guid_body
{
    std::string         dev;
    std::string         guid;
}guid_body_s;
typedef struct data_storage
{
    std::string         name;
    std::string         val;
}data_storage_s;
typedef struct gateway_connect
    {
        std::string     g_gatewayAddr    ;
        int     g_gatewayPort     ;
        std::string     g_gatewayDeviceId ; 
        std::string     g_gatewayPaawd  ;
        std::string     g_workPath  ;
        std::string     g_keepAlivetime ; /* Set the heartbeat interval to 120 seconds */
        std::string     g_Connectionmethod    ;
        std::string     destIp    ;
        std::string     netMask     ;
        std::string     gateway ; 
        std::string     interface  ;
    } gateway_connect_s;

int folder_mkdirs(const char *folder_path);


#ifdef __cplusplus
}
#endif

#endif

