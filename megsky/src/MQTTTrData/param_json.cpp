#include <openssl/md5.h>
#include "mqtt_pub_inter.h"
#include "task_thread.h"
#include "param_json.h"

CFileObj::CFileObj(void)
{
}

CFileObj::~CFileObj()
{
}

DeviceObj::DeviceObj(void)
{
}
DeviceObj::~DeviceObj(void)
{
}

CParamThread::CParamThread(void)
    : m_pMng(NULL)
{
}

CParamThread::~CParamThread()
{
}

void CParamThread::SetPackageAcquireManager(CParamManager *p)
{
    m_pMng = p;
}

void CParamThread::run(void)
{
    m_pMng->thread_prev();
    while (CSL_thread::IsEnableRun())
    {
        m_pMng->thread_func();
    }
    m_pMng->thread_exit();
}

std::string CParamThread::ThreadName(void) const
{
    return std::string("MQTT 内部Broker线程");
}

void CParamThread::RecordLog(const std::string &sMsg)
{
}

//***************************************************************
// 报文获取管理�???
//***************************************************************
CParamManager &CParamManager::CreateInstance()
{
    static CParamManager Mng;
    return Mng;
}

bool CParamManager::GetDataCenterParam(std::string &devData)
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        mskprintf("\nJson head fill failed---------.\n");
        return false;
    }
    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.AddEmptySubArray("body");

    neb::CJsonObject bodyArray;
    bodyArray.Add("dev", devData);
    bodyArray.Add("totalcall", "0");
    bodyArray.AddEmptySubArray("body");

    auto paramData = m_Param_Config["param"];
    auto iter = paramData.begin();
    for (; iter != paramData.end(); iter++)
    {
        bodyArray["body"].Add(iter->m_Name);
    }
    mskprintf("bodyarray----------%s\n", bodyArray.ToString().c_str());
    oJson["body"].Add(bodyArray);

    std::string mssm = oJson.ToString();
    C256String pub_topic;
    pub_topic.Format("MQTTTrData/dataCenter/JSON/get/request/parameter");
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
        item.msg_send_lenth = mssm.size();
        item.retained = 1;
        if (!CMqttClientInterManager::CreateInstance().agent_MqttMsgPub(item.msg_send, item.pubtopic, item.retained))
        {
            return false;
        }
    }
    mskprintf("mssm:%s.\n", mssm.c_str());
    mskprintf("pub_topic:%s.\n", pub_topic.ToChar());

    return true;
}

bool CParamManager::SetDataCenterParam(mqtt_data_info_s &recvData, std::string &devData)
{
    neb::CJsonObject obj;
    obj.Parse(recvData.msg_send);
    neb::CJsonObject obj_body;
    if (!obj.Get("body", obj_body))
    {
        IEC_LOG_RECORD(eErrType, "UnpackDevParameterData body = NULL.");
        return false;
    }
    if (!obj_body.IsArray())
    {
        IEC_LOG_RECORD(eErrType, "UnpackDevParameterData body is not Array.");
        return false;
    }

    std::map<std::string, float> paramMap;
    int asize = obj_body.GetArraySize();
    for (int i = 0; i < asize; i++)
    {
        neb::CJsonObject obj_data;
        if (!obj_body.Get(i, obj_data))
        {
            mskprintf("UnpackDevParameterData body data get error i = %d.\n", i);
            return false;
        }
        std::string devName;
        obj_data.Get("dev", devName);

        mskprintf("modelname = %s\n", devName.c_str());

        neb::CJsonObject obj_model_data;
        obj_data.Get("body", obj_model_data);
        int bsize = obj_model_data.GetArraySize();
        for (int j = 0; j < bsize; j++)
        {
            neb::CJsonObject obj_value;
            if (!obj_model_data.Get(j, obj_value))
            {
                mskprintf("UnpackDevParameterData body data get error j = %d.\n", j);
                IEC_LOG_RECORD(eErrType, "UnpackDevParameterData body data get error j = %d.\n", j);
                return false;
            }

            std::string valuename;
            obj_value.Get("name", valuename);
            std::string value;
            obj_value.Get("val", value);
            mskprintf("valuename = %s, value =%s\n", valuename.c_str(), value.c_str());

            // �����жϴ˲����Ƿ���m_Param_value�����У����ڸ���
            auto &paramData = m_Param_Config["param"];
            for (auto &param : paramData)
            {
                if (param.m_Name.compare(valuename) == 0)
                {
                    param.m_Value = value;
                }
            }
            paramMap[valuename] = std::stof(value.c_str());
        }
    }
    // �����������õ���������
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        mskprintf("\nJson head fill failed---------.\n");
        return false;
    }
    neb::CJsonObject sendObj;
    sendObj.Add("token", jsheader.token);
    sendObj.Add("timestamp", jsheader.timestamp);
    sendObj.AddEmptySubArray("body");
    neb::CJsonObject arrayObj;
    arrayObj.Add("dev", devData);
    arrayObj.AddEmptySubArray("body");

    auto m_Param_Info = m_Param_Config["param"];
    for (auto param : m_Param_Info)
    {
        auto iter = paramMap.find(param.m_Name);
        if (iter != paramMap.end())
        {
            continue;
        }
        else
        {
            neb::CJsonObject object;
            object.Add("name", param.m_Name);
            object.Add("unit", param.m_Unit);
            object.Add("datatype", param.m_DataType);
            object.Add("val", param.m_Value);
            arrayObj["body"].Add(object);
        }
    }
    if (arrayObj["body"].GetArraySize() == 0)
    {
        mskprintf("updata param size 0\n");
        return true;
    }
    sendObj["body"].Add(arrayObj);

    std::string mssm = sendObj.ToString();
    C256String pub_topic;
    pub_topic.Format("MQTTTrData/dataCenter/JSON/set/request/parameter");
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
        item.msg_send_lenth = mssm.size();
        item.retained = 1;
        if (!CMqttClientInterManager::CreateInstance().agent_MqttMsgPub(item.msg_send, item.pubtopic, item.retained))
        {
            return false;
        }
    }
    mskprintf("mssm:%s.\n", mssm.c_str());
    mskprintf("pub_topic:%s.\n", pub_topic.ToChar());
    return true;
}

bool CParamManager::Init(void)
{
    bool bRet(false);
    m_FileObjs.clear();
    if (!ReadJsonFile())
    {
        return false;
    }

    bRet = m_Thread.start();
    return bRet;
}

void CParamManager::Exit(void)
{

    m_Thread.close();
    m_Thread.wait_for_end();
}

void CParamManager::thread_prev(void)
{
}

void CParamManager::thread_func(void)
{
    OnRun();
}

void CParamManager::thread_exit(void)
{
}
void CParamManager::OnRun(void)
{
    CMqttClientInterManager::CreateInstance().get_Device_enrollment();
    mskprintf("askljdfsjdfoiajfsajdfafjksjdlf;kasldjf\n\n");

    ii_sleep(5000);
}

void CParamManager::Start(void)
{
}

void CParamManager::Stop(void)
{
}
void copyFile(const char *srcPath, const char *destPath)
{
    FILE *srcFile = fopen(srcPath, "rb");
    if (srcFile == NULL)
    {
        mskprintf("Source file not found.\n");
        return;
    }

    FILE *destFile = fopen(destPath, "wb");
    if (destFile == NULL)
    {
        mskprintf("Failed to create destination file.\n");
        fclose(srcFile);
        return;
    }

    char buffer[1024];
    size_t bytesRead;

    while ((bytesRead = fread(buffer, 1, sizeof(buffer), srcFile)) > 0)
    {
        fwrite(buffer, 1, bytesRead, destFile);
    }

    fclose(srcFile);
    fclose(destFile);
    printf("File copied successfully.\n");
}
#define IEC_PARAMS_FILEPATH "/data/app/MQTTTrData/configFile/MQTTTrDataparamfile.json"
#define CONFIGFILE_MOREN "/usr/local/extapps/MQTTTrData/configFile/MQTTTrDataparamfile.json"

template <typename T>
bool SetConstantValue(T const &paramVet)
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        mskprintf("\nJson head fill failed---------.\n");
        return false;
    }
    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);

    neb::CJsonObject bodyArray;
    // �˴�dev����Ϊɶ
    bodyArray.Add("dev", "MCCB_guid");
    neb::CJsonObject paraObject;

    mskprintf("vector*******************%d*****************8\n", paramVet.size());
    auto it = paramVet.begin();
    for (; it != paramVet.end(); it++)
    {
        neb::CJsonObject object;
        object.Add("name", it->m_Name);
        object.Add("val", it->m_Value);
        object.Add("unit", it->m_Unit);
        object.Add("detatype", it->m_DataType);
        paraObject.Add(object);
    }
    bodyArray.Add("body", paraObject);

    oJson.Add("body", bodyArray);

    std::string mssm = oJson.ToString();
    C256String pub_topic;
    pub_topic.Format("MQTTTrData/dataCenter/JSON/set/request/parameter");
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
        item.msg_send_lenth = mssm.size();
        item.retained = 1;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
    mskprintf("mssm:%s.\n", mssm.c_str());
    mskprintf("pub_topic:%s.\n", pub_topic);
    return true;
}

template <typename T>
bool ParseConfig(T &paramVet, neb::CJsonObject &obj)
{
    if (!obj.IsArray())
    {
        return false;
    }
    int arrarySize = obj.GetArraySize();

    for (int i = 0; i != arrarySize; i++)
    {
        neb::CJsonObject object;
        obj.Get(i, object);
        constant_Value value;
        object.Get("name", value.m_Name);
        mskprintf("name---------------%s\n", value.m_Name.c_str());
        object.Get("type", value.m_DataType);
        mskprintf("name---------------%s\n", value.m_DataType.c_str());
        object.Get("unit", value.m_Unit);
        mskprintf("name---------------%s\n", value.m_Unit.c_str());
        object.Get("value", value.m_Value);
        mskprintf("name---------------%s\n", value.m_Value.c_str());
        paramVet.emplace_back(std::move(value));
    }
    return true;
}

std::map<std::string, std::string> CParamManager::ActivateParamConfig(neb::CJsonObject param, std::string dev)
{
    std::vector<Constant_Value> constantValue;
    auto paramIter = m_Param_Config_Back.find(dev);
    if (paramIter == m_Param_Config_Back.end())
    {
        return std::map<std::string, std::string>();
    }
    else
    {
        constantValue = paramIter->second;
    }

    neb::CJsonObject data_json;
    std::string parameter_name;
    std::string parameter_value;
    std::string shaketimename;
    int shaketimevalue = 0;
    data_json.Add("dev", dev);
    data_json.AddEmptySubArray("body");
    auto iter1 = constantValue.begin();
    for (; iter1 != constantValue.end(); iter1++)
    {
        mskprintf("name -----------%s", iter1->m_Name.c_str());
        mskprintf("val -----------%s", iter1->m_Value.c_str());
    }

    while (param.GetKey(parameter_name))
    {
        param.Get(parameter_name, parameter_value);
        neb::CJsonObject data_body_json;
        auto iter = constantValue.begin();
        for (; iter != constantValue.end(); iter++)
        {
            if (iter->m_Name.compare(parameter_name) == 0 && iter->m_Value.compare(parameter_value) == 0)
            {
                if (parameter_name.compare("IndScanTm") == 0)
                {
                    shaketimename = "shaketime";
                    shaketimevalue = std::stoi(parameter_value);
                }
                data_body_json.Add("name", parameter_name);
                data_body_json.Add("val", parameter_value);
                data_body_json.Add("unit", iter->m_Unit);
                data_body_json.Add("datatype", iter->m_DataType);
                mskprintf("name -----------%s\n", parameter_name.c_str());
                mskprintf("val -----------%s\n", parameter_value.c_str());
                data_json["body"].Add(data_body_json);
                break;
            }
        }
        if (iter == constantValue.end())
        {
            mskprintf("name -----------%s\n", parameter_name.c_str());
            mskprintf("val -----------%s\n", parameter_value.c_str());
            return std::map<std::string, std::string>();
        }
    }
    neb::CJsonObject arraryObject;
    data_json.Get("body", arraryObject);
    if ((size_t)arraryObject.GetArraySize() != constantValue.size())
    {
        return std::map<std::string, std::string>();
    }
    std::string topic_id;
    // char *data = NULL;
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        mskprintf("\nJson head fill failed---------.\n");
        return std::map<std::string, std::string>();
    }

    neb::CJsonObject send_Json;
    send_Json.Add("token", jsheader.token);
    send_Json.Add("timestamp", jsheader.timestamp);
    send_Json.AddEmptySubArray("body");
    send_Json["body"].Add(data_json);

    topic_id = dev;

    std::map<std::string, std::string> mssm;
    mssm["noshaketime"] = send_Json.ToString();
    if (!shaketimename.empty())
    {
        neb::CJsonObject shaketime_Json;
        shaketime_Json.Add("token", jsheader.token);
        shaketime_Json.Add("timestamp", jsheader.timestamp);
        shaketime_Json.Add(shaketimename, shaketimevalue);
        mssm["shaketime"] = shaketime_Json.ToString();
    }
    m_Param_Config_Back.erase(dev);

    return mssm;
}

void CParamManager::SetParamConfig(neb::CJsonObject const &param, std::string const &dev)
{
    neb::CJsonObject obj;
    for (int i = 0; i < param.GetArraySize(); i++)
    {
        param.Get(i, obj);
        std::string conVal;
        if (obj.Get("name", conVal))
        {
            auto &paramVct = m_Param_Config["param"];
            for (auto &param : paramVct)
            {
                if (param.m_Name.compare(conVal) == 0)
                {
                    obj.Get("val", param.m_Value);
                    mskprintf("name--------%s\n", conVal.c_str());
                    mskprintf("val--------%s\n", param.m_Value.c_str());
                }
            }
        }
    }

    for (auto paramVct : m_Param_Config["param"])
    {
        mskprintf("name----------%s\n", paramVct.m_Name.c_str());
        mskprintf("val----------%s\n", paramVct.m_Value.c_str());
    }
}

// 备份set的数�??到m_Param_Config_Back,并判�??单个的数�??�??否已经存在back�??
void CParamManager::SetBackParamConfig(neb::CJsonObject &param, std::string const &dev)
{
    std::vector<Constant_Value> constantValue;

    m_Param_Config_Back.clear();
    std::string key;
    while (param.GetKey(key))
    {
        constant_Value dataty = GetParamConfig("trparam", key);
        if (dataty.m_Name.empty())
        {
            continue;
        }
        else
        {
            param.Get(key, dataty.m_Value);
            mskprintf("val----------------------%s\n", dataty.m_Value.c_str());
            constantValue.emplace_back(dataty);
        }
    }
    m_Param_Config_Back[dev] = constantValue;
    return;
}

bool CParamManager::ReadJsonFile()
{
    FILE *file = fopen(IEC_PARAMS_FILEPATH, "rb");
    if (file == NULL)
    {
        mskprintf("MQTTTrDataparamfile.json not found, copying default config file...\n");
        copyFile(CONFIGFILE_MOREN, IEC_PARAMS_FILEPATH);
    }
    else
    {
        fclose(file);
        mskprintf("MQTTTrDataparamfile.json found..\n");
    }

    CIIFile iiFile("/data/app/MQTTTrData/configFile/MQTTTrDataparamfile.json");
    // CIIFile iiFile(IEC_MQTTFILE_FILEPATH);
    int filesize = iiFile.GetLength();
    mskprintf("filesize == (%d).\n", filesize);
    if (filesize == 0)
    {
        return false;
    }
    Jchar dataBuf[filesize] = {0};

    if (iiFile.Open("r+")) // file exist
    {
        Jboolean bRead = iiFile.Read(dataBuf, filesize);
        if (!bRead)
        {
            IEC_LOG_RECORD(eErrType, "file read" IEC_MQTTFILE_FILEPATH "failed.");
            return false;
        }
    }
    iiFile.Close();

    mskprintf("dataBuf == (%s).\n", dataBuf);
    neb::CJsonObject oJson;
    if (!oJson.Parse(dataBuf))
    {
        IEC_LOG_RECORD(eErrType, "json_loadb real_data.msg_send failed.");
        return false;
    }

    if (!oJson.Get("sendNum", m_sendNum))
    {
        IEC_LOG_RECORD(eErrType, "file:" IEC_MQTTFILE_FILEPATH "read body sendNum failed.");
    }

    if (!oJson.Get("yxcycle", m_yxcycle))
    {
        IEC_LOG_RECORD(eErrType, "file:" IEC_MQTTFILE_FILEPATH "read body yxcycle failed.");
    }

    std::string dev_str;
    neb::CJsonObject obj_body;
    if (!oJson.Get("body", obj_body))
    {
        IEC_LOG_RECORD(eErrType, "file:" IEC_MQTTFILE_FILEPATH "read body object failed.");
        return false;
    }

    if (!obj_body.IsArray())
    {
        IEC_LOG_RECORD(eErrType, "file:" IEC_MQTTFILE_FILEPATH "read body object failed. not array.");
        return false;
    }

    int asize = obj_body.GetArraySize();
    for (int i = 0; i < asize; i++)
    {
        CFileObj fobj;
        neb::CJsonObject obj;
        if (obj_body.Get(i, obj))
        {
            obj.Get("model", fobj.m_model);
            obj.Get("file", fobj.m_file);
            obj.Get("Topic", fobj.m_Topic);
            mskprintf("Topic:%s.\n", fobj.m_Topic.c_str());
            mskprintf("model:%s.\n", fobj.m_model.c_str());
            mskprintf("file:%s.\n", fobj.m_file.c_str());
            neb::CJsonObject data_obj;
            if (obj.Get("data", data_obj))
            {
                if (!data_obj.IsArray())
                {
                    IEC_LOG_RECORD(eErrType, "file:" IEC_MQTTFILE_FILEPATH "read data object failed. not array.");
                    return false;
                }
                int dsize = data_obj.GetArraySize();
                for (int i = 0; i < dsize; i++)
                {
                    guid_body_s guid;
                    neb::CJsonObject dsubobj;
                    if (data_obj.Get(i, dsubobj))
                    {
                        dsubobj.Get("dev", guid.dev);
                        dsubobj.Get("guid", guid.guid);
                        mskprintf("dev:%s.\n", guid.dev.c_str());
                        mskprintf("guid:%s.\n", guid.guid.c_str());
                        fobj.m_devs.push_back(guid);
                    }
                }
            }
        }
        else
        {
            IEC_LOG_RECORD(eErrType, "file:" IEC_MQTTFILE_FILEPATH "read body sub_object failed. not array.");
            return false;
        }

        neb::CJsonObject dataPara;
        if (!oJson.Get("data", dataPara))
        {
            mskprintf("parase parament data fail\n");
        }
        else
        {
            if (!dataPara.IsArray())
            {
                mskprintf("param json fail\n");
            }
            else
            {
                int arraySize = dataPara.GetArraySize();
                for (int cont = 0; cont < arraySize; cont++)
                {
                    neb::CJsonObject object;
                    dataPara.Get(cont, object);
                    std::string model;
                    object.Get("model", model);
                    if (model.compare("param") == 0)
                    {
                        neb::CJsonObject paramConfig;
                        object.Get("param_config", paramConfig);
                        std::vector<constant_Value> constantVct;
                        ParseConfig(constantVct, paramConfig);
                        m_Param_Config["param"] = constantVct;
                        mskprintf("map size *************%d\n", m_Param_Config.size());
                        for (auto param : m_Param_Config["param"])
                        {
                            if (param.m_Name.compare("report_cycle") == 0)
                            {
                                m_sendPeriod = std::stoi(param.m_Value.c_str());
                                break;
                            }
                        }
                    }
                    else if (model.compare("trparam") == 0)
                    {
                        neb::CJsonObject paramConfig;
                        object.Get("param_info", paramConfig);
                        std::vector<constant_Value> constantVct;
                        ParseConfig(constantVct, paramConfig);
                        m_Param_Config["trparam"] = constantVct;
                    }
                }
            }
        }

        m_FileObjs.push_back(fobj);
    }

    return true;
}
bool CParamManager::ReadModelFile()
{
    CTaskManager::CreateInstance().m_dataObj.clear();
    std::list<CFileObj>::iterator iter = m_FileObjs.begin();
    for (; iter != m_FileObjs.end(); ++iter)
    {
        CFileObj obj = *iter;
        std::string filename = IEC_CFG_PATH + obj.m_file;
        CIIFile iiFile;
        iiFile.SetFileName(filename.c_str());
        int filesize = iiFile.GetLength();
        mskprintf("filesize == (%d).\n", filesize);
        if (filesize == 0)
        {
            IEC_LOG_RECORD(eErrType, "file read %s filesize = %d.", filename.c_str(), filesize);
            return false;
        }
        Jchar dataBuf[filesize] = {0};

        if (iiFile.Open("r+")) // file exist
        {
            Jboolean bRead = iiFile.Read(dataBuf, filesize);
            if (!bRead)
            {
                IEC_LOG_RECORD(eErrType, "file read %s failed.", filename.c_str());
                return false;
            }
        }
        iiFile.Close();
        mskprintf("dataBuf == (%s).\n", dataBuf);
        neb::CJsonObject oJson;
        if (!oJson.Parse(dataBuf))
        {
            IEC_LOG_RECORD(eErrType, "json_loadb real_data.msg_send failed.");
            return false;
        }

        std::list<guid_body_s>::iterator it = obj.m_devs.begin();
        for (; it != obj.m_devs.end(); ++it)
        {
            guid_body_s guid_obj = *it;
            CDataObj *dataObj = NULL;
            dataObj = new CDataObj();
            oJson.Get("deviceType", dataObj->m_deviceType);
            oJson.Get("manufacturerId", dataObj->m_manufacturerId);
            oJson.Get("manufacturerName", dataObj->m_manufacturerName);
            oJson.Get("protocolType", dataObj->m_protocolType);
            oJson.Get("model", dataObj->m_model);

            dataObj->m_topic = obj.m_Topic + guid_obj.guid;
            dataObj->m_guid = guid_obj;
            mskprintf("ReadModelFile deviceType:%s.\n", dataObj->m_deviceType.c_str());
            mskprintf("ReadModelFile manufacturerId:%s.\n", dataObj->m_manufacturerId.c_str());
            mskprintf("ReadModelFile protocolType:%s.\n", dataObj->m_protocolType.c_str());
            mskprintf("ReadModelFile manufacturerName:%s.\n", dataObj->m_manufacturerName.c_str());
            mskprintf("ReadModelFile model:%s.\n", dataObj->m_model.c_str());
            mskprintf("ReadModelFile topic:%s.\n", dataObj->m_topic.c_str());
            mskprintf("ReadModelFile m_guid:%s.\n", guid_obj.guid.c_str());
            mskprintf("ReadModelFile dev:%s.\n", guid_obj.dev.c_str());

            neb::CJsonObject obj_services;
            if (!oJson.Get("services", obj_services))
            {
                IEC_LOG_RECORD(eErrType, "file: %s read services object failed.", obj.m_file.c_str());
                return false;
            }

            if (!obj_services.IsArray())
            {
                IEC_LOG_RECORD(eErrType, "file: %s read services object  object failed. not array.", obj.m_file.c_str());
                return false;
            }

            int servicesSize = obj_services.GetArraySize();
            for (int i = 0; i < servicesSize; i++)
            {
                std::string serviceId;
                neb::CJsonObject service_obj;
                if (obj_services.Get(i, service_obj))
                {
                    service_obj.Get("serviceId", serviceId);
                    mskprintf("ReadModelFile serviceId:%s.\n", serviceId.c_str());
                    neb::CJsonObject obj_properties;
                    if (!service_obj.Get("properties", obj_properties))
                    {
                        IEC_LOG_RECORD(eErrType, "file: %s read properties object failed.", obj.m_file.c_str());
                        return false;
                    }

                    if (!obj_properties.IsArray())
                    {
                        IEC_LOG_RECORD(eErrType, "file: %s read properties object  object failed. not array.", obj.m_file.c_str());
                        return false;
                    }

                    int propertieSize = obj_properties.GetArraySize();
                    CNameObj name_obj;
                    CNameObj Sendname_obj;
                    name_obj.m_names.clear();
                    Juint32 j = 0;
                    for (int i = 0; i < propertieSize; i++)
                    {
                        std::string name;
                        neb::CJsonObject propertie_obj;
                        if (obj_properties.Get(i, propertie_obj))
                        {
                            propertie_obj.Get("name", name);
                            mskprintf("ReadModelFile name:%s.\n", name.c_str());
                            name_obj.m_names.push_back(name);
                            Sendname_obj.m_names.push_back(name);
                            if (j > m_sendNum)
                            {
                                Sendname_obj.m_serviceName = serviceId;
                                dataObj->m_SendNameObjs.push_back(Sendname_obj);
                                Sendname_obj.m_names.clear();
                                j = 0;
                            }
                            else if (i == (propertieSize - 1))
                            {
                                Sendname_obj.m_serviceName = serviceId;
                                dataObj->m_SendNameObjs.push_back(Sendname_obj);
                                Sendname_obj.m_names.clear();
                            }
                        }
                        j++;
                    }
                    name_obj.m_serviceName = serviceId;
                    dataObj->m_serviceIds[serviceId] = name_obj;
                }
            }
            CTaskManager::CreateInstance().m_dataObj.push_back(dataObj);
        }
    }

    return true;
}

std::string CParamManager::GetFirsAppName(std::string topic)
{
    std::string str;
    size_t index = topic.find_first_of("/", 0);
    if (index != std::string::npos)
    {
        str = topic.substr(0, index);
    }
    return str;
}

std::string CParamManager::CreatMd5(std::string str)
{
    MD5_CTX md5;
    std::string out;
    unsigned char md[16] = {0};

    if (!MD5_Init(&md5))
    {
        mskprintf("MD5_Init error\n");
        return "";
    }
    if (!MD5_Update(&md5, str.c_str(), strlen(str.c_str())))
    {
        mskprintf("MD5_Update error\n");
        return "";
    }
    if (!MD5_Final(md, &md5))
    {
        mskprintf("MD5_Final error\n");
        return "";
    }
    for (int i = 0; i < 16; i++)
    {
        char outc[3] = {0};
        sprintf(outc, "%02X", md[i]);
        std::string outb = outc;
        out = out + outb;
    }
    return out;
}
CParamManager::CParamManager()
{
    m_Thread.SetPackageAcquireManager(this);
    m_T.SetParam(30 * 1000); // 默�??30�???
    m_T.StartCount();
    m_sendNum = 20;
}

CParamManager::~CParamManager()
{
}

constant_Value CParamManager::GetParamConfig(std::string const &dev, std::string const &name)
{
    mskprintf("map size ================%d\n", m_Param_Config.size());
    std::vector<constant_Value> constantVct;
    auto vctIter = m_Param_Config.find(dev);
    if (vctIter != m_Param_Config.end())
    {
        constantVct = vctIter->second;
    }
    else
    {
        constantVct = m_Param_Config["trparam"];
    }
    // auto paramIter = constantVct.begin();
    for (auto paramIter : constantVct)
    {
        if (paramIter.m_Name.compare(name) == 0)
        {
            return paramIter;
        }
    }
    return constant_Value();
}
bool CParamManager::SetDeviceConfig(neb::CJsonObject const &param)
{
    // bool sIMCardFlag = false;
    param.Get("vendorId", m_Device_Config["DevMf"]);
    param.Get("devSn", m_Device_Config["DevSN"]);
    param.Get("esn", m_Device_Config["DevESN"]);
    param.Get("osVer", m_Device_Config["SoftVer"]);
    param.Get("hardwareVersion", m_Device_Config["HardVer"]);
    param.Get("manufDate", m_Device_Config["MfDate"]);
    m_Device_Config["PD_SIMCard_IP"] = "none";
    param.Get("hardwareModel", m_Device_Config["DevModel"]);
    return true;
}

std::string CParamManager::GetDeviceConfig(std::string &name)
{
    auto iter = m_Device_Config.find(name);
    if (iter != m_Device_Config.end())
    {
        return iter->second;
    }
    else
    {
        return std::string();
    }
}