

/*=====================================================================
 * �ļ���param_json.h
 *
 * ��������ȡ��ģ���ļ�
 *
 * ���ߣ�����			2021��9��27��13:45:06
 * 
 * �޸ļ�¼��
 =====================================================================*/


#ifndef PARAM_JSON_H
#define PARAM_JSON_H

#include "public_struct.h"
#include "pub_string.h"
#include "pub_thread.h"
#include "pub_logfile.h"
#include "queue_model.h"
#include "CJsonObject.h"




class CFileObj 
{
public:
    CFileObj();
    virtual~CFileObj();

public:
    std::string             m_model;
    std::string             m_file;
    std::string             m_Topic;
    std::list<guid_body_s>  m_devs;
};

//====================================================================================
//�߳�
//=====================================================================================
class CParamManager;
class CParamThread : public CSL_thread
{
public:
    CParamThread();
    virtual~CParamThread();
    void SetPackageAcquireManager(CParamManager * p);

    virtual void run(void);
    virtual std::string ThreadName(void) const;
    virtual void RecordLog(const std::string & sMsg);

private:
    CParamManager * m_pMng;
};

//=====================================================================================
//������
//=====================================================================================
class CParamManager
{
public:
    static CParamManager & CreateInstance();

    bool Init(void);
    void Exit(void);

    void thread_prev(void);
    void thread_func(void);
    void thread_exit(void);

    void OnRun(void);
    void Start(void);
    void Stop(void);

    std::string CreatMd5(std::string str);

    constant_Value GetParamConfig(std::string const&, std::string const&);
    bool SetDeviceConfig(neb::CJsonObject const&);
    void SetBackParamConfig(neb::CJsonObject &, std::string const&);
    std::map<std::string, std::string> ActivateParamConfig(neb::CJsonObject, std::string);
    std::string GetDeviceConfig(std::string&);

    void SetParamConfig(neb::CJsonObject const&, std::string const&);

    bool GetDataCenterParam(std::string&);
    bool SetDataCenterParam(mqtt_data_info_s& recvData, std::string& devData);


    CParamManager();
    ~CParamManager();

    CParamManager& operator = (const CParamManager&);
    CParamManager(const CParamManager&);
private:

    //std::string UnpackToken(neb::CJsonObject obj);                           // ��ȡTokenֵ
    bool ReadJsonFile();
    bool ReadModelFile();
    // �������� 


private:
    // ��ȡ�ַ��� 
       
    std::string GetFirsAppName(std::string topic);
public:
    std::list<CFileObj>                                             m_FileObjs;
    Juint32                                                         m_sendPeriod;
    Juint32                                                         m_sendNum;
    Juint32                                                         m_yxcycle;
private:
    CIIMutex	                   		 	                        m_cs;               //  ����
    CParamThread           		                                    m_Thread;           //  �߳�     
    std::map<std::string, std::vector<constant_Value>>              m_Param_Config;
    std::map<std::string, std::string>                              m_Device_Config;
    std::map<std::string, std::vector<constant_Value>>              m_Param_Config_Back; //

    CTimerCnt								                        m_T;				// ��ʱ�� ����
    friend class CParamThread;

};

#endif
