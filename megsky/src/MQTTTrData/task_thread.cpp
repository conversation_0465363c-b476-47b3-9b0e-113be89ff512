#include <openssl/md5.h>
#include "mqtt_pub_inter.h"
#include "param_json.h"
#include "task_thread.h"
#include <iostream>
#include <iomanip>
#include <sstream>
#include <chrono>
#include <ctime>
#include "unordered_set"
#include <algorithm>
#include <memory>
std::string model_compare;
std::string m_requestId;
std::string m_serviceId;
uint8_t first_data = 0;
uint8_t m_data_len_flag = 0;
char m_esn_sub[128] = "";
uint8_t esn_flag = 1;
terminalCmd_s m_cmd;
extern uint32_t m_equipment_online;
std::unordered_set<std::string> m_zhuce;
std::string m_command_dev;
// neb::CJsonObject m_body_command;
CDataObj::CDataObj(void)
{
    m_startFlag = false;
}

CDataObj::~CDataObj()
{
}

CTaskThread::CTaskThread(void)
    : m_pMng(NULL)
{
}

CTaskThread::~CTaskThread()
{
}

void CTaskThread::SetPackageAcquireManager(CTaskManager *p)
{
    m_pMng = p;
}

void CTaskThread::run(void)
{
    m_pMng->thread_prev();
    while (CSL_thread::IsEnableRun())
    {
        m_pMng->thread_func();
    }
    m_pMng->thread_exit();
}

std::string CTaskThread::ThreadName(void) const
{
    return std::string("MQTT 内部Broker线程");
}

void CTaskThread::RecordLog(const std::string &sMsg)
{
}

//***************************************************************
// 报文获取管理�??????
//***************************************************************
CTaskManager &CTaskManager::CreateInstance()
{
    static CTaskManager Mng;
    return Mng;
}

bool CTaskManager::Init(void)
{
    bool bRet(false);
    m_parameters.clear();
    std::list<CFileObj>::iterator iter = CParamManager::CreateInstance().m_FileObjs.begin();
    for (; iter != CParamManager::CreateInstance().m_FileObjs.end(); ++iter)
    {
        CFileObj obj = *iter;
        std::string topic = obj.m_Topic + "+";
        CMqttClientInterManager::CreateInstance().m_topics.push_back(topic);
        CMqttClientInterManager::CreateInstance().agent_mqtt_msg_subscribe((char *)topic.c_str(), QOS);
    }

    n_ops = OPS_INIT;

    SMTimeInfo currenttime = ::ii_get_current_mtime();
    currenttime.nHour = 0;
    currenttime.nMinute = 0;
    currenttime.nSecond = 0;
    m_baseTime = CEPTime(currenttime);
    m_data_storage.clear();
    bRet = m_Thread.start();
    return bRet;
}

void CTaskManager::Exit(void)
{

    m_Thread.close();
    m_Thread.wait_for_end();
}

void CTaskManager::thread_prev(void)
{
}

void CTaskManager::thread_func(void)
{
    OnRun();
}

void CTaskManager::thread_exit(void)
{
}
void setTimefun(neb::CJsonObject obj)
{
    //    char *data = NULL;
    std::string data_m;
    std::string timeCheck;
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        mskprintf("\nJson head fill failed---------.\n");
        return;
    }
    obj.Get("timeCheck", timeCheck);
    std::string newDate = timeCheck.substr(0, 10);
    newDate.append("T");
    newDate.append(timeCheck.substr(11));
    newDate.append(".000");
    // std::string makeaa =
    neb::CJsonObject oJson;
    oJson.Add("token", 123);
    oJson.Add("timestamp", jsheader.timestamp);
    neb::CJsonObject body;
    body.Add("setTime", newDate);
    body.Add("reqTime", newDate);
    oJson.Add("body", body);
    const char *pub_topic = "MQTTTrData/OS-system/JSON/request/setTime";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
        mskprintf("\nJasdasdasdasdsadsa-------%s.\n", mssm.c_str());
    }
}
void CommandYCget(neb::CJsonObject OJSON, std::string command_dev)
{
    std::string str_key;
    neb::CJsonObject OBJ;
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        mskprintf("\nJson head fill failed---------.\n");
        return;
    }
    OBJ.Add("token", jsheader.token);
    OBJ.Add("timestamp", jsheader.timestamp);
    OBJ.AddEmptySubArray("body");
    neb::CJsonObject body;
    body.Add("dev", command_dev);
    body.Add("totalcall", "0");
    neb::CJsonObject jsonArray;
    while (OJSON.GetKey(str_key))
    {
        jsonArray.Add(str_key);
        mskprintf("____________________str_key:%s.\n", str_key.c_str());
    }
    body.Add("body", jsonArray);
    OBJ["body"].Add(body);
    std::string mssm = OBJ.ToString();
    mskprintf("mssm====%s.\n", mssm.c_str());
    C256String pub_topic;
    pub_topic.Format("MQTTTrData/dataCenter/JSON/get/request/realtime");
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

void CTaskManager::CommandparamGet(neb::CJsonObject OJSON, std::string command_dev)
{
    mskprintf("command get-----------%s\n", OJSON.ToString().c_str());
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject ojson;
    ojson.Add("token", jsheader.token);
    ojson.Add("timestamp", jsheader.timestamp);
    ojson.AddEmptySubArray("body");
    neb::CJsonObject body;
    body.Add("dev", command_dev);
    body.Add("totalcall", "0");
    body.AddEmptySubArray("body");
    neb::CJsonObject devObj;

    std::string str_key;
    std::map<std::string, std::string> deviceInfoMap;

    while (OJSON.GetKey(str_key))
    {
        auto param = CParamManager::CreateInstance().GetDeviceConfig(str_key);
        if (!param.empty())
        {
            deviceInfoMap[str_key] = param;
            devObj.Add(str_key, param);
            continue;
        }
        body["body"].Add(str_key);
    }

    mskprintf("get obj array size ----------------%d\n", body["body"].GetArraySize());
    if (body["body"].GetArraySize() > 0)
    {
        ojson["body"].Add(body);
        m_data_storage.clear();
        std::string mssm = ojson.ToString();
        C256String pub_topic;
        pub_topic.Format("MQTTTrData/dataCenter/JSON/get/request/parameter");
        mskprintf("%s\n", mssm.c_str());
        mskprintf("%s\n", pub_topic.ToChar());
        if (mssm.size() > 0)
        {
            mqtt_data_info_s item = {0};
            memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
            memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
            item.msg_send_lenth = mssm.size();
            item.retained = 0;
            CMqttClientInterManager::CreateInstance().Push_SendItem(item);
            m_data_len_flag = 0;
        }
    }

    if (deviceInfoMap.size() > 0)
    {
        neb::CJsonObject ojsonDev;
        ojsonDev.Add("token", jsheader.token);
        ojsonDev.Add("timestamp", jsheader.timestamp);
        neb::CJsonObject body_command;
        body_command.Add("requestId", m_requestId);
        body_command.Add("serviceId", m_serviceId);
        body_command.Add("serviceProperties", devObj);
        ojsonDev.Add("body", body_command);

        std::string topic_id;
        for (const DeviceObj &device : m_parameters)
        {
            for (const enroll_body_s &enrollBody : device.m_devives)
            {
                if (enrollBody.dev == m_data_dev)
                {
                    topic_id = enrollBody.dev;
                }
            }
        }

        std::string mssmDev = ojsonDev.ToString();
        C256String pub_topicDev;
        pub_topicDev.Format("MQTTTrData/MQTTIot/JSON/action/response/terminalCmd/%s", topic_id.c_str());
        mskprintf("%s\n", mssmDev.c_str());
        mskprintf("%s\n", pub_topicDev.ToChar());
        if (mssmDev.size() > 0)
        {
            mqtt_data_info_s item = {0};
            memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssmDev.c_str(), MSG_ARRVD_MAX_LEN, mssmDev.size());
            memcpy_safe(item.pubtopic, 256, (char *)pub_topicDev.ToChar(), 256, pub_topicDev.Length());
            item.msg_send_lenth = mssmDev.size();
            item.retained = 0;
            CMqttClientInterManager::CreateInstance().Push_SendItem(item);
            m_data_len_flag = 0;
        }
    }
}

void CTaskManager::CommandparamSet(neb::CJsonObject object, std::string command_dev)
{
    CParamManager::CreateInstance().SetBackParamConfig(object, command_dev);
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        mskprintf("\nJson head fill failed---------.\n");
        return;
    }
    neb::CJsonObject ojson;
    ojson.Add("token", jsheader.token);
    ojson.Add("timestamp", jsheader.timestamp);
    neb::CJsonObject body_command;
    body_command.Add("requestId", m_requestId);
    body_command.Add("serviceId", m_serviceId);
    body_command.Add("serviceProperties", object);
    ojson.Add("body", body_command);
    m_data_storage.clear();

    std::string topic_id;
    for (const DeviceObj &device : m_parameters)
    {
        for (const enroll_body_s &enrollBody : device.m_devives)
        {
            if (enrollBody.dev == m_data_dev)
            {
                topic_id = enrollBody.dev;
            }
        }
    }

    std::string mssmo = ojson.ToString();
    C256String pub_topic1;

    mskprintf("topic_id:%s.\n", topic_id.c_str());
    pub_topic1.Format("MQTTTrData/MQTTIot/JSON/action/response/terminalCmd/%s", topic_id.c_str());
    mskprintf("%s\n", mssmo.c_str());
    mskprintf("%s\n", pub_topic1.ToChar());
    if (mssmo.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssmo.c_str(), MSG_ARRVD_MAX_LEN, mssmo.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic1.ToChar(), 256, pub_topic1.Length());
        item.msg_send_lenth = mssmo.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
        m_data_len_flag = 0;
    }
}

void CTaskManager::CommandparamAcvitice(neb::CJsonObject object, std::string command_dev)
{
    // 从�?�份�?查找，并更新
    auto mssm = CParamManager::CreateInstance().ActivateParamConfig(object, command_dev);

    C256String pub_topic;
    pub_topic.Format("MQTTTrData/dataCenter/JSON/set/request/parameter");
    mskprintf("%s\n", mssm["noshaketime"].c_str());
    mskprintf("%s\n", pub_topic.ToChar());
    if (mssm["noshaketime"].size() > 0)
    {
        mqtt_data_info_s sendData = {0};
        memcpy_safe(sendData.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm["noshaketime"].c_str(), mssm["noshaketime"].size(), mssm["noshaketime"].size());
        memcpy_safe(sendData.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
        sendData.msg_send_lenth = mssm["noshaketime"].size();
        sendData.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(sendData);
    }
    for (auto send : mssm)
    {
        if (send.first.compare("shaketime") == 0)
        {
            std::string topicShakeTime = "MQTTTrData/rspSample/JSON/set/request/shakeTime";
            mskprintf("%s\n", topicShakeTime.c_str());
            mskprintf("%s\n", send.second.c_str());
            if (send.second.size() > 0)
            {
                mqtt_data_info_s sendData = {0};
                memcpy_safe(sendData.msg_send, MSG_ARRVD_MAX_LEN, (char *)send.second.c_str(), send.second.size(), send.second.size());
                memcpy_safe(sendData.pubtopic, 256, (char *)topicShakeTime.c_str(), 256, topicShakeTime.size());
                sendData.msg_send_lenth = send.second.size();
                sendData.retained = 0;
                CMqttClientInterManager::CreateInstance().Push_SendItem(sendData);
            }
        }
    }

    std::string topic_id;
    // char *data = NULL;
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        mskprintf("\nJson head fill failed---------.\n");
        return;
    }

    neb::CJsonObject ojson;
    ojson.Add("token", jsheader.token);
    ojson.Add("timestamp", jsheader.timestamp);
    neb::CJsonObject body_command;
    body_command.Add("requestId", m_requestId);
    body_command.Add("serviceId", m_serviceId);
    body_command.Add("serviceProperties", object);
    ojson.Add("body", body_command);
    m_data_storage.clear();

    for (const DeviceObj &device : m_parameters)
    {
        for (const enroll_body_s &enrollBody : device.m_devives)
        {
            if (enrollBody.dev == m_data_dev)
            {
                topic_id = enrollBody.dev;
            }
        }
    }
    std::string mssmo = ojson.ToString();
    C256String pub_topic1;

    mskprintf("topic_id:%s.\n", topic_id.c_str());
    pub_topic1.Format("MQTTTrData/MQTTIot/JSON/action/response/terminalCmd/%s", topic_id.c_str());
    mskprintf("%s\n", mssmo.c_str());
    mskprintf("%s\n", pub_topic1.ToChar());
    if (mssmo.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssmo.c_str(), MSG_ARRVD_MAX_LEN, mssmo.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic1.ToChar(), 256, pub_topic1.Length());
        item.msg_send_lenth = mssmo.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
        m_data_len_flag = 0;
    }
}
void CTaskManager::Commandparamset(neb::CJsonObject OJSON, std::string command_dev)
{
    std::string topic_id;
    // char *data = NULL;
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        mskprintf("\nJson head fill failed---------.\n");
        return;
    }
    neb::CJsonObject ojson;
    ojson.Add("token", jsheader.token);
    ojson.Add("timestamp", jsheader.timestamp);
    neb::CJsonObject body_command;
    body_command.Add("requestId", m_requestId);
    body_command.Add("serviceId", m_serviceId);
    body_command.Add("serviceProperties", OJSON);
    ojson.Add("body", body_command);
    m_data_storage.clear();
    std::string mssmo = ojson.ToString();
    C256String pub_topic1;
    for (const DeviceObj &device : m_parameters)
    {
        for (const enroll_body_s &enrollBody : device.m_devives)
        {
            if (enrollBody.dev == m_data_dev)
            {
                topic_id = enrollBody.dev;
            }
        }
    }
    mskprintf("topic_id:%s.\n", topic_id.c_str());
    pub_topic1.Format("MQTTTrData/MQTTIot/JSON/action/response/terminalCmd/%s", topic_id.c_str());
    if (mssmo.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssmo.c_str(), MSG_ARRVD_MAX_LEN, mssmo.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic1.ToChar(), 256, pub_topic1.Length());
        item.msg_send_lenth = mssmo.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
        m_data_len_flag = 0;
    }
}
void CTaskManager::Commandtimeset(neb::CJsonObject OJSON, std::string command_dev)
{
    // char *data = NULL;
    std::string topic_id;
    std::string data_m;
    std::string timeCheck;
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        mskprintf("\nJson head fill failed---------.\n");
        return;
    }
    OJSON.Get("timeCheck", timeCheck);
    std::string newDate = timeCheck.substr(0, 10);
    newDate.append("T");
    newDate.append(timeCheck.substr(11));
    newDate.append(".000");
    // std::string makeaa =
    neb::CJsonObject oJson;
    oJson.Add("token", 123);
    oJson.Add("timestamp", jsheader.timestamp);
    neb::CJsonObject body;
    body.Add("setTime", newDate);
    body.Add("reqTime", newDate);
    oJson.Add("body", body);
    const char *pub_topic = "MQTTTrData/OS-system/JSON/request/setTime";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
        mskprintf("\nJasdasdasdasdsadsa-------%s.\n", mssm.c_str());
    }

    neb::CJsonObject ojson;
    ojson.Add("token", jsheader.token);
    ojson.Add("timestamp", jsheader.timestamp);
    neb::CJsonObject body_command;
    body_command.Add("requestId", m_requestId);
    body_command.Add("serviceId", m_serviceId);
    neb::CJsonObject time_Json;
    time_Json.Add("timeCheck", newDate);
    body_command.Add("serviceProperties", time_Json);
    ojson.Add("body", body_command);
    // m_data_storage.clear();
    std::string mssmo = ojson.ToString();
    C256String pub_topic1;
    for (const DeviceObj &device : m_parameters)
    {
        for (const enroll_body_s &enrollBody : device.m_devives)
        {
            if (enrollBody.dev == m_data_dev)
            {
                topic_id = enrollBody.dev;
            }
        }
    }
    mskprintf("topic_id:%s.\n", topic_id.c_str());
    pub_topic1.Format("MQTTTrData/MQTTIot/JSON/action/response/terminalCmd/%s", topic_id.c_str());
    if (mssmo.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssmo.c_str(), MSG_ARRVD_MAX_LEN, mssmo.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic1.ToChar(), 256, pub_topic1.Length());
        item.msg_send_lenth = mssmo.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
        m_data_len_flag = 0;
    }
}
void CTaskManager::Commandtimeget(neb::CJsonObject OJSON, std::string command_dev)
{
    // char *data = NULL;
    std::string data_m;
    std::string timeRead;
    std::string topic_id;
    OJSON.Get("timeRead", timeRead);
    neb::CJsonObject oJson;

    struct timeval tv;
    struct timezone tz;
    struct tm *t;

    gettimeofday(&tv, &tz);
    t = localtime(&tv.tv_sec);

    char buffer[64] = {0};
    // snprintf(buffer, sizeof(buffer),"%02d:%02d:%02d",  t->tm_hour, t->tm_min,t->tm_sec);
    snprintf(buffer, sizeof(buffer), "%04d-%02d-%02d %02d:%02d:%02d",
             1900 + t->tm_year, 1 + t->tm_mon, t->tm_mday, t->tm_hour, t->tm_min, t->tm_sec);

    oJson.Add("timeRead", buffer);

    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        mskprintf("\nJson head fill failed---------.\n");
        return;
    }
    neb::CJsonObject ojson;
    ojson.Add("token", jsheader.token);
    ojson.Add("timestamp", jsheader.timestamp);
    neb::CJsonObject body;
    body.Add("requestId", m_requestId);
    body.Add("serviceId", m_serviceId);

    body.Add("serviceProperties", oJson);
    ojson.Add("body", body);
    // m_data_storage.clear();
    std::string mssm = ojson.ToString();
    C256String pub_topic;
    for (const DeviceObj &device : m_parameters)
    {
        for (const enroll_body_s &enrollBody : device.m_devives)
        {
            if (enrollBody.dev == m_data_dev)
            {
                topic_id = enrollBody.dev;
            }
        }
    }
    mskprintf("topic_id:%s.\n", topic_id.c_str());
    pub_topic.Format("MQTTTrData/MQTTIot/JSON/action/response/terminalCmd/%s", topic_id.c_str());
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
        m_data_len_flag = 0;
    }
}
void CommandYXget(neb::CJsonObject OJSON, std::string command_dev)
{

    std::string str_key;

    mskprintf("++++++++++++++++++OJSON:%s.\n", OJSON.ToString().c_str());
    neb::CJsonObject OBJ;
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        mskprintf("\nJson head fill failed---------.\n");
        return;
    }
    OBJ.Add("token", jsheader.token);
    OBJ.Add("timestamp", jsheader.timestamp);
    OBJ.AddEmptySubArray("body");
    neb::CJsonObject body;
    body.Add("dev", command_dev);
    body.Add("totalcall", "0");
    neb::CJsonObject jsonArray;
    // m_body_command = OJSON;
    mskprintf("++++++++++++++++++++++OJSON[0]:%s.\n", OJSON[0].ToString().c_str());
    while (OJSON.GetKey(str_key))
    {
        jsonArray.Add(str_key);
        mskprintf("____________________str_key:%s.\n", str_key.c_str());
    }

    // jsonArray.Add(powerOnAlmKey);
    body.Add("body", jsonArray);
    OBJ["body"].Add(body);
    std::string mssm = OBJ.ToString();
    C256String pub_topic;
    pub_topic.Format("MQTTTrData/dataCenter/JSON/get/request/realtime");
    mskprintf("mssm====%s.\n", mssm.c_str());
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}
void CTaskManager::Commandexecute(neb::CJsonObject obj, std::string command_dev)
{
    mskprintf("command param-----------%s\n", obj.ToString().c_str());
    m_data_len_flag = 1;
    neb::CJsonObject Ojson;
    // std::string method;
    obj.Get("body", Ojson);
    Ojson.Get("method", m_serviceId);
    Ojson.Get("requestId", m_requestId);
    neb::CJsonObject JSON;
    Ojson.Get("cmdContent", JSON);
    if (m_serviceId.compare("analog_Get") == 0)
    {
        CommandYCget(JSON, command_dev);
    }
    if (m_serviceId.compare("discrete_Get") == 0)
    {
        CommandYXget(JSON, command_dev);
    }
    if (m_serviceId.compare("timeRead") == 0)
    {
        Commandtimeget(JSON, command_dev);
    }
    if (m_serviceId.compare("timeCheck") == 0)
    {
        Commandtimeset(JSON, command_dev);
    }
    if (m_serviceId.compare("parameter_Set") == 0)
    {
        CommandparamSet(JSON, command_dev);
    }
    if (m_serviceId.compare("parameter_Get") == 0)
    {
        CommandparamGet(JSON, command_dev);
    }
    if (m_serviceId.compare("parameter_Activate") == 0)
    {
        CommandparamAcvitice(JSON, command_dev);
    }
}

void CTaskManager::equipment_online_Framing(void)
{
    // mskprintf("+++++++++++++++succeff+++++++++++++++++++\n");
    if (m_zhuce.find(m_dev) != m_zhuce.end())
    {
        // IEC_LOG_RECORD(eRunType, "The %s device has already been added and no more needs to be added.", m_dev.c_str());
        // mskprintf("+++++++Registration is complete++++++++\n\n");
    }
    else
    {
        IEC_LOG_RECORD(eRunType, "m_parameters quantity : %d.", m_parameters.size());
        for (const DeviceObj &device : m_parameters)
        {
            for (const enroll_body_s &enrollBody : device.m_devives)
            {
                mskprintf("enrollBody.guid=%s.\n", enrollBody.guid.c_str());
                mskprintf("m_dev=%s.\n", m_dev.c_str());
                if (enrollBody.dev == m_dev)
                {
                    m_zhuce.insert(m_dev);
                    IEC_LOG_RECORD(eRunType, "Add a device %s.", m_dev.c_str());
                    mskprintf("-+-+-+-+-0000-+-+-+-+-\n");
                    mqtt_header_s jsheader = {0};
                    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
                    if (ret != MQTT_OK)
                    {
                        mskprintf("\nJson head fill failed---------.\n");
                        return;
                    }
                    neb::CJsonObject oJson;
                    oJson.Add("token", jsheader.token);
                    oJson.Add("timestamp", jsheader.timestamp);
                    oJson.AddEmptySubObject("body");
                    neb::CJsonObject &body = oJson["body"];
                    body.Add("manufacturerId", enrollBody.manuID);
                    body.Add("manufacturerName", enrollBody.manuName);
                    body.Add("deviceType", enrollBody.deviceType);
                    body.Add("model", device.m_model);
                    body.Add("protocolType", enrollBody.productID);
                    body.Add("devName", enrollBody.dev);
                    body.Add("devDesc", enrollBody.desc);
                    body.Add("subName", enrollBody.guid);
                    body.Add("state", "online");
                    body.Add("event-time", jsheader.timestamp);
                    std::string mssm = oJson.ToString();
                    C256String pub_topic;
                    pub_topic.Format("MQTTTrData/MQTTIot/JSON/report/notification/terminalStatus/%s", enrollBody.dev.c_str());
                    if (mssm.size() > 0)
                    {
                        mqtt_data_info_s item = {0};
                        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
                        memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
                        item.msg_send_lenth = mssm.size();
                        item.retained = 1;
                        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
                    }
                }
            }
        }
    }
    m_equipment_online = 0;
}
void CTaskManager::OnRun(void)
{
    static int count = 0;
    ii_sleep(100);
    count++; // 10 1S 600 1min
    // mskprintf("\033[0;31m ***************************count=%d******************************.\033[0m\n", count);
    if (count == 6000)
    {
        count = 0;
        // mskprintf("\033[0;31m ***************************count=%d******************************.\033[0m\n", count);
        m_zhuce.clear();
        m_parameters.clear();
        first_data = 0;
        IEC_LOG_RECORD(eRunType, "Clear the flag to allow the device to be re-added.");
    }
    // if (m_T.CalOvertime()) {

    m_T.StartCount();

    SMTimeInfo currenttime = ::ii_get_current_mtime();
    CEPTime current = CEPTime(currenttime);

    if (m_second != currenttime.nSecond)
    {
        m_second = currenttime.nSecond;
    }
    else
    {
        return;
    }

    Juint32 period = CParamManager::CreateInstance().m_sendPeriod;
    mskprintf("sendPerio = %d. \n", period);
    if (currenttime.nHour == 0 && currenttime.nMinute == 0 && currenttime.nSecond == 0)
    {
        m_baseTime = CEPTime(currenttime);
    }

    SIntervalTime tm1 = current.ToIntervalTime();
    SIntervalTime tm2 = m_baseTime.ToIntervalTime();

    Juint64 IntervaSeclTime = tm1.nSecond - tm2.nSecond; // 取整 �??????
    mskprintf("IntervaSeclTime = %d. \n", IntervaSeclTime);
    Juint32 remainder = IntervaSeclTime % (period);
    mskprintf("remainder = %d. \n", remainder);
    if (remainder == 0)
    {
        Get_Real_timedata();
        mskprintf("++++++++++++++++++++++++The timing time is up++++++++++++++++++++\n\n");
    }
}

void CTaskManager::Start(void)
{
    std::list<CDataObj *>::iterator itInit = m_dataObj.begin();
    for (; itInit != m_dataObj.end(); itInit++)
    {
        CDataObj *objd = *itInit;
        if (objd->m_startFlag)
        {
            MqttPackOnline(*itInit); // 一直发上线就可以了
            IEC_LOG_RECORD(eRunType, "guid (%s)online.", objd->m_guid.guid.c_str());
            ii_sleep(1000);
        }
    }
}

void CTaskManager::Stop(void)
{
}

char *extract_prefix(const char *input)
{
    if (input == NULL)
    {
        return NULL;
    }
    const char *underscore_pos = strchr(input, '_');
    if (underscore_pos == NULL)
    {
        return NULL;
    }
    size_t prefix_len = underscore_pos - input;
    char *prefix = (char *)malloc(prefix_len + 1);
    if (prefix == NULL)
    {
        return NULL;
    }
    strncpy(prefix, input, prefix_len);
    prefix[prefix_len] = '\0';
    return prefix;
}
#define DATA_JSON_PATH "/data/app/MQTTTrData/configFile/"
void CTaskManager::Get_Real_timedata()
{

    char file_name[512];
    std::list<std::string>::iterator it = m_getdata_dev.begin();
    for (; it != m_getdata_dev.end(); it++)
    {
        neb::CJsonObject oJson;
        std::string getdata_dev = *it;
        mskprintf("00000000000000000000000000getdata_dev:%s.\n", getdata_dev.c_str());
        mqtt_header_s jsheader = {0};
        int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
        if (ret != MQTT_OK)
        {
            MG_LOG_E("\nJson head fill failed.\n");
            // mskprintf("+++++++++++Json head fill failed+++++++++++++\n");
            return;
        }
        char *prefix = extract_prefix(getdata_dev.c_str());
        if (prefix == NULL)
        {
            return;
        }
        mskprintf("prefix = %s.\n", prefix);
        sprintf(file_name, "%s%s.json", DATA_JSON_PATH, prefix);
        mskprintf("file_name = %s.\n", file_name);

        if (strcmp(prefix, "TTU") == 0)
        {
            FILE *test = fopen(file_name, "rb");
            if (test == NULL)
            {
                copyFile("/usr/local/extapps/MQTTTrData/configFile/TTU.json", "/data/app/MQTTTrData/configFile/TTU.json");
            }
            else
            {
                fclose(test);
            }
        }
        FILE *fp = fopen(file_name, "rb");
        if (fp == NULL)
        {
            mskprintf("There is no model file, and all models are queried by default ! ! !");

            oJson.Add("token", jsheader.token);
            oJson.Add("timestamp", jsheader.timestamp);
            oJson.AddEmptySubArray("body");

            neb::CJsonObject body_wai;
            body_wai.Add("dev", getdata_dev);

            body_wai.Add("totalcall", "1");
            body_wai.AddEmptySubArray("body");

            oJson["body"].Add(body_wai);
        }
        else
        {
            std::string str_key;
            fseek(fp, 0, SEEK_END);
            int file_size = ftell(fp);
            mskprintf("file_size:%d.\n", file_size);
            char *tmp = (char *)malloc(file_size * sizeof(char) + 1);
            memset(tmp, 0, file_size * sizeof(char));
            fseek(fp, 0, SEEK_SET);
            size_t read_size = fread(tmp, sizeof(char), file_size, fp);
            tmp[read_size] = '\0';
            mskprintf("tmp:%s.\n", tmp);
            fclose(fp);
            mskprintf("1111111111111111\n");
            neb::CJsonObject OBJ;
            if (!OBJ.Parse(tmp))
            {
                IEC_LOG_RECORD(eErrType, "json_loadb real_data.msg_send failed.");
                return;
            }
            mskprintf("22222222222222222222222222222222\n");
            neb::CJsonObject obj_body;
            if (!OBJ.Get("data", obj_body)) // 获取数组：["daa","aaa","asd"]
            {
                IEC_LOG_RECORD(eErrType, "file:" IEC_MQTTFILE_FILEPATH "read body object failed.");
                return;
            }
            mskprintf("3333333333333333333333333\n");
            oJson.Add("token", jsheader.token);
            oJson.Add("timestamp", jsheader.timestamp);
            oJson.AddEmptySubArray("body");

            neb::CJsonObject body_wai;
            body_wai.Add("dev", getdata_dev);

            body_wai.Add("totalcall", "0");
            // neb::CJsonObject jsonArray;
            // while (obj_body.GetKey(str_key))
            // {
            //     jsonArray.Add(str_key);
            //     mskprintf("____________________str_key:%s.\n", str_key.c_str());
            // }
            // body_wai.Add("body", jsonArray);
            // OBJ["body"].Add(body);
            // body_wai.AddEmptySubArray("body");
            // body_wai["body"].Add(obj_body);
            body_wai.Add("body", obj_body);

            oJson["body"].Add(body_wai);
            mskprintf("4444444444444444444444\n");
            std::string mssm = oJson.ToString();
            mskprintf("mssm====%s.\n", mssm.c_str());
        }
        mskprintf("5555555555555555555555555555\n");
        const char *pub_topic = "MQTTTrData/dataCenter/JSON/get/request/realtime";
        std::string mssm = oJson.ToString();
        if (mssm.size() > 0)
        {
            mqtt_data_info_s item = {0};
            memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
            memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
            item.msg_send_lenth = mssm.size();
            item.retained = 0;
            CMqttClientInterManager::CreateInstance().Push_SendItem(item);
        }
        free(prefix);
    }
}
void CalMachinecode(char *str, char *currentCode)
{
    int RecvBuff[4096];
    memset(RecvBuff, 0, sizeof(RecvBuff));
    int nCount = 0;

    // 将机器码字�?�串�??????�??????为整数数�??????
    for (int i = 0; i < (int)strlen(str); i += 2)
    {
        char str2[3] = {str[i], str[i + 1], '\0'};
        int strtxt = (int)strtol(str2, NULL, 16);
        RecvBuff[nCount] = strtxt;
        nCount++;
    }

    // 根据机器码�?�算注册�??????
    int m;
    for (int i = 0; i < nCount; i++)
    {
        int n = RecvBuff[i];
        if (n == 0x10)
        {
            m = 0xFF;
            char *currentCodePtr = currentCode;
            sprintf(currentCodePtr, "%s%02X%s", currentCode, m, "FF");
        }
        else if (n % 5 == 3)
        {
            m = n + 2;
            char *currentCodePtr = currentCode;
            sprintf(currentCodePtr, "%s%02X%s", currentCode, m, "MEG");
        }
        else
        {
            m = n - 1;
            if (m < 0)
            {
                m = 0;
                char *currentCodePtr = currentCode;
                sprintf(currentCodePtr, "%s%02X%s", currentCode, m, "10");
            }
            else if (m % 3 == 1)
            {
                char *currentCodePtr = currentCode;
                sprintf(currentCodePtr, "%s%02X%s", currentCode, m, "A#");
            }
            else
            {
                char *currentCodePtr = currentCode;
                sprintf(currentCodePtr, "%s%02X%s", currentCode, m, "C$");
            }
        }
    }
}

void CommandReply_to_the_frame(std::string str)
{
    neb::CJsonObject oJson;
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        mskprintf("\nJson head fill failed---------.\n");
        return;
    }
    oJson.Add("token", jsheader.token);
    oJson.Add("token", jsheader.timestamp);
    neb::CJsonObject body;
    body.Add("requestId", m_cmd.requestId);
    body.Add("serviceId", m_cmd.serviceId);
    neb::CJsonObject body_n;
    if (str.compare("0") == 0)
    {
        body_n.Add("SUCCESS");
    }
    else
    {
        body_n.Add("FAIL");
    }
    body.Add("serviceProperties", body_n);
    oJson.Add("body", body);
    std::string mssm = oJson.ToString();
    mskprintf("mssm:%s.\n", mssm.c_str());
    C256String pub_topic;
    pub_topic.Format("MQTTTrData/MQTTIot/JSON/action/response/terminalCmd/%s", m_command_dev.c_str());
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
        item.msg_send_lenth = mssm.size();
        item.retained = 1;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}
void Command_reply(neb::CJsonObject obj, std::string str)
{
    std::string Returnflag;
    obj.Get("statusCode", Returnflag);
    CommandReply_to_the_frame(Returnflag);
}

void CTaskManager::UnpackParamData(neb::CJsonObject object)
{
    mskprintf("set param ------------%s\n", object.ToString().c_str());
    neb::CJsonObject bodyArrary;
    object.Get("body", bodyArrary);
    for (int i = 0; i < bodyArrary.GetArraySize(); i++)
    {
        neb::CJsonObject devObject;
        bodyArrary.Get(i, devObject);
        std::string dev;
        devObject.Get("dev", dev);
        if (std::strstr(dev.c_str(), "TTU") != 0)
        {
            neb::CJsonObject paramObject;
            devObject.Get("body", paramObject);
            mskprintf("changer param ------------%s\n", paramObject.ToString().c_str());
            CParamManager::CreateInstance().SetParamConfig(paramObject, dev);
        }
    }
}

#define ESN_PATH "/data/app/common/"
bool CTaskManager::UnpackData(mqtt_data_info_s &real_data)
{
    char ESN_check_code[128] = "";
    char currentCode[128] = "";
    uint8_t dev_flag = 0;
    neb::CJsonObject oJson;
    if (!oJson.Parse(real_data.msg_send))
    {
        IEC_LOG_RECORD(eErrType, "json_loadb real_data.msg_send failed.");
        return false;
    }

    std::string mssm = oJson.ToString();

    if (mssm.size() > 0)
    {
        if (NULL != strstr(real_data.pubtopic, "MQTTTrData"))
        {
            mskprintf("unpack mqtt topic:%s.\r\n", real_data.pubtopic);
            mskprintf("unpack mqtt msg:%s.\r\n", mssm.c_str());
        }
    }

    C256String pub_topic;
    std::string appName = GetFirsAppName(real_data.pubtopic); // �??????�?????? 首字�??????
    m_dev = GetLastSegment(real_data.pubtopic);               // �??????取尾字�??

    if (NULL != strstr(real_data.pubtopic, "dataCenter/MQTTTrData/JSON/get/response/devRegister"))
    {
        UnpackDeviceenrollment(oJson);
        return true;
    }
    // 解析实时数据
    if (NULL != strstr(real_data.pubtopic, "dataCenter/MQTTTrData/JSON/get/response/realtime"))
    {
        UnpackRealtimedata(oJson);
    }
    if (NULL != strstr(real_data.pubtopic, "dataCenter/MQTTTrData/JSON/get/response/parameter"))
    {
        UnpackRealtimeparameter(oJson);
    }
    if (NULL != strstr(real_data.pubtopic, "dataCenter/Broadcast/JSON/report/notification/parameter"))
    {
        UnpackParamData(oJson);
    }

    if (NULL != strstr(real_data.pubtopic, "/Broadcast/JSON/report/notification/"))
    {
        UnpackDiscretedata(oJson);
    }

    if (NULL != strstr(real_data.pubtopic, "dataCenter/MQTTTrData/JSON/set/response/parameter"))
    {
        std::string status;
        if (oJson.Get("status", status))
        {
            if (status.compare("OK") == 0)
            {
                mskprintf("set param ok\n");
            }
            else if (status.compare("FAILURE"))
            {
                mskprintf("set param fail\n");
            }
        }
    }

    if (NULL != strstr(real_data.pubtopic, "rspSample/MQTTTrData/JSON/set/response/shakeTime"))
    {
        int status;
        if (oJson.Get("status", status))
        {
            if (status == 0)
            {
                mskprintf("set shaketime ok\n");
            }
            else
            {
                mskprintf("set shaketime fail\n");
            }
        }
    }

    if ((NULL != strstr(real_data.pubtopic, "/dataCenter/JSON/set/request/")))
    {
        equipment_online_Framing();
        if (m_getdata_dev.empty())
        {
            m_getdata_dev.push_back(m_dev);
        }
        else
        {
            std::list<std::string>::iterator it = m_getdata_dev.begin();
            for (; it != m_getdata_dev.end(); it++)
            {
                std::string old_dev = *it;
                if (m_dev.compare(old_dev) == 0)
                {
                    dev_flag = 1;
                    break;
                }
            }
            if (dev_flag == 0)
            {
                m_getdata_dev.push_back(m_dev);
            }
        }
    }
    if ((NULL != strstr(real_data.pubtopic, "/JSON/action/request/terminalCmd/")))
    {
        m_command_dev = m_dev;
        Commandexecute(oJson, m_command_dev);
    }
    if (NULL != strstr(real_data.pubtopic, "OS-system/MQTTTrData/JSON/response/setTime"))
    {
        Command_reply(oJson, m_command_dev);
    }
    if (NULL != strstr(real_data.pubtopic, "OS-system/MQTTTrData/JSON/response/devInfo"))
    {

        neb::CJsonObject body;
        std::string esn;
        oJson.Get("body", body);
        body.Get("esn", esn);
        esn_flag = 0;
        strcpy(m_esn_sub, esn.c_str());
        CalMachinecode(m_esn_sub, currentCode);

        // mskprintf("m_esn_sub:%s.\n",m_esn_sub);
        // mskprintf("currentCode:%s.\n",currentCode);
        if (currentCode[0] == '\0')
        {
            return 0;
            //  exit(0);
        }
        else
        {
            strcpy(ESN_check_code, ESN_PATH);
            strcat(ESN_check_code, currentCode);
            FILE *fp = fopen(ESN_check_code, "r"); // 配置文件
            if (NULL == fp)
            {
                exit(0);
                fclose(fp);
            }
            else
            {
                fclose(fp);
            }
            // jiaoyan_flag = 1;
        }
    }
    if (NULL != strstr(real_data.pubtopic, "OS-system/MQTTTrData/JSON/request/keepAlive"))
    {
        mqtt_header_s jsheader = {0};
        int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
        if (ret != MQTT_OK)
        {
            mskprintf("\nJson head fill failed---------.\n");
            return 1;
        }
        neb::CJsonObject oJson;
        oJson.Add("token", 3321);
        oJson.Add("timestamp", jsheader.timestamp);
        oJson.Add("statusCode", 0);
        // oJson.Add("timestamp",jsheader.timestamp);
        std::string mssm = oJson.ToString();
        mskprintf("mssm:%s.\n", mssm.c_str());
        C256String pub_topic;
        pub_topic.Format("MQTTTrData/OS-system/JSON/response/keepAlive");
        if (mssm.size() > 0)
        {
            mqtt_data_info_s item = {0};
            memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
            memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
            item.msg_send_lenth = mssm.size();
            item.retained = 1;
            CMqttClientInterManager::CreateInstance().Push_SendItem(item);
        }
    }

    std::list<CDataObj *>::iterator iter = m_dataObj.begin();
    for (; iter != m_dataObj.end(); ++iter)
    {
        CDataObj *obj = *iter;
        C256String cmdtopic;

        cmdtopic.Format("esdk/terminal/cmdReq/%s/%s/%s/%s/%s",
                        obj->m_manufacturerId.c_str(),
                        obj->m_manufacturerName.c_str(),
                        obj->m_deviceType.c_str(),
                        obj->m_model.c_str(),
                        obj->m_guid.dev.c_str());

        if (obj != NULL)
        {
            mskprintf("obj topic:%s.\r\n", obj->m_topic.c_str());
            if (obj->m_topic.compare(real_data.pubtopic) == 0)
            {
                UnpackRealData(obj, oJson);
            }
            else if (cmdtopic.ToString().compare(real_data.pubtopic) == 0)
            { // 命令处理
            }
        }
    }

    return true;
}

bool CTaskManager::UnpackRealData(CDataObj *dobj, neb::CJsonObject obj)
{

    bool b(false);
    if (!dobj->m_startFlag)
    {
        dobj->m_startFlag = true;
        dobj->m_startTime = ii_get_current_mtime();
    }

    if (dobj == NULL)
    {
        IEC_LOG_RECORD(eErrType, "UnpackRealData dobj = NULL.");
        return false;
    }
    bool bRet = true;
    neb::CJsonObject obj_body;
    if (!obj.Get("body", obj_body))
    {
        IEC_LOG_RECORD(eErrType, "UnpackRealData body = NULL.");
        return false;
    }
    if (!obj_body.IsArray())
    {
        IEC_LOG_RECORD(eErrType, "UnpackRealData body is not Array.");
        return false;
    }
    int asize = obj_body.GetArraySize();
    // 清空当前的变化name
    dobj->m_SpontObj.m_Spontnames.clear();

    for (int i = 0; i < asize; i++)
    {
        neb::CJsonObject obj_data;
        if (!obj_body.Get(i, obj_data))
        {
            mskprintf("UnpackRealData body data get error i = %d.\n", i);
            return false;
        }
        std::string name;
        std::string val;
        std::string stype;
        bRet &= obj_data.Get("name", name);
        bRet &= obj_data.Get("val", val);

        if (name.size() > 0)
        {
            dobj->m_RealDatas[name] = val;
            obj_data.Get("type", stype);
            if (stype.compare("cycle") == 0)
            { // 周期数据赋�?
                continue;
            }
            mskprintf("yx change name = (%s),val = (%s).\n", name.c_str(), val.c_str());
            // 如果name在遥信中则发送变�??????
            std::string serveiceName;
            if (IsFindDiscrete(dobj, name, serveiceName))
            {
                dobj->m_SpontObj.m_Spontnames.push_back(name);
                dobj->m_SpontObj.m_serviceName = serveiceName;
                b = true;
            }
        }
    }
    if (b)
    {
        MqttPackSpontData(dobj);
    }
    return bRet;
}

std::string CTaskManager::UnpackToken(neb::CJsonObject obj)
{
    std::string tokenStr;
    if (obj.Get("token", tokenStr))
    {
        return tokenStr;
    }
    return NULL;
}

void CTaskManager::MqttPackEsn()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;

    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    const char *pub_topic = CFG_APP_NAME "/get/request/esdk/deviceInfo";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
        // CMqttClientInterManager::CreateInstance().agent_MqttMsgPub((char*)(mssm.c_str()), (char *)pub_topic, 0);
    }
}

void CTaskManager::UnpackDeviceenrollment(neb::CJsonObject OBJ)
{
    uint8_t new_dev_flag = 0;
    uint8_t dev_flag = 0;
    // bool found = false;
    neb::CJsonObject obj_body;
    if (!OBJ.Get("body", obj_body))
    {
        IEC_LOG_RECORD(eErrType, "file:" IEC_MQTTFILE_FILEPATH "read body object failed.");
        return;
    }
    if (!obj_body.IsArray())
    {
        IEC_LOG_RECORD(eErrType, "file:" IEC_MQTTFILE_FILEPATH "read body object failed. not array.");
        return;
    }
    int asize = obj_body.GetArraySize();
    for (int i = 0; i < asize; i++)
    {
        DeviceObj fobj;
        neb::CJsonObject obj;
        if (obj_body.Get(i, obj))
        {
            obj.Get("model", fobj.m_model);
            obj.Get("port", fobj.m_port);
            mskprintf("model:%s.\n", fobj.m_model.c_str());
            mskprintf("port:%s.\n", fobj.m_port.c_str());
            neb::CJsonObject data_obj;
            if (obj.Get("body", data_obj))
            {

                if (!data_obj.IsArray())
                {
                    IEC_LOG_RECORD(eErrType, "file:" IEC_MQTTFILE_FILEPATH "read data object failed. not array.");
                    return;
                }
                int dsize = data_obj.GetArraySize();
                for (int j = 0; j < dsize; j++)
                {
                    enroll_body_s parameter;
                    neb::CJsonObject paraobj;
                    if (data_obj.Get(j, paraobj))
                    {
                        paraobj.Get("dev", parameter.dev);
                        paraobj.Get("guid", parameter.guid);
                        paraobj.Get("addr", parameter.addr);
                        paraobj.Get("desc", parameter.desc);
                        paraobj.Get("manuID", parameter.manuID);
                        paraobj.Get("isReport", parameter.isReport);
                        paraobj.Get("nodeID", parameter.nodeID);
                        paraobj.Get("productID", parameter.productID);
                        paraobj.Get("manuName", parameter.manuName);
                        paraobj.Get("ProType", parameter.ProType);
                        paraobj.Get("deviceType", parameter.deviceType);
                        mskprintf("dev:%s.\n", parameter.dev.c_str());
                        if (first_data == 0)
                        {
                            fobj.m_devives.push_back(parameter);
                        }
                        else
                        {
                            std::list<DeviceObj>::iterator it = m_parameters.begin();
                            for (; it != m_parameters.end(); it++)
                            {
                                DeviceObj old_data = *it;
                                std::list<enroll_body_s>::iterator it1 = old_data.m_devives.begin();
                                for (; it1 != old_data.m_devives.end(); it1++)
                                {
                                    enroll_body_s old_body = *it1;
                                    if (old_body.dev.compare(parameter.dev))
                                    {
                                        // IEC_LOG_RECORD(eRunType, "old_body.dev :%s.", old_body.dev.c_str());
                                        // IEC_LOG_RECORD(eRunType, "parameter.dev :%s.", parameter.dev.c_str());
                                        dev_flag = 1;
                                        // mskprintf(" no add dev.\n");
                                    }
                                }
                            }
                            if (dev_flag == 1)
                            {
                            }
                            else
                            {
                                fobj.m_devives.push_back(parameter);
                                new_dev_flag = 1;
                                IEC_LOG_RECORD(eRunType, "dev_flag :%d.", dev_flag);
                            }
                        }
                    }
                }
            }
        }
        if (first_data == 0)
        {
            m_parameters.push_back(fobj);
            IEC_LOG_RECORD(eRunType, "first_data m_parameters.push_back");
        }
        if (new_dev_flag)
        {
            m_parameters.push_back(fobj);
            new_dev_flag = 0;
            mskprintf("add fobj.\n");
            IEC_LOG_RECORD(eRunType, "new_dev_flag m_parameters.push_back ");
        }
    }
    first_data = 1;
}
void CTaskManager::UnpackDiscretedata(neb::CJsonObject obj)
{
    neb::CJsonObject obj_body;
    std::string topic_id;
    std::string datatype;
    if (!obj.Get("datatype", datatype))
    {
        return;
    }
    if (datatype.compare("1") != 0)
    {
        mskprintf("datatype:%s.\n", datatype);
        return;
    }
    if (!obj.Get("body", obj_body))
    {
        IEC_LOG_RECORD(eErrType, "file:" IEC_MQTTFILE_FILEPATH "read body object failed.");
        return;
    }

    if (!obj_body.IsArray())
    {
        IEC_LOG_RECORD(eErrType, "file:" IEC_MQTTFILE_FILEPATH "read body object failed. not array.");
        return;
    }

    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject Json;
    Json.Add("token", jsheader.token);
    Json.Add("timestamp", jsheader.timestamp);
    Json.AddEmptySubObject("body");
    neb::CJsonObject &body = Json["body"];
    body.Add("serviceId", "discrete");

    body.AddEmptySubObject("serviceProperties");
    neb::CJsonObject &serviceProperties = body["serviceProperties"];

    int len = obj_body.GetArraySize();
    mskprintf("------------%s----------\n", obj_body.ToString().c_str());
    for (int i = 0; i < len; i++)
    {
        neb::CJsonObject obj;
        obj_body.Get(i, obj);
        data_storage_s data;
        obj.Get("name", data.name);
        obj.Get("val", data.val);
        if (data.name.compare("PTOV_Op_phsC") == 0)
        {
            data.name = "PTOV_lim_Op_phsC";
        }
        else if (data.name.compare("PTOV_Op_phsB") == 0)
        {
            data.name = "PTOV_lim_Op_phsB";
        }
        else if (data.name.compare("PTOV_Op_phsA") == 0)
        {
            data.name = "PTOV_lim_Op_phsA";
        }
        else if (data.name.compare("PTUV_Op_phsA") == 0)
        {
            data.name = "PTUV_lim_Op_phsA";
        }
        else if (data.name.compare("PTUV_Op_phsB") == 0)
        {
            data.name = "PTUV_lim_Op_phsB";
        }
        else if (data.name.compare("PTUV_Op_phsC") == 0)
        {
            data.name = "PTUV_lim_Op_phsC";
        }
        else if (data.name.compare("PTUV_Alm") == 0)
        {
            data.name = "PTUV_lim_Alm";
        }
        else if (data.name.compare("PTOV_Alm") == 0)
        {
            data.name = "PTOV_lim_Alm";
        }
        mskprintf("neme=%s.\n", data.name.c_str());
        mskprintf("val=%s.\n", data.val.c_str());
        serviceProperties.Add(data.name, data.val);
    }
    mskprintf("-----%s----------\n", serviceProperties.ToString().c_str());
    mskprintf("-----%s----------\n", body.ToString().c_str());
    mskprintf("-----%s----------\n", Json.ToString().c_str());

    m_data_storage.clear();
    std::string mssm = Json.ToString();
    mskprintf("mssm =%s.\n", mssm.c_str());
    // std::string dev = GetLastSegment();
    C256String pub_topic;
    for (const DeviceObj &device : m_parameters)
    {
        for (const enroll_body_s &enrollBody : device.m_devives)
        {
            mskprintf("enrollBody.guid=%s.\n", enrollBody.guid.c_str());
            //  mskprintf("guid=%s.\n",guid.c_str());
            if (enrollBody.dev == m_dev)
            {
                topic_id = enrollBody.dev;
            }
        }
    }
    mskprintf("topic_id:%s.\n", topic_id.c_str());
    pub_topic.Format("MQTTTrData/MQTTIot/JSON/report/notification/terminalData/%s", topic_id.c_str());
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
        item.msg_send_lenth = mssm.size();
        item.retained = 1;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}
void CTaskManager::UnpackRealtimeparameter(neb::CJsonObject obj)
{
    std::string servi_ceId;
    neb::CJsonObject obj_body;
    std::string serviceId;
    // uint8_t new_data = 0;
    std::string topic_id;

    if (!obj.Get("body", obj_body))
    {
        IEC_LOG_RECORD(eErrType, "file:" IEC_MQTTFILE_FILEPATH "read body object failed.");
        return;
    }

    if (!obj_body.IsArray())
    {
        IEC_LOG_RECORD(eErrType, "file:" IEC_MQTTFILE_FILEPATH "read body object failed. not array.");
        return;
    }
    int len = obj_body.GetArraySize();
    for (int i = 0; i < len; i++)
    {
        neb::CJsonObject data_body;
        if (!obj_body.Get(i, data_body))
        {
            mskprintf("array(%d) get failed \n", i);
            return;
        }
        data_body.Get("dev", m_data_dev);
        mskprintf("m_data_dev=%s.\n", m_data_dev.c_str());

        neb::CJsonObject o_body;
        if (!data_body.Get("body", o_body))
        {
            IEC_LOG_RECORD(eErrType, "file:" IEC_MQTTFILE_FILEPATH "read body object failed.");
            return;
        }
        if (!o_body.IsArray())
        {
            IEC_LOG_RECORD(eErrType, "file:" IEC_MQTTFILE_FILEPATH "read body object failed. not array.");
            return;
        }
        int asize = o_body.GetArraySize();
        mskprintf("asize=%d.\n", asize);
        for (int i = 0; i < asize; i++)
        {
            mskprintf("i=%d.\n", i);
            neb::CJsonObject value_body;
            if (!o_body.Get(i, value_body))
            {
                mskprintf("array(%d) get failed \n", i);
                return;
            }
            data_storage_s data;
            value_body.Get("name", data.name);
            value_body.Get("val", data.val);
            mskprintf("neme=%s.\n", data.name.c_str());
            mskprintf("val=%s.\n", data.val.c_str());
            m_data_storage.push_back(data);
        }
    }
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }
    neb::CJsonObject ojson;
    ojson.Add("token", jsheader.token);
    ojson.Add("timestamp", jsheader.timestamp);
    neb::CJsonObject body;
    body.Add("requestId", m_requestId);
    body.Add("serviceId", m_serviceId);
    neb::CJsonObject data;
    std::list<data_storage_s>::iterator at = m_data_storage.begin();
    for (; at != m_data_storage.end(); at++)
    {
        data_storage_s val_zanshi = *at;
        data.Add(val_zanshi.name, val_zanshi.val);
    }
    body.Add("serviceProperties", data);
    ojson.Add("body", body);
    m_data_storage.clear();
    std::string mssm = ojson.ToString();
    C256String pub_topic;
    for (const DeviceObj &device : m_parameters)
    {
        for (const enroll_body_s &enrollBody : device.m_devives)
        {
            if (enrollBody.dev == m_data_dev)
            {
                topic_id = enrollBody.dev;
            }
        }
    }
    mskprintf("topic_id:%s.\n", m_data_dev.c_str());
    pub_topic.Format("MQTTTrData/MQTTIot/JSON/action/response/terminalCmd/%s", m_data_dev.c_str());
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
        m_data_len_flag = 0;
    }
}
void CTaskManager::UnpackRealtimedata(neb::CJsonObject obj)
{
    mskprintf("realdata*******************%s*********************\n", obj.ToString().c_str());
    std::string servi_ceId;
    neb::CJsonObject obj_body;
    std::string serviceId;
    // uint8_t new_data = 0;
    std::string topic_id;

    if (!obj.Get("body", obj_body))
    {
        IEC_LOG_RECORD(eErrType, "file:" IEC_MQTTFILE_FILEPATH "read body object failed.");
        return;
    }

    if (!obj_body.IsArray())
    {
        IEC_LOG_RECORD(eErrType, "file:" IEC_MQTTFILE_FILEPATH "read body object failed. not array.");
        return;
    }
    uint32_t len = obj_body.GetArraySize();
    for (uint32_t i = 0; i < len; i++)
    {
        neb::CJsonObject data_body;
        if (!obj_body.Get(i, data_body))
        {
            mskprintf("array(%d) get failed \n", i);
            return;
        }
        data_body.Get("dev", m_data_dev);
        mskprintf("m_data_dev=%s.\n", m_data_dev.c_str());

        neb::CJsonObject o_body;
        if (!data_body.Get("body", o_body))
        {
            IEC_LOG_RECORD(eErrType, "file:" IEC_MQTTFILE_FILEPATH "read body object failed.");
            return;
        }
        if (!o_body.IsArray())
        {
            IEC_LOG_RECORD(eErrType, "file:" IEC_MQTTFILE_FILEPATH "read body object failed. not array.");
            return;
        }

        int asize = o_body.GetArraySize();
        std::string artgStr = CParamManager::CreateInstance().GetParamConfig("param", "ARtg").m_Value;
        std::string artgSndStr = CParamManager::CreateInstance().GetParamConfig("param", "ARtgSnd").m_Value;
        float ctA = std::stod(artgStr.c_str()) / std::stod(artgSndStr.c_str());
        std::string vrtgStr = CParamManager::CreateInstance().GetParamConfig("param", "VRtg").m_Value;
        std::string vrtgSndStr = CParamManager::CreateInstance().GetParamConfig("param", "VRtgSnd").m_Value;
        float ctV = std::stod(vrtgStr.c_str()) / std::stod(vrtgSndStr.c_str());
        mskprintf("CTA----%f\n", ctA);
        mskprintf("CTV----%f\n", ctV);
        for (int i = 0; i < asize; i++)
        {
            // bool flagHz = false;
            mskprintf("i=%d.\n", i);
            neb::CJsonObject value_body;
            if (!o_body.Get(i, value_body))
            {
                mskprintf("array(%d) get failed \n", i);
                return;
            }
            data_storage_s data;
            std::string Name;
            std::string Val;
            value_body.Get("name", Name);
            value_body.Get("val", Val);
            if (Name.compare("PhV_phsA") == 0 || Name.compare("PhV_phsB") == 0 || Name.compare("PhV_phsC") == 0)
            {
                auto ret = std::stod(Val);
                if (ret < std::stod(CParamManager::CreateInstance().GetParamConfig("param", "V_Zero_Drift").m_Value.c_str()))
                {
                    ret = 0.0;
                }
                ret = ret * ctV;
                data.val = std::to_string(ret);
                data.name = Name;
                m_data_storage.push_back(data);
                continue;
            }
            else if (Name.compare("A_phsA") == 0 || Name.compare("A_phsB") == 0 || Name.compare("A_phsC") == 0)
            {
                auto ret = std::stod(Val);
                if (ret < std::stod(CParamManager::CreateInstance().GetParamConfig("param", "A_Zero_Drift").m_Value.c_str()))
                {
                    ret = 0.0;
                }
                ret = ret * ctA;
                data.val = std::to_string(ret);
                data.name = Name;
                m_data_storage.push_back(data);
                continue;
            }
            else if (strncmp(Name.c_str(), "TotVar", strlen("TotVar")) == 0)
            {
                auto ret = std::stod(Val) * ctV * ctA;
                data.val = std::to_string(ret);
                data.name = Name;
                m_data_storage.push_back(data);
                continue;
            }
            else if (strncmp(Name.c_str(), "TotW", strlen("TotW")) == 0)
            {
                auto ret = std::stod(Val) * ctV * ctA;
                data.val = std::to_string(ret);
                data.name = Name;
                m_data_storage.push_back(data);
                continue;
            }
            else if (Name.compare("SeqA_c0") == 0)
            {
                auto ret = std::stod(Val) * ctA / 3;
                data.val = std::to_string(ret);
                data.name = Name;
                m_data_storage.push_back(data);
                continue;
            }
            else if (Name.compare("SeqV_c0") == 0)
            {
                auto ret = std::stod(Val) * ctV / 3;
                data.val = std::to_string(ret);
                data.name = Name;
                m_data_storage.push_back(data);
                continue;
            }

            data.name = Name;
            data.val = Val;
            mskprintf("name------1-------%s\n", data.name);
            mskprintf("val--------1------%s\n", data.val);
            mskprintf("neme=%s.\n", data.name.c_str());
            mskprintf("val=%s.\n", data.val.c_str());
            m_data_storage.push_back(data);
        }
    }

    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    if (m_data_len_flag == 0)
    {
        neb::CJsonObject Json;
        // neb::CJsonObject body;
        Json.Add("token", jsheader.token);
        Json.Add("timestamp", jsheader.timestamp);
        // Json.Add("body",body);
        Json.AddEmptySubObject("body");
        neb::CJsonObject &body = Json["body"];
        body.Add("serviceId", "discrete");

        body.AddEmptySubObject("serviceProperties");
        neb::CJsonObject &serviceProperties = body["serviceProperties"];
        std::list<data_storage_s>::iterator at = m_data_storage.begin();
        for (; at != m_data_storage.end(); at++)
        {
            data_storage_s val_zanshi = *at;

            serviceProperties.Add(val_zanshi.name, val_zanshi.val);
            // mskprintf("val_zanshi.name.c_str():%s.\n", val_zanshi.name.c_str());
            // mskprintf("val_zanshi.name.c_str():%s.\n", val_zanshi.val.c_str());
        }
        m_data_storage.clear();

        std::string mssm1 = serviceProperties.ToString();
        mskprintf("mm1=%s.\n", mssm1.c_str());

        std::string mssm = Json.ToString();
        mskprintf("mssm =%s.\n", mssm.c_str());
        C256String pub_topic;
        for (const DeviceObj &device : m_parameters)
        {
            for (const enroll_body_s &enrollBody : device.m_devives)
            {
                mskprintf("enrollBody.guid=%s.\n", enrollBody.guid.c_str());
                //  mskprintf("guid=%s.\n",guid.c_str());
                if (enrollBody.dev == m_data_dev)
                {
                    topic_id = enrollBody.dev;
                }
            }
        }
        mskprintf("topic_id:%s.\n", topic_id.c_str());
        pub_topic.Format("MQTTTrData/MQTTIot/JSON/report/notification/terminalData/%s", topic_id.c_str());
        if (mssm.size() > 0)
        {
            mqtt_data_info_s item = {0};
            memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
            memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
            item.msg_send_lenth = mssm.size();
            item.retained = 0;
            CMqttClientInterManager::CreateInstance().Push_SendItem(item);
        }
    }
    else if (m_data_len_flag == 1)
    {
        neb::CJsonObject ojson;
        ojson.Add("token", jsheader.token);
        ojson.Add("timestamp", jsheader.timestamp);
        neb::CJsonObject body;
        body.Add("requestId", m_requestId);
        body.Add("serviceId", m_serviceId);
        neb::CJsonObject data;
        std::list<data_storage_s>::iterator at = m_data_storage.begin();
        for (; at != m_data_storage.end(); at++)
        {
            data_storage_s val_zanshi = *at;
            data.Add(val_zanshi.name, val_zanshi.val);
        }
        body.Add("serviceProperties", data);
        ojson.Add("body", body);
        m_data_storage.clear();
        std::string mssm = ojson.ToString();
        C256String pub_topic;
        for (const DeviceObj &device : m_parameters)
        {
            for (const enroll_body_s &enrollBody : device.m_devives)
            {
                if (enrollBody.dev == m_data_dev)
                {
                    topic_id = enrollBody.dev;
                }
            }
        }

        mskprintf("topic_id:%s.\n", topic_id.c_str());
        pub_topic.Format("MQTTTrData/MQTTIot/JSON/action/response/terminalCmd/%s", topic_id.c_str());
        if (mssm.size() > 0)
        {
            mqtt_data_info_s item = {0};
            memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
            memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
            item.msg_send_lenth = mssm.size();
            item.retained = 0;
            CMqttClientInterManager::CreateInstance().Push_SendItem(item);
            m_data_len_flag = 0;
        }
    }
}
void CTaskManager::MqttPackOnline(CDataObj *obj)
{
    if (obj == NULL)
    {
        return;
    }

    if (m_esn.empty())
    {
        return;
    }

    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;

    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);

    neb::CJsonObject oBodyJson;
    oBodyJson.Add("state", "online");
    // obj->m_startTime
    C256String eventTime;
    STimeInfo st = ii_get_current_time();

    // 测试
    eventTime.Format("%04d-%02d-%02dT%02d:%02d:%02dZ",
                     st.nYear,
                     st.nMonth,
                     st.nDay,
                     st.nHour,
                     st.nMinute,
                     st.nSecond);

    oBodyJson.Add("event-time", eventTime.ToString()); // �??????一次收到交采数�??????�??????
    oJson.Add("body", oBodyJson);

    C256String pub_topic;
    pub_topic.Format("%s/notify/event/gwTerminal/status/%s/%s/%s/%s/%s/%s%s",
                     CFG_APP_NAME,
                     obj->m_manufacturerId.c_str(),
                     obj->m_manufacturerName.c_str(),
                     obj->m_deviceType.c_str(),
                     obj->m_model.c_str(),
                     obj->m_protocolType.c_str(),
                     obj->m_guid.dev.c_str(),
                     m_esn.c_str());

    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
        item.msg_send_lenth = mssm.size();
        item.retained = 1;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
        // CMqttClientInterManager::CreateInstance().agent_MqttMsgPub((char*)(mssm.c_str()), (char *)pub_topic.ToChar(), 1);
    }
}

void CTaskManager::MqttPackData(CDataObj *obj)
{
    bool bRet = false;
    if (obj == NULL)
    {
        return;
    }

    if (m_esn.empty())
    {
        return;
    }

    std::list<CNameObj>::iterator it = obj->m_SendNameObjs.begin();
    for (; it != obj->m_SendNameObjs.end(); it++)
    {
        bRet = false;
        CNameObj nameObj = *it;
        std::string serviceId = nameObj.m_serviceName;

        if (serviceId.compare("discrete") == 0 && CParamManager::CreateInstance().m_yxcycle == 0)
        {
            continue;
        }

        mqtt_header_s jsheader = {0};
        int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
        if (ret != MQTT_OK)
        {
            MG_LOG_E("\nJson head fill failed.\n");
            return;
        }

        SMTimeInfo currenttime = ::ii_get_current_mtime();
        ret = snprintf(jsheader.timestamp, 32, "%04d-%02d-%02dT%02d:%02d:%02dZ",
                       currenttime.nYear,
                       currenttime.nMonth,
                       currenttime.nDay,
                       currenttime.nHour,
                       currenttime.nMinute,
                       0);

        neb::CJsonObject oJson;

        oJson.Add("token", jsheader.token);
        oJson.Add("timestamp", jsheader.timestamp);

        neb::CJsonObject oBodyJson;
        neb::CJsonObject oServicePropertiesJson;
        // neb::CJsonObject oDataPropertiesJson;

        oBodyJson.Add("serviceId", serviceId);
        std::list<std::string>::iterator itName = nameObj.m_names.begin();
        for (; itName != nameObj.m_names.end(); itName++)
        {
            std::string sname = *itName;
            std::string svalue = obj->m_RealDatas[sname];
            if (svalue.size() > 0)
            {
                oServicePropertiesJson.Add(sname, svalue);
                bRet = true;
            }
        }
        if (bRet)
        {
            oBodyJson.Add("serviceProperties", oServicePropertiesJson);
            oJson.Add("body", oBodyJson);
            C256String pub_topic;
            pub_topic.Format("%s/terminal/dataReport/%s/%s/%s/%s/%s%s",
                             CFG_APP_NAME,
                             obj->m_manufacturerId.c_str(),
                             obj->m_manufacturerName.c_str(),
                             obj->m_deviceType.c_str(),
                             obj->m_model.c_str(),
                             // obj->m_protocolType.c_str(),
                             obj->m_guid.dev.c_str(),
                             m_esn.c_str());

            std::string mssm = oJson.ToString();
            if (mssm.size() > 0 && bRet)
            {
                mqtt_data_info_s item = {0};
                memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
                memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
                item.msg_send_lenth = mssm.size();
                item.retained = 0;
                CMqttClientInterManager::CreateInstance().Push_SendItem(item);
            }
        }

        ii_sleep(100);
    }
}

void CTaskManager::MqttPackSpontData(CDataObj *obj)
{
    bool bRet = false;
    if (obj == NULL)
    {
        return;
    }

    std::list<std::string>::iterator it = obj->m_SpontObj.m_Spontnames.begin();
    for (; it != obj->m_SpontObj.m_Spontnames.end(); it++)
    {
        std::string serviceId = obj->m_SpontObj.m_serviceName;
        CNameObj nameObj = obj->m_SpontObj;
        mqtt_header_s jsheader = {0};
        int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
        if (ret != MQTT_OK)
        {
            MG_LOG_E("\nJson head fill failed.\n");
            return;
        }

        neb::CJsonObject oJson;

        oJson.Add("token", jsheader.token);
        oJson.Add("timestamp", jsheader.timestamp);

        neb::CJsonObject oBodyJson;
        neb::CJsonObject oServicePropertiesJson;
        // neb::CJsonObject oDataPropertiesJson;

        oBodyJson.Add("serviceId", serviceId);
        std::list<std::string>::iterator itName = nameObj.m_Spontnames.begin();
        for (; itName != nameObj.m_Spontnames.end(); itName++)
        {
            std::string sname = *itName;
            std::string svalue = obj->m_RealDatas[sname];
            if (svalue.size() > 0)
            {
                oServicePropertiesJson.Add(sname, svalue);
                bRet = true;
            }
        }
        if (bRet)
        {
            oBodyJson.Add("serviceProperties", oServicePropertiesJson);
            oJson.Add("body", oBodyJson);
            C256String pub_topic;
            pub_topic.Format("%s/terminal/dataReport/%s/%s/%s/%s/%s%s",
                             CFG_APP_NAME,
                             obj->m_manufacturerId.c_str(),
                             obj->m_manufacturerName.c_str(),
                             obj->m_deviceType.c_str(),
                             obj->m_model.c_str(),
                             // obj->m_protocolType.c_str(),
                             obj->m_guid.dev.c_str(),
                             m_esn.c_str());

            std::string mssm = oJson.ToString();
            if (mssm.size() > 0 && bRet)
            {
                mqtt_data_info_s item = {0};
                memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
                memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
                item.msg_send_lenth = mssm.size();
                item.retained = 0;
                CMqttClientInterManager::CreateInstance().Push_SendItem(item);
            }
        }
        ii_sleep(1000);
    }
}

bool CTaskManager::IsFindDiscrete(CDataObj *obj, std::string name, std::string &serveiceName)
{
    bool bRet = false;
    std::string serviceDis = "discrete";
    std::string serviceDis1 = "yx";
    std::string serviceDis2 = "YX";

    std::map<std::string, CNameObj>::iterator iter = obj->m_serviceIds.find(serviceDis);
    if (iter != obj->m_serviceIds.end())
    {
        CNameObj nameObj = obj->m_serviceIds[serviceDis];
        std::list<std::string>::iterator itName = nameObj.m_names.begin();
        for (; itName != nameObj.m_names.end(); itName++)
        {
            std::string sname = *itName;
            if (sname.compare(name.c_str()) == 0)
            {
                serveiceName = serviceDis;
                return true;
            }
        }
    }

    std::map<std::string, CNameObj>::iterator iter1 = obj->m_serviceIds.find(serviceDis1);
    if (iter1 != obj->m_serviceIds.end())
    {
        CNameObj nameObj = obj->m_serviceIds[serviceDis1];
        std::list<std::string>::iterator itName = nameObj.m_names.begin();
        for (; itName != nameObj.m_names.end(); itName++)
        {
            std::string sname = *itName;
            if (sname.compare(name.c_str()) == 0)
            {
                serveiceName = serviceDis1;
                return true;
            }
        }
    }

    std::map<std::string, CNameObj>::iterator iter2 = obj->m_serviceIds.find(serviceDis2);
    if (iter2 != obj->m_serviceIds.end())
    {
        CNameObj nameObj = obj->m_serviceIds[serviceDis2];
        std::list<std::string>::iterator itName = nameObj.m_names.begin();
        for (; itName != nameObj.m_names.end(); itName++)
        {
            std::string sname = *itName;
            if (sname.compare(name.c_str()) == 0)
            {
                serveiceName = serviceDis2;
                return true;
            }
        }
    }

    return bRet;
}

std::string CTaskManager::GetFirsAppName(std::string topic)
{
    std::string str;
    size_t index = topic.find_first_of("/", 0);
    if (index != std::string::npos)
    {
        str = topic.substr(0, index);
    }
    return str;
}
std::string CTaskManager::GetLastSegment(std::string topic)
{
    std::string str;
    size_t index = topic.find_last_of("/");
    if (index != std::string::npos && index != topic.length() - 1)
    {
        str = topic.substr(index + 1);
    }
    return str;
}

bool CTaskManager::GetStringVlaue(neb::CJsonObject obj, const char *key, C64String &str)
{
    std::string s;
    if (obj.Get(key, s))
    {
        str = s;
        return true;
    }
    else
    {
        return false;
    }
}

std::string CTaskManager::CreatMd5(std::string str)
{
    MD5_CTX md5;
    std::string out;
    unsigned char md[16] = {0};

    if (!MD5_Init(&md5))
    {
        mskprintf("MD5_Init error\n");
        return "";
    }
    if (!MD5_Update(&md5, str.c_str(), strlen(str.c_str())))
    {
        mskprintf("MD5_Update error\n");
        return "";
    }
    if (!MD5_Final(md, &md5))
    {
        mskprintf("MD5_Final error\n");
        return "";
    }
    for (int i = 0; i < 16; i++)
    {
        char outc[3] = {0};
        sprintf(outc, "%02X", md[i]);
        std::string outb = outc;
        out = out + outb;
    }
    return out;
}
CTaskManager::CTaskManager()
{
    m_Thread.SetPackageAcquireManager(this);
    m_T.SetParam(30 * 1000); // 默�??120??????
    m_T.StartCount();
    m_ESN_T.SetParam(600 * 1000); // 默�??600??????
    m_ESN_T.StartCount();
    m_Data_T.SetParam(30 * 1000); //
    m_Data_T.StartCount();
}

CTaskManager::~CTaskManager()
{
    std::list<CDataObj *>::iterator iter = m_dataObj.begin();
    for (; iter != m_dataObj.end(); ++iter)
    {
        CDataObj *obj = *iter;
        if (obj != NULL)
        {
            delete obj;
            obj = NULL;
        }
    }
}

CNameObj::CNameObj()
{
}

CNameObj::~CNameObj()
{
}