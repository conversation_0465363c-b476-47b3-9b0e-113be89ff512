##############################################################################
# Copyright (c) 2021 【山东梅格彤天电气有限公司】 http://www.megsky.com
#
# @file    : Makefile
# @brief   : 104 board library Makefile
# @note
#

##############################################################################

TZCDIR			 = ../..
OVADIR			 = ../../ova
APPNAME			 = MskIEC104
CROSS_COMPILE	?= aarch64-linux-gnu-
TARGET  		?= $(TZCDIR)/bin/$(APPNAME)

CC 				:= $(CROSS_COMPILE)g++
#CC 				:= arm-linux-gnueabihf-gcc

INCDIRS		:=  $(TZCDIR)/src/include \
				$(TZCDIR)/src/include/tzc_std \
				$(TZCDIR)/src/include/meg_pub \
				$(TZCDIR)/src/include/commdevice \
				$(TZCDIR)/src/third_party/CJsonObject \
				$(TZCDIR)/src/third_party/cpp-base64 \
				$(TZCDIR)/src/third_party/tinyxml \
				
SRCDIRS		:=  $(TZCDIR)/src/$(APPNAME)  \
				$(TZCDIR)/src/third_party/CJsonObject \
				$(TZCDIR)/src/third_party/cpp-base64 \
				$(TZCDIR)/src/third_party/tinyxml \


INCLUDE		:= $(patsubst %, -I %, $(INCDIRS))

CPPFILES	:= $(foreach dir, $(SRCDIRS), $(wildcard $(dir)/*.cpp))
CPPFILENDIR	:= $(notdir  $(CPPFILES))
CPPOBJS		:= $(patsubst %, $(TZCDIR)/obj/$(APPNAME)/%, $(CPPFILENDIR:.cpp=.o))

CFILES		:= $(foreach dir, $(SRCDIRS), $(wildcard $(dir)/*.c))
CFILENDIR	:= $(notdir  $(CFILES))
COBJS		:= $(patsubst %, $(TZCDIR)/obj/$(APPNAME)/%, $(CFILENDIR:.c=.o))

OBJS		:= $(CPPOBJS) $(COBJS)

CFLAGS  	:= -Wall  -Wno-psabi -Wl,-rpath=$(TZCDIR)/lib:$(TZCDIR)/lib/third_lib
LDFLAGS		:=-L$(TZCDIR)/lib -L$(TZCDIR)/lib/third_lib  -lpthread -lpaho-mqtt3c -ljansson -ltzcstd -lmegpub -lcommdevice -lnsl
#-L/usr/lib
VPATH		:= $(SRCDIRS)

.PHONY : clean packet


$(TARGET) : $(OBJS)
	$(CC) $(CFLAGS) $(OBJS) $(LDFLAGS) -o $@ 

$(CPPOBJS):$(TZCDIR)/obj/$(APPNAME)/%.o: %.cpp
	$(CC) -c $(CFLAGS) $(INCLUDE) $< -o $@ 

$(COBJS):$(TZCDIR)/obj/$(APPNAME)/%.o: %.c
	$(CC) -c $(CFLAGS) $(INCLUDE) $< -o $@ 

pac :
	objdump -d $(APPNAME)  > MM.s
	
clean :
	rm -rf $(OBJS)
	rm -rf $(TARGET)

#需要提前将so库文件移动至ova对应文件夹下的lib目录下，没有lib目录需要提前创建
packet :
	rm -f $(TZCDIR)/ova/$(APPNAME)/bin/$(APPNAME)
	rm -f $(TZCDIR)/ova/$(APPNAME).tar
	cp $(TARGET) $(TZCDIR)/ova/$(APPNAME)/bin/$(APPNAME)
	cd $(TZCDIR)/ova/ && tar -zcvf $(APPNAME).tar.gz $(APPNAME)/ && tar -cvf $(APPNAME).tar $(APPNAME).tar.gz && rm -f $(APPNAME).tar.gz



#tar -zcvf lcMonitor.tar.gz lcMonitor/

#tar -cvf lcMonitor.tar lcMonitor.tar.gz