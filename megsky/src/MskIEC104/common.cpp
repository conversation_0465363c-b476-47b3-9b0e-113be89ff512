
#include "common.h"
#include <iostream>
#include <string>
#include <fstream>
#include <ifaddrs.h>
#include <arpa/inet.h>

using namespace std;
const char *common_json_get_string(json_t *object, const char *key)
{
    json_t *ObjString = json_object_get(object, key);
    if (json_is_string(ObjString))
    {
        return json_string_value(ObjString);
    }
    return NULL;
}

bool common_json_get_data_string(char *str, json_t *object, const char *key)
{
    json_t *ObjString = json_object_get(object, key);
    if (json_is_string(ObjString))
    {
        // str = (char *)json_string_value(ObjString);
        strcpy(str, json_string_value(ObjString));
        return true;
    }
    return false;
}

bool common_json_get_data_int(json_int_t *data, json_t *object, const char *key)
{

    json_t *ObjInt = json_object_get(object, key);
    if (json_is_string(ObjInt))
    {
        *data = atoi(json_string_value(ObjInt));
    }
    else if (json_is_integer(ObjInt))
    {
        *data = (json_int_t)json_integer_value(ObjInt);
    }
    else
    {
        return false;
    }
    return true;
}

bool common_json_get_data_hex(json_int_t *data, json_t *object, const char *key)
{
    json_int_t value = 0;
    unsigned int uvalue;
    json_t *ObjInt = json_object_get(object, key);
    if (json_is_string(ObjInt))
    {
        sscanf(json_string_value(ObjInt), "%X", &uvalue);
    }
    else
    {
        return false;
    }
    value = (json_int_t)uvalue;
    *data = value;
    return true;
}

bool common_json_get_data_real(double *data, json_t *object, const char *key)
{

    json_t *ObjReal = json_object_get(object, key);
    if (json_is_string(ObjReal))
    {
        *data = atof(json_string_value(ObjReal));
    }
    else if (json_is_real(ObjReal))
    {
        *data = json_real_value(ObjReal);
    }
    else
    {
        return false;
    }
    return true;
}

bool common_json_get_data_time(SMTimeInfo *time, json_t *object, const char *key)
{
    // 2019-03-01T09:30:08.230+0800
    // 2023-08-09T18:06:50+0800
    json_t *ObjString = json_object_get(object, key);
    char *str = NULL;
    if (json_is_string(ObjString))
    {
        str = (char *)json_string_value(ObjString);
        if (7 == sscanf(str, "%u-%u-%uT%u:%u:%u.%u", (Juint32 *)&time->nYear, (Juint32 *)&time->nMonth, (Juint32 *)&time->nDay,
                        (Juint32 *)&time->nHour, (Juint32 *)&time->nMinute, (Juint32 *)&time->nSecond, (Juint32 *)&time->nMSecond))
            return true;
    }
    return false;
}

bool common_json_get_data_time2(SMTimeInfo *time, json_t *object, const char *key)
{
    // 2019-03-01 09:30:08.230
    json_t *ObjString = json_object_get(object, key);
    char *str = NULL;
    if (json_is_string(ObjString))
    {
        str = (char *)json_string_value(ObjString);
        if (7 == sscanf(str, "%u-%u-%u %u:%u:%u.%u", (Juint32 *)&time->nYear, (Juint32 *)&time->nMonth, (Juint32 *)&time->nDay,
                        (Juint32 *)&time->nHour, (Juint32 *)&time->nMinute, (Juint32 *)&time->nSecond, (Juint32 *)&time->nMSecond))
            return true;
    }
    return false;
}

bool common_str2time(SMTimeInfo *time, const char *timestr)
{
    // 2019-03-01 09:30:08.230
    if (7 == sscanf(timestr, "%u-%u-%u %u:%u:%u.%u", (Juint32 *)&time->nYear, (Juint32 *)&time->nMonth, (Juint32 *)&time->nDay,
                    (Juint32 *)&time->nHour, (Juint32 *)&time->nMinute, (Juint32 *)&time->nSecond, (Juint32 *)&time->nMSecond))
        return true;
    return false;
}

#include <stdio.h>
#include <string.h>
#include <unistd.h>

/*******************************************************************
** ������:     folder_mkdirs
** ��������:   �ɶ༶�����ļ���
** ����:       folder_path:Ŀ���ļ���·��
** ����:       1 - Ŀ���ļ��д��ڣ�2 - ����ʧ��
********************************************************************/
int folder_mkdirs(const char *folder_path)
{
    if (!access(folder_path, F_OK))
    { /* �ж�Ŀ���ļ����Ƿ���� */
        return 1;
    }

    char path[256];      /* Ŀ���ļ���·�� */
    char *path_buf;      /* Ŀ���ļ���·��ָ�� */
    char temp_path[256]; /* �����ʱ�ļ���·�� */
    char *temp;          /* �����ļ������� */
    int temp_len;        /* �����ļ������Ƴ��� */

    memset(path, 0, sizeof(path));
    memset(temp_path, 0, sizeof(temp_path));
    strcat(path, folder_path);
    path_buf = path;

    while ((temp = strsep(&path_buf, "/")) != NULL)
    { /* ���·�� */
        temp_len = strlen(temp);
        if (0 == temp_len)
        {
            continue;
        }
        strcat(temp_path, "/");
        strcat(temp_path, temp);

        if (-1 == access(temp_path, F_OK))
        { /* �������򴴽� */
            if (-1 == mkdir(temp_path, 0777))
            {
                return 2;
            }
        }
    }
    return 1;
}

bool check_file_exists(const char *path)
{
    bool ret = false;
    if (path == NULL)
    {
        return false;
    }
    FILE *fd = NULL;
    fd = fopen(path, "r");
    if (!fd)
    {
        fd = fopen(path, "w");
        if (!fd)
        {
            // printf("can not write file!");
            // exit(1);
            return false;
        }
        fclose(fd);
        ret = false;
    }
    else
    {
        fclose(fd);
        ret = true;
    }
    return ret;
}

int copyFile(std::string src, std::string dst)
{
    ifstream f(dst.c_str());
    if (!f.good())
    {
        printf("%s file is not exist\n", dst.c_str());
        // f�����ļ�
        ifstream source(src, ios::binary);
        ofstream dest(dst, ios::binary);

        dest << source.rdbuf();
        source.close();
        dest.close();
        // printf("���Ƴɹ�\n");
    }

    printf("%s file is exist\n", dst.c_str());
    return 1;
}

std::string getSubnetMask()
{
    // struct sockaddr_in *sin = NULL;
    struct ifaddrs *ifa = NULL, *ifList;
    std::string netName = "";

    if (getifaddrs(&ifList) < 0)
    {
        return netName;
    }

    for (ifa = ifList; ifa != NULL; ifa = ifa->ifa_next)
    {
        if (ifa->ifa_addr->sa_family == AF_INET)
        {
            // printf("n>>> interfaceName: %sn", ifa->ifa_name);

            if (strstr(ifa->ifa_name, "veth-con"))
            {
                netName = ifa->ifa_name;
            }

            // sin = (struct sockaddr_in *)ifa->ifa_addr;
            // // printf(">>> ipAddress: %sn", inet_ntoa(sin->sin_addr));

            // sin = (struct sockaddr_in *)ifa->ifa_dstaddr;
            // // printf(">>> broadcast: %sn", inet_ntoa(sin->sin_addr));

            // sin = (struct sockaddr_in *)ifa->ifa_netmask;
            // printf(">>> subnetMask: %sn", inet_ntoa(sin->sin_addr));
        }
    }

    freeifaddrs(ifList);

    return netName;
}
