/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012.
 */
 
#ifndef INC_AGENT_HW_TYPE_H
#define INC_AGENT_HW_TYPE_H

#if defined(WIN32) || defined(WIN64) && defined(EXPORT_AGENTLITE)
#define HW_API_FUNC __declspec(dllexport)
#else
#define HW_API_FUNC
#endif

/* Return code */
#define HW_TRUE                     (1)
#define HW_FALSE                    (0)
#define HW_SUCCESS                  (0)
#define HW_FAILED                   (1)
#define HW_NULL                     ((void *)0)

#define IOTA_PARAMETER_EMPTY          (-101)
#define IOTA_RESOURCE_NOT_AVAILABLE   (-102)
#define IOTA_INITIALIZATION_REPEATED  (-103)
#define IOTA_LIBRARY_LOAD_FAILED      (-104)
#define IOTA_SECRET_ENCRYPT_FAILED    (-105)
#define IOTA_MQTT_CONNECT_FAILED      (-106)
#define IOTA_MQTT_CONNECT_EXISTED     (-107)
#define IOTA_CERTIFICATE_NOT_FOUND    (-108)

/* Variable Type */								  
typedef int            HW_INT;   /* Indicates type of int. */
typedef unsigned int   HW_UINT;  /* Indicates type of unsigned int. */
typedef char           HW_CHAR;  /* Indicates type of char. */
typedef unsigned char  HW_UCHAR; /* Indicates type of unsigned char. */
typedef int            HW_BOOL;  /* Indicates type of bool. */
typedef void           HW_VOID;  /* Indicates type of void. */

#endif
