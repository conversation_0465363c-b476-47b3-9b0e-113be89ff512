/********************************************************
 $ <AUTHOR> z<PERSON>ran(Cproape) <EMAIL>
 $ @Date         : 2023-06-30 03:10:01
 $ @LastEditors  : zhangran(Cproape) <EMAIL>
 $ @LastEditTime : 2023-06-30 08:44:04
 $ @FilePath     : /megsky/src/include/roma/iota_login.h
 $ @Description  : 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 *********************************************************/
/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012.
 */

#ifndef INC_AGENT_LOGIN_H
#define INC_AGENT_LOGIN_H

#include "hw_type.h"
 


#ifdef __cplusplus
extern "C" {
#endif
/**
* @brief	Device login.
*
* @param [in] void 
*
* @retval IOTA_SUCCESS 			     : Interface call succeeded.
* @retval IOTA_PARAMETER_EMPTY	     : Parameter is empty.
* @retval IOTA_SECRET_ENCRYPT_FAILED : Encrypt failed
* @see iot_type.h.
*/
HW_API_FUNC HW_INT IOTA_Login(void);

/**
* @brief	Device logout.
*
* @param [in] void 
*
* @retval IOTA_SUCCESS 	: Interface call succeeded.
* @retval IOTA_FAILURE	: Interface call  failed.
* @see iot_type.h.
*/
HW_API_FUNC HW_INT IOTA_Logout(void);

/**
* @brief Determine whether the client is still connected.
*
* @param [in] void 
*
* @return Boolean true if the client is connected, otherwise false.
*/
HW_API_FUNC HW_INT IOTA_IsConnected(void);


typedef enum enum_IOTA_LOGIN_REASON_TYPE
{
    EN_IOTA_LGN_REASON_NULL = 0,                //EN_ULGN_REASON_NULL,
    EN_IOTA_LGN_REASON_CONNCET_ERR = 1,         //Connect failed = EN_ULGN_REASON_CONNCET_ERR,
    EN_IOTA_LGN_REASON_SERVER_BUSY = 2,         //Server busy = EN_ULGN_REASON_SERVER_BUSY,
    EN_IOTA_LGN_REASON_AUTH_FAILED = 3,         //Authentication failed = EN_ULGN_REASON_AUTH_FAILED,
    EN_IOTA_LGN_REASON_NET_UNAVAILABLE = 5,     //Network unavailable = EN_ULGN_REASON_NET_UNAVAILABLE,
    EN_IOTA_LGN_REASON_DEVICE_NOEXIST  = 12,    //Device not exist = EN_ULGN_REASON_UNREG_USER,
    EN_IOTA_LGN_REASON_DEVICE_RMVED    = 13,    //Device removed = EN_ULGN_REASON_RMVED_USER,
    EN_IOTA_LGN_REASON_UNKNOWN = 255
}EN_IOTA_LGN_REASON_TYPE;

#ifdef __cplusplus
}
#endif
#endif

