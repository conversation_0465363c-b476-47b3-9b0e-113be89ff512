/********************************************************
 $ <AUTHOR> z<PERSON>ran(Cproape) <EMAIL>
 $ @Date         : 2023-06-30 03:10:01
 $ @LastEditors  : zhangran(Cproape) <EMAIL>
 $ @LastEditTime : 2023-06-30 08:44:50
 $ @FilePath     : /megsky/src/include/roma/iota_hub.h
 $ @Description  : 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 *********************************************************/
/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012.
 */

#ifndef INC_AGENT_IOTA_HUB_H
#define INC_AGENT_IOTA_HUB_H

#include "hw_type.h"


#ifdef __cplusplus
extern "C" {
#endif
/**
* @brief	Add gateway subdevice.
*
* @param [in] uiMid    : mid(1-65535)
* @param [in] pstInfo  : Device info, see ST_IOTA_DEVICE_INFO
* @param [in] qos      : qos
*
* @retval messageId : > 0 success.
* @retval -1        : Failed.
*/
HW_API_FUNC HW_INT IOTA_HubDeviceAdd(HW_UINT uiMid, ST_IOTA_DEVICE_INFO *pstInfo);


/**
* @brief	Remove gateway subdevice.
*
* @param [in] uiMid        : mid(1-65535)
* @param [in] pucDeviceId  : Device ID
*
* @retval messageId : >= 0 success.
* @retval -1        : Failed.
*/
HW_API_FUNC HW_INT IOTA_HubDeviceRemove(HW_UINT uiMid,  HW_CHAR *pucDeviceId);

/**
* @brief	Update gateway subdevice status.
*
* @param [in] uiMid       : mid(1-65535)
* @param [in] pcDeviceId  : Device ID
* @param [in] pcStatus    : Device status("ONLINE" or "OFFLINE")
*
* @retval messageId : >= 0 success.
* @retval -1        : Failed.
*/
HW_API_FUNC HW_INT IOTA_DeviceStatusUpdate(HW_UINT uiMid, HW_CHAR *pcDeviceId, HW_CHAR *pcStatus);

/* This interface function is temporarily unavailable */
HW_API_FUNC HW_INT IOTA_DeviceBatchStatusUpdate(HW_CHAR *pcPayload);


/**
* @brief	Query device information of gateway sub-device.
*
* @param [in] uiMid         : When get the deviceid info of the gateway device, you can fill in 55636
* @param [in] pcMarker      : Get information page, if IoT platform does not support: NULL
* @param [in] uiLimit       : Get the number of information, get gateway device information: 1
* @param [in] pstDeviceInfo : Device status("ONLINE" or "OFFLINE")
*
* @retval messageId : >= 0 success.
* @retval -1        : Failed.
*/
HW_API_FUNC HW_INT IOTA_HubDeviceQuery(HW_UINT uiMid, HW_CHAR *pcMarker, HW_UINT uiLimit, ST_IOTA_DEVICE_INFO *pstDeviceInfo);

/**
* @brief	Determine whether the child device is online.
*
* @param [in] deviceId 		: deviceId [Platform side client ID]
*
* @retval  : 0: online  -1: offline.
*/
HW_API_FUNC HW_INT IOTA_DevIsOnline(HW_CHAR *deviceId);

/**
 * ----------------------------deprecated below-------------------------------------->
 */
typedef enum enum_EN_IOTA_HUB_RESULT_TYPE
{
    EN_IOTA_HUB_RESULT_SUCCESS       = 0,//add/detete success
    EN_IOTA_HUB_RESULT_DEVICE_EXIST  = 1,//device already exist
    EN_IOTA_HUB_RESULT_DEVICE_NOTEXIST = 2,//device not exist
    EN_IOTA_HUB_RESULT_DEVICE_FAILED = 255
}EN_IOTA_HUB_RESULT_TYPE;
#ifdef __cplusplus
}
#endif
#endif
