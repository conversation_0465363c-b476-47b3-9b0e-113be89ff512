/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012.
 */

#ifndef INC_AGENT_IOTA_CFG_H
#define INC_AGENT_IOTA_CFG_H

#include "hw_type.h"



#ifdef __cplusplus
extern "C" {
#endif
/**
* @brief enum values for iItem in IOTA_ConfigSetStr() and IOTA_ConfigSetUint()
*/
typedef enum enum_IOTA_CFG_TYPE
{
    EN_IOTA_CFG_DEVICEID         = 0,  /* the value for this item is deviceId or nodeId */
    EN_IOTA_CFG_DEVICESECRET     = 1,
							     
    EN_IOTA_CFG_MQTT_ADDR        = 5,
    EN_IOTA_CFG_MQTT_PORT        = 6,
	EN_IOTA_CFG_MQTT_URL_PREFIX  = 7,
								 
    EN_IOTA_CFG_AUTH_MODE        = 9,  /* for configuration value of this item, please refer to @enum_IOTA_CFG_AUTH_MODE below */
    
    EN_IOTA_CFG_LOG_LOCAL_NUMBER = 10, /* take effect only when syslog is available */
    EN_IOTA_CFG_LOG_LEVEL        = 11, /* take effect only when syslog is available */

    EN_IOTA_CFG_KEEP_ALIVE_TIME  = 12,
    EN_IOTA_CFG_CONNECT_TIMEOUT  = 13,
    EN_IOTA_CFG_RETRY_INTERVAL   = 14,

    EN_IOTA_CFG_RESET_SECRET_IN_PROGRESS   = 15,

    EN_IOTA_CFG_QOS   = 16,

}EN_IOTA_CFG_TYPE;

/**
* @brief Authentication mode for <<EN_IOTA_CFG_AUTH_MODE>>
*/
typedef enum enum_IOTA_CFG_AUTH_MODE
{
    EN_IOTA_CFG_AUTH_MODE_DEVICE_ID = 0,
    EN_IOTA_CFG_AUTH_MODE_NODE_ID   = 2,
}EN_IOTA_CFG_AUTH_MODE_ID;

/**
* @brief Url channel (TCP/SSL) for <<EN_IOTA_CFG_MQTT_URL_PREFIX>>
*/
typedef enum enum_IOTA_CFG_URL_PREFIX
{
	EN_IOTA_CFG_URL_PREFIX_TCP = 0,
	EN_IOTA_CFG_URL_PREFIX_SSL = 1,
}EN_IOTA_CFG_URL_PREFIX;


/**
* @brief	Configure parameters.
*
* @param [in] iItem  : Configuration items bound to the device, see EN_IOTA_CFG_TYPE			
			EN_IOTA_CFG_DEVICEID                 : Device ID
    		EN_IOTA_CFG_DEVICESECRET     	     : Device secret					     
    		EN_IOTA_CFG_MQTT_ADDR                : LINK ip addr
    		EN_IOTA_CFG_MQTT_PORT                : LINK port
			EN_IOTA_CFG_MQTT_URL_PREFIX  		 : Url channel (TCP/SSL)			 
    		EN_IOTA_CFG_AUTH_MODE                : Authentication mode
    		EN_IOTA_CFG_LOG_LOCAL_NUMBER         : Log Local number
    		EN_IOTA_CFG_LOG_LEVEL                : Log lecel
    		EN_IOTA_CFG_KEEP_ALIVE_TIME          : Keep alive time
    		EN_IOTA_CFG_CONNECT_TIMEOUT          : Connect timeout
    		EN_IOTA_CFG_RETRY_INTERVAL           : Retry interval 
    		EN_IOTA_CFG_RESET_SECRET_IN_PROGRESS : Reset secret  
    		EN_IOTA_CFG_QOS                      : Service quality
* @param [in] pValue :  Set string Value
*
* @retval IOTA_SUCCESS 			     : Login success.
* @retval IOTA_PARAMETER_EMPTY	     : Parameter is empty.
* @see iot_type.h.
*/
HW_API_FUNC HW_INT IOTA_ConfigSetStr(HW_INT iItem, HW_CHAR *pValue);

/**
* @brief	Configure parameters.
*
* @param [in] iItem  : Configuration items bound to the device, see EN_IOTA_CFG_TYPE			
			EN_IOTA_CFG_DEVICEID                 : Device ID
    		EN_IOTA_CFG_DEVICESECRET     	     : Device secret					     
    		EN_IOTA_CFG_MQTT_ADDR                : LINK ip addr
    		EN_IOTA_CFG_MQTT_PORT                : LINK port
			EN_IOTA_CFG_MQTT_URL_PREFIX  		 : Url channel (TCP/SSL	)			 
    		EN_IOTA_CFG_AUTH_MODE                : Authentication mode
    		EN_IOTA_CFG_LOG_LOCAL_NUMBER         : Log Local number
    		EN_IOTA_CFG_LOG_LEVEL                : Log lecel
    		EN_IOTA_CFG_KEEP_ALIVE_TIME          : Keep alive time
    		EN_IOTA_CFG_CONNECT_TIMEOUT          : Connect timeout
    		EN_IOTA_CFG_RETRY_INTERVAL           : Retry interval 
    		EN_IOTA_CFG_RESET_SECRET_IN_PROGRESS : Reset secret  
    		EN_IOTA_CFG_QOS                      : Service quality
* @param [in] uiValue :  Set specific value.
*
* @retval IOTA_SUCCESS 			     : Login success.
* @retval IOTA_PARAMETER_EMPTY	     : Parameter is empty.
* @see iot_type.h.
*/
HW_API_FUNC HW_INT IOTA_ConfigSetUint(HW_INT iItem, HW_UINT uiValue);


#ifdef __cplusplus
}
#endif
#endif

