
#include "iec_Param.h"

CObjInfoTab::CObjInfoTab()
        : m_Type(AppData_Null)
{
}

CObjInfoTab::~CObjInfoTab()
{
	Clear();
}

void CObjInfoTab::Clear()
{
	m_vTab.clear();
}

void CObjInfoTab::PushNew(const tagObjMap& ObjMap)
{
	m_vTab.push_back(ObjMap);
}

Juint32 CObjInfoTab::Size()
{
	return static_cast<Juint32>(m_vTab.size());
}

Jboolean CObjInfoTab::MoveFirst(OBJINFO_ITER& iter)
{
    if (m_vTab.size() <= 0)
    {
        return JFALSE;
    }

    iter = m_vTab.begin();
    return JTRUE;
}

Jboolean CObjInfoTab::GetNext(tagObjMap& ObjMap, OBJINFO_ITER& iter)
{
    if (iter == m_vTab.end())
    {
        return JFALSE;
    }
	ObjMap = *iter;
	iter ++;
	return JTRUE;
}
