
#include <sys/ioctl.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <net/if.h>
#include "pub_socket.h"
#include "IEC101Protocol.h"
#include "param_json.h"
#include "itg_run_diagnotor.h"
#include "iec_fileStorage.h"

CEventConnectSocket::CEventConnectSocket()
	: m_bRunning(false)
{
	m_timer.SetParam(3000);
	m_timer.StartCount();
}

CEventConnectSocket::~CEventConnectSocket()
{
	CSL_thread::close();
}

bool CEventConnectSocket::IsConnectted(void) const
{
	return m_sock.IsOpen();
}

void CEventConnectSocket::AttachLinkSocket(const CIITCPListenSocket::accepted_socket &as)
{
	m_sock.SetAcceptedSocket(as);
	// if(SendHeartAttack() > 0)	// ��������֡�ɹ�
	if (!m_bRunning)
	{
		m_bRunning = CSL_thread::start();
		CItgRunDiagnotor::Instance().setConnectStatus(1);
		mskprintf("tcp server connected \n");

		ULOGstruct ulog;
		ulog.logType = "02";
		ulog.txt = "Channel disconnected";
		ulog.val = "0";

		CRealDataManager::CreateInstance().ULOG_pushevent(ulog);
	}
}

void CEventConnectSocket::SendData(Jchar *pbuf, int len)
{
	if (m_sock.IsOpen())
	{
		if (len > 0)
		{
			int ret = m_sock.Write(pbuf, len);
			if (ret < 0)
			{
				IEC_LOG_RECORD(eRunType, "tcp server,SendData data error len(%d) [sock ret < 0].\n", len);
				CItgRunDiagnotor::Instance().setConnectStatus(0);
				mskprintf("tcp server disconnected,SendData data error [sock ret < 0].\n \n");
			}
		}
	}
	else
	{
		ii_sleep(1000);
	}
}

CIIIPAddressPort CEventConnectSocket::GetClientIPAddr() const
{
	return m_sock.PeerIPAddress();
}


void CEventConnectSocket::run(void)
{
	m_bRunning = true;
	

	while (CSL_thread::IsEnableRun())
	{
		if (m_sock.IsOpen())
		{
			int rcv_len = m_sock.Read(m_cRecvBuffer, RECV_BUF_LEN);
			if (rcv_len > 0)
			{
				// m_pConnector->RecvEventData(m_cRecvBuffer, rcv_len);
				if (get_printf_enable())
				{
					CEPTime ct = CEPTime::LocalTime();
					mskprintf("\n%s:Read len (%03d)  <-<- data:", ct.ToStringMS().ToChar(), rcv_len);
					for (int i = 0; i < rcv_len; i++)
					{
						mskprintf(" %02x", m_cRecvBuffer[i]);
					}
				}
				mskprintf("\n");
				
				CItgRunDiagnotor::Instance().AddRecvQueue((unsigned char *)m_cRecvBuffer, rcv_len);
			}
			if (rcv_len < 0)
			{
				IEC_LOG_RECORD(eErrType, "rcv_len Less than zero,rcv_len(%d).", rcv_len);
				close();
			}
			
			Juint32 ulCnt = 1024 * 8;
			unsigned char uchWrite[1024 * 8] = {0};
			Juint32 ulNCnt = CItgRunDiagnotor::Instance().ContinueSendData(uchWrite, ulCnt);
			if (ulNCnt > 0)
			{
				SendData(reinterpret_cast<Jchar *>(&uchWrite), ulNCnt);
				if (get_printf_enable())
				{
					CEPTime ct = CEPTime::LocalTime();
					mskprintf("\n%s:Write len (%03d) ->-> data:", ct.ToStringMS().ToChar(), ulNCnt);
					for (size_t i = 0; i < ulNCnt; i++)
					{
						mskprintf(" %02x", uchWrite[i]);
					}
				}
				
				mskprintf("\n");
			}

			// if(m_timer.CalOvertime(/*3000*/) && rcv_len >= 0)
			// {
			// 	if(SendHeartAttack() < 0)
			// 		close();
			// 	m_timer.StartCount();
			// }
		}
		else
		{
			CItgRunDiagnotor::Instance().setConnectStatus(0);
			mskprintf("m_sock open failed, tcp server disconnected \n");
			ii_sleep(500);
		}
		// mskprintf("iiiiiiiiiiii = %d.\n",i);
		// i++;
		ii_sleep(20); // ��Ϊ20
	}
	ii_sleep(100);
	m_sock.Close();
	m_bRunning = false;
}

std::string CEventConnectSocket::ThreadName(void) const
{
	return std::string("UI CUIClientSocket_thread!");
}

void CEventConnectSocket::RecordLog(const std::string & /* sMsg*/)
{
}
void CEventConnectSocket::close(void)
{
	m_sock.Close();
	CSL_thread::close();
	CItgRunDiagnotor::Instance().setConnectStatus(0);
	// mskprintf("CEventConnectSocket close tcp server disconnected \n");
	IEC_LOG_RECORD(eRunType, "CEventConnectSocket close tcp server disconnected,tcp server disconnected");

	ULOGstruct ulog;
	ulog.logType = "02";
	ulog.txt = "Channel disconnected";
	ulog.val = "1";

	CRealDataManager::CreateInstance().ULOG_pushevent(ulog);
}

Jint32 CEventConnectSocket::SendHeartAttack()
{
	Juint8 pSendBuf[9];
	pSendBuf[0] = 0xEB;
	pSendBuf[1] = 0x90;
	pSendBuf[2] = 0xEB;
	pSendBuf[3] = 0x90;
	pSendBuf[4] = 0x03;
	pSendBuf[5] = 0x00;
	pSendBuf[6] = 0x99;
	pSendBuf[7] = 0x00;
	pSendBuf[8] = 0x00;

	return m_sock.Write(reinterpret_cast<Jchar *>(&pSendBuf), sizeof(pSendBuf));
}

//======================================================================================================================
// ����SOCKET
//======================================================================================================================
CCoreListenSocket::CCoreListenSocket(const CIIIPAddressPort &local)
	: CListenSockInterface(local)
{
}

CCoreListenSocket::~CCoreListenSocket(void)
{
}

//======================================================================================================================
// ����SOCKET
//======================================================================================================================
CCoreServerSocket::CCoreServerSocket(const CIIIPAddressPort &local, const CIIIPAddressPort &remote)
	: CServerSockInterface(local, remote)
{
}

CCoreServerSocket::~CCoreServerSocket(void)
{
}

// �ⲿ�ͻ�������ʱ,֪ͨ�������ӵ�IP��ַ�Ͷ˿�
void CCoreServerSocket::OnAcceptRemotePeerName(const CIIIPAddressPort &ip)
{
}

// �շ��������
void CCoreServerSocket::OnNotifySend()
{
}

void CCoreServerSocket::OnNotifySendCount(unsigned long len)
{
}

unsigned long CCoreServerSocket::OnGetContinueSend(char *&pbuf)
{
	Juint32 ulCnt = 1024 * 8;
	unsigned char uchWrite[1024 * 8] = {0};
	// Juint32 ulNCnt = CItgRunDiagnotor::Instance().ContinueSendData((unsigned char *)pbuf,ulCnt);
	Juint32 ulNCnt = CItgRunDiagnotor::Instance().ContinueSendData(uchWrite, ulCnt);
	if (ulNCnt > 0)
	{
		pbuf = reinterpret_cast<Jchar *>(uchWrite);
	}
	// memcpy(pbuf, uchWrite, ulNCnt);
	return ulNCnt;
}

void CCoreServerSocket::OnNotifyRecv(char *pbuf, unsigned long len)
{
	if (len > 0)
	{
		CItgRunDiagnotor::Instance().AddRecvQueue((unsigned char *)pbuf, len);
	}
}

// ����֪ͨͨ��״̬
void CCoreServerSocket::OnNotifyDevNotOpen()
{
}

void CCoreServerSocket::OnNotifyDisconnect()
{
	Juint8 con = 0;
	CItgRunDiagnotor::Instance().setConnectStatus(con);
	mskprintf("OnNotifyDisconnect:tcp server disconnected \n");
	ULOGstruct ulog;
	ulog.logType = "02";
	ulog.txt = "Channel connection disconnect";
	ulog.val = "0";

	//CRealDataManager::CreateInstance().ULOG_pushevent(ulog);
}

void CCoreServerSocket::OnNotifyConnected()
{
	Juint8 con = 1;
	CItgRunDiagnotor::Instance().setConnectStatus(con);
	mskprintf("tcp server connected  \n");
	ULOGstruct ulog;
	ulog.logType = "02";
	ulog.txt = "Channel disconnected";
	ulog.val = "0";

	CRealDataManager::CreateInstance().ULOG_pushevent(ulog);
}

void CCoreServerSocket::OnNotifyAlive()
{
	mskprintf("tcp Alive \n");
}

void CCoreServerSocket::OnNotifyError(int err)
{
}

//======================================================================================================================
// �ͻ���SOCKET
//======================================================================================================================
CCoreClientSocket::CCoreClientSocket(const CIIIPAddressPort &local)
	: CClientSockInterface(local)
{
	m_socketflag = false;
}

CCoreClientSocket::~CCoreClientSocket(void)
{
}

void CCoreClientSocket::ProCloseSocketThread()
{
	CloseThread();
	m_socketflag = false;
}

void CCoreClientSocket::ProstartSocketThread()
{
	if (!m_socketflag)
	{
		StartThread();
	}
}

// �շ��������
void CCoreClientSocket::OnNotifySend()
{
}

void CCoreClientSocket::OnNotifySendCount(unsigned long len)
{
}

unsigned long CCoreClientSocket::OnGetContinueSend(char *&pbuf)
{
	Juint32 ulCnt = 1024 * 8;
	unsigned char uchWrite[1024 * 8] = {0};

	Juint32 ulNCnt = CItgRunDiagnotor::Instance().ContinueSendData(uchWrite, ulCnt);
	if (ulNCnt > 0)
	{
		mskprintf("\nSend data=====start======= len ===%d\n", ulNCnt);
		if (get_printf_enable())
		{
			for (Juint32 i = 0; i < ulNCnt; i++)
			{
				mskprintf(" %02x", uchWrite[i]);
			}
		}
		mskprintf("\nSend data=====end===== len ===%d\n", ulNCnt);
		pbuf = reinterpret_cast<Jchar *>(uchWrite);
	}

	return ulNCnt;
}

void CCoreClientSocket::OnNotifyRecv(char *pbuf, unsigned long len)
{
	if (len > 0)
	{
		mskprintf("\nRead data=====start======= len ===%d\n", len);
		if (get_printf_enable())
		{
			for (unsigned long i = 0; i < len; i++)
			{
				mskprintf(" %02x", pbuf[i]);
			}
		}
		mskprintf("\nRead data=====end===== len ===%d\n", len);
		CItgRunDiagnotor::Instance().AddRecvQueue((unsigned char *)pbuf, len);
	}
}

// ����֪ͨͨ��״̬
void CCoreClientSocket::OnNotifyDevNotOpen()
{
}

void CCoreClientSocket::OnNotifyDisconnect()
{
	Juint8 con = 0;
	CItgRunDiagnotor::Instance().setConnectStatus(con);
	mskprintf("tcp client disconnected \n");

	ULOGstruct ulog;
	ulog.logType = "02";
	ulog.txt = "Channel connection disconnect";
	ulog.val = "0";

	//CRealDataManager::CreateInstance().ULOG_pushevent(ulog);
}

void CCoreClientSocket::OnNotifyConnected()
{
	Juint8 con = 1;
	CItgRunDiagnotor::Instance().setConnectStatus(con);
	mskprintf("tcp client tcp connected \n");

	ULOGstruct ulog;
	ulog.logType = "02";
	ulog.txt = "Channel connection establishment";
	ulog.val = "0";

	//CRealDataManager::CreateInstance().ULOG_pushevent(ulog);
}

void CCoreClientSocket::OnNotifyAlive()
{
	mskprintf("tcp client tcp Alive \n");
	// ���ӻ���
}

void CCoreClientSocket::OnNotifyError(int err)
{
}

//======================================================================================================================
// ����SOCKET
//======================================================================================================================
CUartManagerSocket::CUartManagerSocket()
{
	// m_port = port;
}

CUartManagerSocket::~CUartManagerSocket(void)
{
}

unsigned long CUartManagerSocket::send(char *&pbuf)
{
	return 0;
}

void CUartManagerSocket::Recv(char *pbuf, unsigned long len)
{
	if (len > 0)
	{
		mskprintf("\nRead data=====start======= len ===%d\n", len);
		if (get_printf_enable())
		{
			for (unsigned long i = 0; i < len; i++)
			{
				mskprintf(" %02x", pbuf[i]);
			}
		}
		mskprintf("\nRead data=====end===== len ===%d\n", len);
		CItgRunDiagnotor::Instance().AddRecvQueue((unsigned char *)pbuf, len);
	}
}

//=============================================================================
// �߳���Ϣ��
//=============================================================================
CItgThreadInfo::CItgThreadInfo()
	: m_strName("run-thread-name"), m_hAliveEvent(NULL), m_nMaxAliveCnt(60)
{
}

CItgThreadInfo::CItgThreadInfo(const std::string &strName, CIIEventSend *pEvent, int nMaxAliveCnt)
	: m_strName(strName), m_hAliveEvent(pEvent), m_nMaxAliveCnt(nMaxAliveCnt)
{
}

CItgThreadInfo::CItgThreadInfo(const CItgThreadInfo &r)
	: m_strName(r.m_strName), m_hAliveEvent(r.m_hAliveEvent), m_nMaxAliveCnt(r.m_nMaxAliveCnt)
{
}

void CItgThreadInfo::operator=(const CItgThreadInfo &state)
{
	m_strName = state.m_strName;
	m_hAliveEvent = state.m_hAliveEvent;
	m_nMaxAliveCnt = state.m_nMaxAliveCnt;
}

CItgThreadInfo::~CItgThreadInfo(void)
{
}

//=============================================================================
// �߳�״̬��
//=============================================================================
CItgThreadState::CItgThreadState(const CItgThreadInfo &info)
{
	SetInfo(info);
}
CItgThreadState::~CItgThreadState(void)
{
}

void CItgThreadState::SetInfo(const CItgThreadInfo &info)
{
	m_info = info;
	m_nAliveCnt = m_info.MaxAliveCount();
}

bool CItgThreadState::AliveCheck(int nChkCycle)
{
	assert(nChkCycle > 0);
	m_nAliveCnt -= nChkCycle;

	CIIEventSend *hEvent = m_info.AliveEvent();
	CIIEventWait wait_obj(hEvent);
	Jint32 nRet = wait_obj.WaitEvents(0);
	if (TZC_EVENT_TIMEOUT != nRet)
	{
		m_nAliveCnt = m_info.MaxAliveCount();
		hEvent->Clear();
	}

	return (m_nAliveCnt > 0);
}

std::string CItgThreadState::ExceptTxt(void) const
{
	char szTemp[512] = {0};
	sprintf(szTemp, "%s�߳������쳣.[WDT_CNT=%d]", m_info.Name().c_str(), m_nAliveCnt);
	return std::string(szTemp);
}

std::string CItgThreadState::ThreadName(void) const
{
	return m_info.Name();
}

//=============================================================================
// �����̼߳�����
//=============================================================================
CItgWorkThreadMonitor::CItgWorkThreadMonitor(void)
{
}

CItgWorkThreadMonitor::~CItgWorkThreadMonitor(void)
{
	while (!m_WorkThreads.empty())
	{
		WORKTHREAD::iterator it = m_WorkThreads.begin();
		CItgThreadState *pState(it->second);
		delete pState;
		m_WorkThreads.erase(it);
	}

	while (!m_FreeThreads.empty())
	{
		CItgThreadState *pState(m_FreeThreads.front());
		delete pState;
		m_FreeThreads.pop_front();
	}
}

void CItgWorkThreadMonitor::Attach(CIIEventSend *hAliveEnt, const CItgThreadInfo &info)
{
	CIIAutoMutex lock(&m_cs);

	CItgThreadState *pState(0);
	WORKTHREAD::iterator it = m_WorkThreads.find(hAliveEnt);
	if (m_WorkThreads.end() != it)
	{
		pState = it->second;
	}
	else
	{
		if (!m_FreeThreads.empty())
		{
			pState = m_FreeThreads.front();
			m_FreeThreads.pop_front();
		}
		else
		{
			pState = new CItgThreadState(info);
		}
		assert(0 != pState);
		m_WorkThreads[hAliveEnt] = pState;
	}

	pState->SetInfo(info);

	std::string strLogText = info.Name() + std::string("��ʼ����");
	mskprintf("%s \n", strLogText.c_str());
	IEC_LOG_RECORD(eRunType, "CItgWorkThreadMonitor::Attach RUNNING");

	// WriteLogFile(strLogText);
}

void CItgWorkThreadMonitor::Detach(CIIEventSend *hAliveEnt)
{
	CIIAutoMutex lock(&m_cs);

	WORKTHREAD::iterator it = m_WorkThreads.find(hAliveEnt);
	if (m_WorkThreads.end() != it)
	{
		CItgThreadState *pState(it->second);
		m_FreeThreads.push_back(pState);
		m_WorkThreads.erase(it);

		std::string strLogText = pState->ThreadName() + std::string("�˳�����");
		mskprintf("%s \n", strLogText.c_str());
		// WriteLogFile(strLogText);
	}
}

bool CItgWorkThreadMonitor::RTCheck(Jint32 nChkCycle)
{
	CIIAutoMutex lock(&m_cs);

	bool bOK(true);
	WORKTHREAD::iterator it = m_WorkThreads.begin();
	for (; it != m_WorkThreads.end(); ++it)
	{
		CItgThreadState *p(it->second);
		if (!p->AliveCheck(nChkCycle))
		{
			bOK = false;
			std::string strLogText = p->ExceptTxt();
			mskprintf("%s \n", strLogText.c_str());
			// WriteLogFile(strLogText);
		}
	}

	return bOK;
}

//===============================================================
// �����������
//===============================================================

// �����߳�
CRunDiagnotorThread::CRunDiagnotorThread(void)
	: m_pObject(NULL)
{
}

CRunDiagnotorThread::~CRunDiagnotorThread(void)
{
}

void CRunDiagnotorThread::SetObject(CItgRunDiagnotor *p)
{
	m_pObject = p;
}

void CRunDiagnotorThread::run(void)
{
	Jint32 tmp_cnt = 0;
	while (CSL_thread::IsEnableRun())
	{
		ii_sleep(40);
		// tmp_cnt ++;
		// if (tmp_cnt >= 1) {
		m_pObject->thread_func(tmp_cnt);
		//	tmp_cnt = 0;
		//}
	}
}

std::string CRunDiagnotorThread::ThreadName(void) const
{
	return std::string("����������߳�");
}

void CRunDiagnotorThread::RecordLog(const std::string &)
{
}

// ������
CItgRunDiagnotor::CItgRunDiagnotor(void)
	: m_ulDefaultQueueLen(QUEUE_LEN), m_xmitQueue(QUEUE_LEN),
	  m_recvQueue(QUEUE_LEN)
{
	m_connect = 0;
	m_thread.SetObject(this);
	m_xmitQueue.clear();
	m_recvQueue.clear();
	m_101_protocol = NULL;
	Initialize();
}

CItgRunDiagnotor::~CItgRunDiagnotor(void)
{
	m_xmitQueue.clear();
	m_recvQueue.clear();

	if(m_101_protocol)
	{
		delete m_101_protocol;
		m_101_protocol = NULL;
	}

	if(m_pUartManagerSocket)
	{
		delete m_pUartManagerSocket;
		m_pUartManagerSocket = NULL;
	}

	if(m_pClientSocket)
	{
		delete m_pClientSocket;
		m_pClientSocket = NULL;
	}
	
}

CItgRunDiagnotor &CItgRunDiagnotor::Instance()
{
	static CItgRunDiagnotor diag;
	return diag;
}

int CItgRunDiagnotor::get_localip(const char *eth_name, char *local_ip_addr)
{
	int ret = -1;
	register int fd;
	struct ifreq ifr;

	if (local_ip_addr == NULL || eth_name == NULL)
	{
		return ret;
	}
	if ((fd = socket(AF_INET, SOCK_DGRAM, 0)) > 0)
	{
		strcpy(ifr.ifr_name, eth_name);
		if (!(ioctl(fd, SIOCGIFADDR, &ifr)))
		{
			ret = 0;
			strcpy(local_ip_addr, inet_ntoa(((struct sockaddr_in *)&ifr.ifr_addr)->sin_addr));
		}
	}
	if (fd > 0)
	{
		close(fd);
	}
	return ret;
}

void CItgRunDiagnotor::Init(void)
{
	m_101_protocol = new CProtocol101();
	m_101_protocol->Initialize(); // ��Լ��ʼ��
	Initialize();
	m_xmitQueue.clear();
	m_recvQueue.clear();

	if (CParamJsonManager::CreateInstance().m_101ParamDevInfo.connectType == 1
		||CParamJsonManager::CreateInstance().m_101ParamDevInfo.connectType == 2) // ����ģʽ
	{
		m_pUartManagerSocket = new CUartManagerSocket();
		m_iTCP_flag = UARTMANAGER_T;
		m_thread.start();
		return;
	}
	else if (CParamJsonManager::CreateInstance().m_101ParamDevInfo.connectType == BTMANAGER_T) // ����ģʽ
	{	
		m_pUartManagerSocket = new CUartManagerSocket();
		m_iTCP_flag = BTMANAGER_T;
		m_thread.start();
		return;
	}

	char IP[32] = {0};
	sprintf(IP, "%s", "***************");
	std::string strlocalip = CParamJsonManager::CreateInstance().m_101ParamDevInfo.localIP;

	while (get_localip(strlocalip.c_str(), IP) == -1) // T2:veth1   T3:eth0
	{
		CLogFile::Instance().Write(eRunType, "veth1  ip get error....");
		mskprintf("veth1  ip get error....\n");
		ii_sleep(5000); // ��ֻ�ȡ����IP
	}
	mskprintf("veth1  ip is(%s) \n", IP);
	m_pClientSocket = NULL;
	std::string sip = CParamJsonManager::CreateInstance().m_101ParamDevInfo.mainIP;
	Juint16 iport = (Juint16)CParamJsonManager::CreateInstance().m_101ParamDevInfo.mainPort;
	m_iTCP_flag = TCP_CLIENT_T;
	if (sip.compare("0.0.0.0") == 0)
	{
		m_iTCP_flag = TCP_SERVER_T;
	}

	CIIIPAddressPort localip = CIIIPAddressPort(IP, iport);
	CIIIPAddressPort ip = CIIIPAddressPort(sip.c_str(), iport);
	mskprintf("init local  ip is : (%s) \n", IP);
	mskprintf("init local port is : (%d) \n", iport);
	mskprintf("init remote ip is : (%s) \n", sip.c_str());
	IEC_LOG_RECORD(eRunType, "init tcp type(%d),localip(%s),port(%d),remoteip(%s)", m_iTCP_flag, IP, iport, sip.c_str());

	if (m_iTCP_flag == TCP_CLIENT_T) // �ͻ���ģʽ
	{
		//lyq--�����ϵͳ����ӳ��
		//CParamJsonManager::CreateInstance().MqttSetIptables(CFG_APP_NAME"/OS-system/JSON/request/setIptables",sip, iport);
		
		m_pClientSocket = new CCoreClientSocket(localip);
		m_pClientSocket->SetRemotePeerName(ip);
		m_pClientSocket->StartThread();
	}
	else // �����ģʽ
	{
		while (!m_ListenSocket.Create(CIIAsyncSocket::SOCK_TCP, localip))
		{
			ii_sleep(5000); // ��ִ���socket
		}
		IEC_LOG_RECORD(eRunType, "Create socket success.");
		while (!m_ListenSocket.Bind(localip))
		{
			ii_sleep(5000); // ��ְ�socket
		}
		IEC_LOG_RECORD(eRunType, "Bind socket success.");
		while (!m_ListenSocket.Listen())
		{
			ii_sleep(1000); // �������
		}
		IEC_LOG_RECORD(eRunType, "Listen socket success.");
	}
	m_thread.start();
}

void CItgRunDiagnotor::Exit(void)
{

	if (m_pServerSocket != NULL)
	{
		m_pServerSocket->CloseThread();
		delete m_pServerSocket;
	}
	if (m_pIstenSocket != NULL)
	{
		m_pIstenSocket->CloseThread();
		delete m_pIstenSocket;
	}
	m_serclient.close();
	m_thread.close();
	m_thread.wait_for_end();
	m_xmitQueue.clear();
	m_recvQueue.clear();
	Initialize();
}

void CItgRunDiagnotor::Attach(CIIEventSend *hAliveEvent, const CItgThreadInfo &info)
{
	m_monitor.Attach(hAliveEvent, info);
}

void CItgRunDiagnotor::Detach(CIIEventSend *hAliveEvent)
{
	m_monitor.Detach(hAliveEvent);
}

void CItgRunDiagnotor::thread_prev(void)
{
	WriteLogFile(eRunType, std::string("ģ������߳̿�ʼ����"));
}

void CItgRunDiagnotor::thread_exit(void)
{
	WriteLogFile(eRunType, std::string("ģ������߳��˳�����"));
}

void CItgRunDiagnotor::thread_func(Jint32 wait_msecs)
{
	if (m_101_protocol == NULL)
	{
		ii_sleep(1000);
		return;
	}

	m_101_protocol->OnRun();

	// mskprintf(" m_iTCP_flag = %d\n",m_iTCP_flag);
	//   ����ģʽ
	if (m_iTCP_flag == UARTMANAGER_T)
	{
		m_101_protocol->SendUartManagerFrame(m_pUartManagerSocket);
		m_101_protocol->RecvUartManagerData(m_pUartManagerSocket);
		return;
	}
	else if (m_iTCP_flag == BTMANAGER_T)
	{
		m_101_protocol->SendUartManagerFrame(m_pUartManagerSocket);
		m_101_protocol->RecvUartManagerData(m_pUartManagerSocket);
		return;
	}
	else if (m_iTCP_flag == TCP_CLIENT_T) // �ͻ���ģʽ
	{
		if (m_pClientSocket != NULL)
		{
			m_pClientSocket->ProstartSocketThread();
		}
		m_101_protocol->SendClientFrame(m_pClientSocket);
		m_101_protocol->RecvClientData(m_pClientSocket);
	}
	else if (m_iTCP_flag == TCP_SERVER_T) // ������ģʽ
	{
		// ������ģʽ
		CIITCPListenSocket::accepted_socket as;
		if (m_ListenSocket.Accept(as))
		{
			IEC_LOG_RECORD(eRunType, "Accept as(%s).", as.addr.ToString().c_str());
			m_serclient.close();
			m_serclient.wait_for_end();
			m_serclient.AttachLinkSocket(as);
		}

		m_101_protocol->SendFrame(&m_serclient);
		m_101_protocol->RecvData(&m_serclient);
	}
}

bool CItgRunDiagnotor::Send(Juint8 *pBuf, Juint32 ulCnt)
{
	CIIAutoMutex lockXmit(&object_cs_xmit);
	Juint32 ulXmitCnt(0);
	Juint32 ulLeave = m_ulDefaultQueueLen - m_xmitQueue.size();
	if (ulLeave > ulCnt)
		ulXmitCnt = ulCnt;
	else
		ulXmitCnt = ulLeave;
	m_xmitQueue.insert(m_xmitQueue.end(), pBuf, pBuf + ulXmitCnt);

	return ulXmitCnt;
}

Juint32 CItgRunDiagnotor::ContinueSendData(Juint8 *pBuf, Juint32 ulSize)
{
	CIIAutoMutex lockXmit(&object_cs_xmit);
	Juint32 ulSendSize = static_cast<Juint32>(m_xmitQueue.size());
	if (ulSendSize > 0)
	{
		if (ulSize < ulSendSize)
		{
			ulSendSize = ulSize;
		}
		for (Juint32 i = 0; i < ulSendSize; i++)
		{
			pBuf[i] = *(m_xmitQueue.begin() + i);
		}

		m_xmitQueue.erase(m_xmitQueue.begin(), m_xmitQueue.begin() + ulSendSize);
	}
	return ulSendSize;
}

void CItgRunDiagnotor::AddRecvQueue(Juint8 *pBuf, Juint32 ulCnt)
{
	mskprintf("ulCnt : %d,  m_recvQueue.size() = %d\n",ulCnt,m_recvQueue.size());
	CIIAutoMutex lockRecv(&object_cs_recv);

	if (m_recvQueue.size() < QUEUE_LEN * 2)
	{
		m_recvQueue.insert(m_recvQueue.end(), pBuf, pBuf + ulCnt);

		for (Juint32 i = 0; i < ulCnt; i++)
		{
			mskprintf(" %02x", pBuf[i]);
		}

		mskprintf("\n");
	}		
	else
	{
		CIIString str;
	}
}
CProtocol101 *CItgRunDiagnotor::GetProtocol101()
{
	return m_101_protocol; // 101Ӧ�ò�
}
void CItgRunDiagnotor::setConnectStatus(Juint8 con)
{
	m_connect = con;
	mskprintf("tcp m_connect = %d \n", m_connect);
	if (m_connect == 1)
	{
		m_101_protocol->OnLinkSucceed();
	}
	else
	{
		m_101_protocol->OnLinkFaild();
	}
}
Juint32 CItgRunDiagnotor::PackFrame(Juint8 type, Juint8 *pBuf, Jint32 len)
{
	Jint16 nlen = 0;
	Jint16 nSendCnt = 0;
	Juint8 byAppBuf[8192] = {0};
	int i = 0;

	Juint8 *pAppHead = byAppBuf;

	// ��֡ͷ
	*(pAppHead + nSendCnt++) = 0x68;
	nSendCnt = nSendCnt + 2; // ����
	*(pAppHead + nSendCnt++) = type;

	// ����
	*(Juint16 *)(pAppHead + nSendCnt) = len;
	nSendCnt = nSendCnt + 2; // ����

	memcpy(pAppHead + nSendCnt, pBuf, len);
	nSendCnt = nSendCnt + len;

	*(Juint16 *)(pAppHead + 1) = len + 2;

	if (nSendCnt > 0)
	{
		Juint16 wCheckSum = 0;
		Juint8 *pcal = pAppHead;
		for (i = 0; i < nSendCnt; i++)
		{
			wCheckSum += pcal[i] % 256;
		}
		*(pAppHead + nSendCnt++) = GETLOBYTE(wCheckSum);
		*(pAppHead + nSendCnt++) = 0x0a;
	}

	nlen = nSendCnt;
	mskprintf("PackFrame len send%d\n", nlen);

	// �ж����ӳɹ���
	Send(pAppHead, nlen);
	return nlen;
}

Juint32 CItgRunDiagnotor::Recv(Juint8 *pBuf, Juint32 ulSize)
{
	CIIAutoMutex lockRecv(&object_cs_recv);
	std::deque<Juint8>::size_type ulRecvCnt(0);
	ulRecvCnt = m_recvQueue.size();
	if (ulSize < ulRecvCnt)
	{
		ulRecvCnt = ulSize;
	}
	
	std::deque<Juint8>::iterator itor;
	itor = m_recvQueue.begin();
	for (Juint32 i = 0; i < ulRecvCnt; i++)
	{
		*(pBuf++) = *(itor++);
	}
	m_recvQueue.erase(m_recvQueue.begin(), m_recvQueue.begin() + ulRecvCnt);

	if(ulRecvCnt > 0)
	{
		mskprintf("ulRecvCnt2:%d\n",ulRecvCnt);
	}
	return static_cast<Juint32>(ulRecvCnt);
}

Juint8 *CItgRunDiagnotor::GetRecvBuf(Juint16 usPos)
{
	assert(usPos < RECVBUFLEN);
	return &m_byAssistBuf[usPos];
}

Juint16 CItgRunDiagnotor::GetRecvNum(void)
{
	return (RECVBUFLEN - m_usRecvCnt);
}

void CItgRunDiagnotor::SetRecvCnt(Juint16 usRecvCnt)
{
	assert((m_usRecvCnt + usRecvCnt <= RECVBUFLEN));
	m_usRecvCnt += usRecvCnt;
	m_usAssistCnt += usRecvCnt;
}

void CItgRunDiagnotor::ProcBuf(void)
{
	// ���ջ�����βָ��
	Juint16 usTail = m_usRecvCnt - m_usAssistCnt;
	assert(usTail >= 0 && usTail < RECVBUFLEN);

	// �����������������ݿ��������ջ�������
	memcpy(&m_byRecvBuf[usTail], m_byAssistBuf, m_usAssistCnt);

	// ��λ����������
	memset(m_byAssistBuf, 0, RECVBUFLEN);
	m_usAssistCnt = 0;
}

void CItgRunDiagnotor::AfterUnpackProc(void)
{
	// ���ջ�����δ�������ݵ�ͷָ��
	Juint16 usHead = m_usProcCnt;

	// ������ջ��������Ѿ�����������
	memcpy(m_byAssistBuf, &m_byRecvBuf[usHead], m_usRecvCnt);
	memset(m_byRecvBuf, 0, RECVBUFLEN);
	memcpy(m_byRecvBuf, m_byAssistBuf, m_usRecvCnt);
	memset(m_byAssistBuf, 0, RECVBUFLEN);

	// ��λ��־λ
	m_usProcCnt = 0;
}

void CItgRunDiagnotor::Initialize(void)
{
	m_usRecvCnt = 0;
	m_usProcCnt = 0;
	m_usAssistCnt = 0;
	m_usRecvNum = RECVBUFLEN;
	memset(m_byRecvBuf, 0, RECVBUFLEN);
	memset(m_byAssistBuf, 0, RECVBUFLEN);
}

Jint32 CItgRunDiagnotor::GetRecvBufMax(void)
{
	return RECVBUFLEN;
}
