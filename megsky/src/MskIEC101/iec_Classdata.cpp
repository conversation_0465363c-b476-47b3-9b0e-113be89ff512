//

#include "IECApl.h"
#include "iec_Classdata.h"

Jboolean ISOverTime(SMTimeInfo& time, long nOverTime)
{
	SMTimeInfo curr_ct = ii_get_current_mtime();
	Juint32 nSpanTime = (curr_ct.nDay - time.nDay)*86400 +(curr_ct.nHour - time.nHour)*3600 +(curr_ct.nMinute - time.nMinute)*60 + curr_ct.nSecond - time.nSecond;
	if (nSpanTime >= static_cast<Juint32>(nOverTime))
		return JTRUE;
	return JFALSE;
}

DATA_CELL_IDENT CAplQueryClass::GetCellIdent( )
{
	DATA_CELL_IDENT ident;
	memset(&ident, 0, sizeof(ident));
	ident.adr = m_lpIECApl->m_param.address;
	ident.cot.src_adr = m_cot_src;
	return ident;
}

