/*
 *	101��Լ����·���ƺ�����
 *
 *	1����·��֡��������֡ʱ�����ã�
 *	2����·���ƺ�������֡ʱ�����ã�
 */
////
#include "public_struct.h"
#include "IECApl.h"
#include "IEC101Protocol.h"
#include "itg_run_diagnotor.h"
#include "real_data.h"
#include "iec_101PtlLink.h"
#include <bitset>
#include "mqtt_pub_inter.h"

// ��ʼ������
void C101PtlLink::OnInitialize(const S101LinkParam &param)
{
	m_pLinkSend = &m_send_buffer; // ���·��ͻ�����
	m_dwRecvByteNum = 0;
	memset(m_byCopyBuff, 0, LNK_BUF_LEN);
	memset(m_byRecvBuff, 0, LNK_BUF_LEN);

	m_LinkParam = param;
	LinkCtrl *pLnkCtr = &m_LnkData;
	// �趨��ʱ������ֵ
	pLnkCtr->t1_U_V.SetParam(m_LinkParam.m_byT1Para * 2000);
	pLnkCtr->t2_S_ACK.SetParam(m_LinkParam.m_byT2Para * 3000);
	pLnkCtr->t3_Link_Act.SetParam(m_LinkParam.m_byT3Para * 4000);

	mskprintf("T1 = %d,T2 = %d,T3 = %d.\n", m_LinkParam.m_byT1Para, m_LinkParam.m_byT2Para, m_LinkParam.m_byT3Para);
	pLnkCtr->t1_U_V.StopCount();
	pLnkCtr->t2_S_ACK.StopCount();
	pLnkCtr->t3_Link_Act.StopCount();
	pLnkCtr->bFirstStart_T2 = 1;

	// ȡ���ϴ�ȫ��δȷ�ϵ�I֡
	pLnkCtr->SendQue.GetReSendBuf(pLnkCtr->wResendNum, pLnkCtr->vReSendQue);
	pLnkCtr->wResendCnt = 0;

	pLnkCtr->bSendUV_Start = 0;		 // ����������·��־
	pLnkCtr->bSendU_Start_ACK = 0;	 // ����U��ʽ����ȷ��֡��־��Ч
	pLnkCtr->bSendU_Test_ACK = 0;	 // ����U��ʽ����ȷ��֡��־��Ч
	pLnkCtr->state = et_link_stopdt; // ��·����ֹͣ״̬

	pLnkCtr->SendQue.Resize(param.m_wParaK); // �趨����ѭ������
	// �����ڲ��������ĳ�ֵ
	pLnkCtr->wACK = pLnkCtr->wVR = pLnkCtr->wVS = pLnkCtr->wCntW = 0;
	pLnkCtr->ApciInf_R.Fmt_I.wNoSend = 0;
	pLnkCtr->ApciInf_R.Fmt_I.wNoRecv = 0;
	pLnkCtr->ApciInf_R.Fmt_S.wNoRecv = 0;
	pLnkCtr->SendQue.Reset();

	// ��ʼ�������������ص�������
	pLnkCtr->dwRecvCnt = 0;

	memset(pLnkCtr->byRecvBuf, 0, MAX_R_BUF_SIZE);
	pLnkCtr->bySendBuf_len = 0;

	// �����ȡ
	pLnkCtr->lpRecvPtr = pLnkCtr->byRecvBuf;
	pLnkCtr->dwRecvNum = sizeof(Frm_Head); // ��һ�ζ�ȡ�̶���֡ͷ
}

void C101PtlLink::OnInitialize101(const Pdn101_LNK_PARAM &param)
{

	m_link_param = param;
	m_dwRecvByteNum = 0;
	memset(m_byCopyBuff, 0, LNK_BUF_LEN);
	memset(m_byRecvBuff, 0, LNK_BUF_LEN);

	m_send_buffer.link_lpdu_len = 0;
	memset(m_send_buffer.send_buf, 0, SEND_BUFFER_LEN);

	m_T_Timeout.SetParam(m_link_param.link_frame_timeover);
	m_T_Interval.SetParam(m_link_param.link_frame_interval);
}

// ��·������ʼ��
void C101PtlLink::ReT123() // add by tianzhenchao
{
	Juint32 t1 = CParamJsonManager::CreateInstance().m_101ParamDevInfo.t1;
	Juint32 t2 = CParamJsonManager::CreateInstance().m_101ParamDevInfo.t2;
	Juint32 t3 = CParamJsonManager::CreateInstance().m_101ParamDevInfo.t3;

	LinkCtrl *pLnkCtr = &m_LnkData;

	pLnkCtr->t1_U_V.SetParam(t1 * 2000);
	pLnkCtr->t2_S_ACK.SetParam(t2 * 3000);
	pLnkCtr->t3_Link_Act.SetParam(t3 * 4000);
}
void C101PtlLink::ReconnectInit(void)
{
	LinkCtrl *pLnkCtr = &m_LnkData;
	pLnkCtr->t1_U_V.StopCount();
	pLnkCtr->t2_S_ACK.StopCount();
	pLnkCtr->t3_Link_Act.StartCount();
	Frm_I_T1 *pTail_Frm_I_T1 = NULL;
	if (pLnkCtr->SendQue.Tail_Buf(pTail_Frm_I_T1) && pTail_Frm_I_T1)
	{
		pTail_Frm_I_T1->t1.StopCount();
	} // add by tianzhenchao 2021-9-13 17:09:58

	// ȡ���ϴ�ȫ��δȷ�ϵ�I֡
	pLnkCtr->SendQue.GetReSendBuf(pLnkCtr->wResendNum, pLnkCtr->vReSendQue);
	pLnkCtr->wResendCnt = 0;

	pLnkCtr->bSendUV_Start = m_LinkParam.m_bSelfStartLnk; // ����������·��־
	pLnkCtr->bSendU_Start_ACK = 0;						  // ����U��ʽ����ȷ��֡��־��Ч
	pLnkCtr->bSendU_Test_ACK = 0;						  // ����U��ʽ����ȷ��֡��־��Ч

	// �����ڲ��������ĳ�ֵ  ������·��ʱ���Ƿ���Ҫ��գ�// add by tianzhenchao 2021-9-13 17:09:58
	if (CParamJsonManager::CreateInstance().m_101ParamDevInfo.apl_kw_reconnect_init_b == 0)
	{
		pLnkCtr->state = et_link_stopdt; // ��·����ֹͣ״̬
										 // �����ڲ��������ĳ�ֵ
		pLnkCtr->wACK = pLnkCtr->wVR = pLnkCtr->wVS = pLnkCtr->wCntW = 0;
		pLnkCtr->ApciInf_R.Fmt_I.wNoSend = 0;
		pLnkCtr->ApciInf_R.Fmt_I.wNoRecv = 0;
		pLnkCtr->ApciInf_R.Fmt_S.wNoRecv = 0;
		pLnkCtr->SendQue.Reset();
	}
	// ��ʼ�������������ص�������
	pLnkCtr->dwRecvCnt = 0;
	memset(pLnkCtr->byRecvBuf, 0, MAX_R_BUF_SIZE);

	// �����ȡ
	pLnkCtr->lpRecvPtr = pLnkCtr->byRecvBuf;
	pLnkCtr->dwRecvNum = sizeof(Frm_Head); // ��һ�ζ�ȡ�̶���֡ͷ
}

//---------------------------------
// ��·��֡����
void C101PtlLink::EthPtl101LnkUnpack(CCoreClientSocket *pCLink, CEventConnectSocket *pLink, CUartManagerSocket *pULink)
{
	LinkCtrl *pLnkCtr = &m_LnkData;
	Juint32 len = CItgRunDiagnotor::Instance().Recv(pLnkCtr->byRecvBuf + pLnkCtr->dwRecvCnt, pLnkCtr->dwRecvNum);	
	m_dwRecvByteNum += len;

	if (len == 0 || pLnkCtr->dwRecvNum == 0)
	{ 
		// û���յ�����
		if (pLnkCtr->dwRecvNum == 0)
		{
			ResearchFrmHead();
		}
		return;
	}

	//mskprintf("\n\nEthPtl101LnkUnpack 2,len = %d,pLnkCtr->dwRecvNum = %d,LPDU_FIXED_LENGTH=%d\n\n", len, pLnkCtr->dwRecvNum,LPDU_FIXED_LENGTH);

	// ��·���ֻ״̬
	pLnkCtr->t3_Link_Act.StartCount();
	pLnkCtr->dwRecvCnt += len;
	//mskprintf("\n\nEthPtl101LnkUnpack 2,len = %d,pLnkCtr->dwRecvCnt = %d,LPDU_FIXED_LENGTH=%d\n\n", len, pLnkCtr->dwRecvCnt,LPDU_FIXED_LENGTH);

	// ����Э��
	Juint32 dwUnpackCnt(0);

	while (pLnkCtr->dwRecvCnt >= LPDU_FIXED_LENGTH)
	{		
		Juint8 *pLinkBuff = pLnkCtr->lpRecvPtr + dwUnpackCnt;
		Juint8 byCheckSum = 0;		

		if(pLinkBuff == NULL)
		{
			ResearchFrmHead();
			return;
		}

		for(int i=0; i<LPDU_FIXED_LENGTH;i++)
		{
			mskprintf("%02d ",pLinkBuff[i]);
		}

		//mskprintf("%d::::%d\n",pLinkBuff[0],pLinkBuff[LPDU_FIXED_LENGTH - 1]);
		// �̶�֡
		if (LPDU_FIXED_HEADER == pLinkBuff[0] && LPDU_FIXED_TAIL == pLinkBuff[LPDU_FIXED_LENGTH - 1])
		{
			mskprintf("--------------LPDU_FIXED_HEADER-----------------\n");
			LPDU_6 frm_6 = *(LPDU_6 *)(pLinkBuff); // ע���ַ�Ѿ��涨Ϊ2���ֽڣ�δ������
			byCheckSum = GetCheckSum(pLinkBuff + 1, LPDU_FIXED_LENGTH - 3);

			// У����ȷ
			if (pLinkBuff[LPDU_FIXED_LENGTH - 2] == byCheckSum)
			{
				LPDU_PARSE parse;
				memset(&parse, 0, sizeof(parse));
				parse.ctrl.union_ctrl.temp = frm_6.link_ctrl;
				parse.addr = frm_6.link_address;
				EthPtl101Unpack_LPDU_FIX(parse, pCLink, pLink, pULink);
				dwUnpackCnt += LPDU_FIXED_LENGTH;
				pLnkCtr->dwRecvCnt -= LPDU_FIXED_LENGTH;
				break;
			}
			else
			{
				dwUnpackCnt += LPDU_FIXED_LENGTH;
				pLnkCtr->dwRecvCnt -= LPDU_FIXED_LENGTH;
				// m_pPtl101->AddRecvFrmErrorCount(LPDU_FIXED_LENGTH);// ά��ͨ�����ʹ������
			}
		}
		// �ɱ�֡
		else if (LPCI_HEADER == pLinkBuff[0] && LPCI_HEADER == pLinkBuff[3] && pLinkBuff[1] == pLinkBuff[2])
		{
			// Ԥ�ƿɱ�֡����
			mskprintf("--------------LPCI_HEADER-----------------\n");
			LPCI_HEADER_7 frmhead = *(LPCI_HEADER_7 *)(pLinkBuff);
			Juint16 usLength = sizeof(LPCI_HEADER_7) - 3 + frmhead.len1 + 1 + 1;

			mskprintf("--------------usLength:%d  dwUnpackCnt= %d\n", usLength, dwUnpackCnt);

			for (int i = 0; i < usLength; i++)
			{
				mskprintf("%d ", pLinkBuff[i]);
			}

			Juint8 byTypeIdent =  pLinkBuff[7];

			mskprintf("\n--byTypeIdent:%d \n", byTypeIdent);
			//�ж������������Զ�����Ϣ
			//ȷ���Ƿ����Զ���������ָ��  ���жϼ�Уλ�� ��Уλ256 ���
			if(byTypeIdent == 155 || byTypeIdent == 156 || byTypeIdent == 157 || byTypeIdent == 158 || byTypeIdent == 159 || byTypeIdent == 160
				 || byTypeIdent == 161 || byTypeIdent == 162 || byTypeIdent == 163 || byTypeIdent == 164 || byTypeIdent == 165 || byTypeIdent == 166|| byTypeIdent == 167|| byTypeIdent == 168|| byTypeIdent == 169 )
			{

				/* if(byTypeIdent == 155 || byTypeIdent == 156 || byTypeIdent == 159|| byTypeIdent == 161 || byTypeIdent == 164 || byTypeIdent == 165 || byTypeIdent == 166 )
				{
					CParamJsonManager::CreateInstance().MqttGetDevInfo2btManager(byTypeIdent,pLinkBuff,len);
				}
				else
				{
					CParamJsonManager::CreateInstance().MqttGetDevInfo2btManager(byTypeIdent);	
				} */
				CParamJsonManager::CreateInstance().MqttGetDevInfo2btManager(byTypeIdent,pLinkBuff,len);
				ResearchFrmHead();
				dwUnpackCnt += usLength;
				m_dwRecvByteNum -= usLength;				
				return;		
			}
		

			if (m_dwRecvByteNum >= usLength && LPCI_TAIL == pLinkBuff[usLength - 1])
			{
				byCheckSum = GetCheckSum(pLinkBuff + sizeof(LPCI_HEADER_7) - 3, frmhead.len1);
				mskprintf("\nbyCheckSum:%d  pLinkBuff[usLength - 2]= %d\n", byCheckSum, pLinkBuff[usLength - 2]);

				// У����ȷ
				if (pLinkBuff[usLength - 2] == byCheckSum)
				{
					LPDU_PARSE parse;
					memset(&parse, 0, sizeof(parse));

					parse.ctrl.union_ctrl.temp = frmhead.link_ctrl;
					parse.addr = frmhead.link_address;

					parse.lpdata = pLinkBuff + sizeof(LPCI_HEADER_7);
					parse.len_asdu = frmhead.len1 - 3; //

					EthPtl101Unpack_LPDU_VAR(parse, pCLink, pLink, pULink);

					dwUnpackCnt += usLength;
					m_dwRecvByteNum -= usLength;
					break;
				}
				else
				{
					dwUnpackCnt += usLength;
					m_dwRecvByteNum -= usLength;
					// m_pPtl101->AddRecvFrmErrorCount(usLength);// ά��ͨ�����ʹ������
				}
			}
			else
			{
				// ����������1.�������ֽ����ڱ��ν�֡���̣�2.���к���������֡�Ĳ����ֽ�����
				if (dwUnpackCnt > 0 && m_dwRecvByteNum > 0)
				{
					memcpy(m_byCopyBuff, m_byRecvBuff + dwUnpackCnt, m_dwRecvByteNum);
					memcpy(m_byRecvBuff, m_byCopyBuff, m_dwRecvByteNum);
					break;
				}
				else
				{
					dwUnpackCnt += 1;
					m_dwRecvByteNum -= 1;
				}
			}
		}
		else
		{
			mskprintf("--------------OTHER-----------------\n");
			dwUnpackCnt = 0;
			pLnkCtr->dwRecvCnt = 0;
		}
	}

	ResearchFrmHead();
	return;
}

//---------------------------------
// 101��·��֡����
void C101PtlLink::EthPtl101LnkPack(CCoreClientSocket *pCLink, CEventConnectSocket *pLink, CUartManagerSocket *pULink)
{
	LinkCtrl *pLnkCtr = &m_LnkData;
	Juint8 *lpSendBuffer = NULL;
	Juint32 dwSendByteNumber = 0;

	lpSendBuffer = pLnkCtr->bySendBuf;
	dwSendByteNumber = pLnkCtr->bySendBuf_len;

	//mskprintf("dwSendByteNumber = %d\n\r",dwSendByteNumber);
	// �ɹ���֡���ύ��Լ���������Ͳ���֡��ʾ����
	if (dwSendByteNumber > 0 && NULL != lpSendBuffer)
	{
		mskprintf("connectType:%d\n\r",CParamJsonManager::CreateInstance().m_101ParamDevInfo.connectType);
		if (pULink == NULL)
		{
			mskprintf("Framing successfully, submit protocol adapter to send and frame display data\n\r");
			/* mskprintf("m_link_state:%d, m_pLinkSend>link_lpdu_len = %d\n\r", m_link_state,m_pLinkSend->link_lpdu_len);
			for (int i = 0; i < dwSendByteNumber; i++)
			{
				mskprintf("%02x ", m_pLinkSend->send_buf[i]);
			}
			mskprintf("\n");

			for (int i = 0; i < dwSendByteNumber; i++)
			{
				mskprintf("%02x ", pLnkCtr->bySendBuf[i]);
			}
			mskprintf("\n");*/

			mskprintf("%02x,%d\n",pLnkCtr->bySendBuf[0],dwSendByteNumber);
						
			if(dwSendByteNumber > 6)
			{
				CItgRunDiagnotor::Instance().Send(m_pLinkSend->send_buf, pLnkCtr->bySendBuf_len);				
			}
			else
			{
				CItgRunDiagnotor::Instance().Send(pLnkCtr->bySendBuf, dwSendByteNumber);
			}		
			m_pLinkSend->link_lpdu_len = 0;	
			pLnkCtr->bySendBuf_len = 0;
		}
		//����
		else if(CParamJsonManager::CreateInstance().m_101ParamDevInfo.connectType == BTMANAGER_T)
		{
			mskprintf("%02x,%d\n",pLnkCtr->bySendBuf[0],dwSendByteNumber);
						
			// Mqtt���з���
			mskprintf("Framing successfully, MQTT send\n\r");
			if(dwSendByteNumber > 6)
			{
				CMqttClientInterManager::CreateInstance().MqttPackBtManagerManagerFill(m_pLinkSend->send_buf, pLnkCtr->bySendBuf_len);			
			}
			else
			{
				CMqttClientInterManager::CreateInstance().MqttPackBtManagerManagerFill(pLnkCtr->bySendBuf, dwSendByteNumber);
			}		
			m_pLinkSend->link_lpdu_len = 0;	
			pLnkCtr->bySendBuf_len = 0;
		}
		//����
		else if(CParamJsonManager::CreateInstance().m_101ParamDevInfo.connectType == 1
				||CParamJsonManager::CreateInstance().m_101ParamDevInfo.connectType == 2)// ���� ֱ�ӷ�����MQTTma'k
		{
			// Mqtt���з���
			mskprintf("Framing successfully, MQTT send\n\r");
			if(dwSendByteNumber > 6)
			{
				CMqttClientInterManager::CreateInstance().MqttPackUartManagerFill(m_pLinkSend->send_buf, pLnkCtr->bySendBuf_len);			
			}
			else
			{
				CMqttClientInterManager::CreateInstance().MqttPackUartManagerFill(pLnkCtr->bySendBuf, dwSendByteNumber);
			}		
			m_pLinkSend->link_lpdu_len = 0;	
			pLnkCtr->bySendBuf_len = 0;
		}
	}
	else
	{
		// �̶�֡��ʼ��ʱ ��������
		LINK_CONTROL linkCtrl;
		linkCtrl.SetFlag(1,1);
		
		if (m_link_state == USLS_Reset_Ack_L)
		{
			memset(&linkCtrl, 0, sizeof(linkCtrl));
			linkCtrl.SetValue(0xC9); // BSC_ACK �϶�ȷ��
			EthPtl101LnkPackLPDU_FIX(m_pLinkSend, linkCtrl, &m_link_param);
			m_link_state = USLS_State_Ask_R;
			m_frame_state = Wait_Link_State;
			m_T_Timeout.StartCount();
			m_ucSameFCBCnt = 0;
		}
		else if (m_link_state == USLS_State_Ack_R)
		{
			memset(&linkCtrl, 0, sizeof(linkCtrl));
			linkCtrl.SetValue(0xC0); // BSC_ACK �϶�ȷ��
			EthPtl101LnkPackLPDU_FIX(m_pLinkSend, linkCtrl, &m_link_param);
			m_link_state = USLS_Reset_Ask_R;
			m_frame_state = Wait_Reset_Result;
			m_T_Timeout.StartCount();
		}
		// �ɱ�֡
		else if (USLS_LinkOK == m_link_state)
		{
			
			Juint8 *lpdata = 0; // pLnkCtr->bySendBuf;
			Juint8 max_len_lpdu = 0;
			GetASDUBuffer(m_pLinkSend, &m_link_param, lpdata, max_len_lpdu);

			Juint8 byResendNoAck = 0; // ����IEC-101��Ӧ�ò�:101��·���I֡��δ��ȷ�����ط�

			CEPTime ct = CEPTime::LocalTime();
			
			Juint8 byASDULen = m_lpIECApl->RequestClass1(lpdata, max_len_lpdu, byResendNoAck);
			
			lpSendBuffer = lpdata;			

			if (byASDULen != 0)
			{
				memset(&linkCtrl, 0, sizeof(linkCtrl));
				linkCtrl.SetValue(0xD3); // BSC_ACK �϶�ȷ��
				linkCtrl.union_ctrl.fcb_acd = m_localfcb;
				m_localfcb ^= 1;

				mskprintf("-------------------::2\n");
				EthPtl101LnkPackLPDU_VAR(m_pLinkSend, linkCtrl, &m_link_param, byASDULen);			
				//memcpy(pLnkCtr->bySendBuf,m_pLinkSend->send_buf,m_pLinkSend->link_lpdu_len);
				pLnkCtr->bySendBuf_len = m_pLinkSend->link_lpdu_len;								

				m_frame_state = Wait_Ack;
				m_T_Timeout.StartCount();
			}
		}
	}
}

void C101PtlLink::GetASDUBuffer(PLINK_SEND_BUFFER lpSendBuf, const PLINK_PARAM lpLinkParam, Juint8 *&lpdata, Juint8 &max_len_lpdu)
{
	Juint8 byLPCIHeaderLen = ((2 == lpLinkParam->link_address_len) ? 7 : 6);
	lpdata = lpSendBuf->send_buf + byLPCIHeaderLen;
	max_len_lpdu = static_cast<Juint8>(lpLinkParam->link_lpdu_len - byLPCIHeaderLen - 2);
}

void C101PtlLink::Reset(CCoreClientSocket *pClientCLink, CEventConnectSocket *pServerLink)
{	
		LinkCtrl *pLnkCtr = &m_LnkData;
		if (pClientCLink != NULL)
		{
			 //pClientCLink->CloseThread(); // �ر�����ͨ��
		}

		if (pServerLink != NULL)
		{
			 //pServerLink->close(); // �ر�����ͨ��
		}

		// ��·��������ʼ��
		pLnkCtr->SendQue.Reset();
		pLnkCtr->t1_U_V.StopCount();
		pLnkCtr->t2_S_ACK.StopCount();
		pLnkCtr->t3_Link_Act.StopCount();
		pLnkCtr->state = et_link_closed;
		pLnkCtr->bySendBuf_len = 0;

		m_fcb = 1;
		m_link_state = USLS_Linkinit;
		m_frame_state = eNullNull;
}

LinkCtrl C101PtlLink::GetLinkParam() const
{
	return m_LnkData;
}

// ��������֡ͷ
void C101PtlLink::ResearchFrmHead()
{
	LinkCtrl *pLnkCtr = &m_LnkData;

	pLnkCtr->dwRecvCnt = 0;
	pLnkCtr->dwRecvNum = 5120; // sizeof(Frm_Head);
	pLnkCtr->lpRecvPtr = pLnkCtr->byRecvBuf;
}

// У���
Juint8 C101PtlLink::GetCheckSum(Juint8 *lpbuf, Juint32 len)
{
	Juint8 bySum = 0;
	while (len-- > 0)
	{
		bySum += *(lpbuf++);
	}

	return bySum;
}

// ����ĵ�linkAddr.
Juint32 C101PtlLink::PackLinkAddr(Juint8 *lpBuf, const PLINK_PARAM lpLinkParam)
{
	if (2 == lpLinkParam->link_address_len)
	{
		REFERENCE<Juint16>(lpBuf) = lpLinkParam->link_address;
		return (sizeof(Juint16));
	}
	else
	{
		*lpBuf = static_cast<Juint8>(lpLinkParam->link_address);
		return (sizeof(Juint8));
	}
}

Juint32 C101PtlLink::GetRecvNum(CCoreClientSocket *pCLink, CEventConnectSocket *pLink, CUartManagerSocket *pULink)
{
	return LNK_BUF_LEN - m_dwRecvByteNum;
}

// 101�����յ��Ĺ̶�֡��
void C101PtlLink::EthPtl101Unpack_LPDU_FIX(LPDU_PARSE parse, CCoreClientSocket *pCLink, CEventConnectSocket *pLink, CUartManagerSocket *pULink)
{
	LINK_CONTROL linkCtrl;
	memset(&linkCtrl, 0, sizeof(linkCtrl));

	mskprintf("parse.addr= %d   linkAddr.: %d\n", parse.addr,CParamJsonManager::CreateInstance().m_101ParamDevInfo.linkAddr);

	if (parse.addr != CParamJsonManager::CreateInstance().m_101ParamDevInfo.linkAddr && parse.addr != 65535)
		return;

/* 	if (parse.ctrl.union_ctrl.dir_rese) // dir_rese = 1����
		return; */

	

 	mskprintf("parse.ctrl.union_ctrl.temp: %d\n", parse.ctrl.union_ctrl.temp);
	mskprintf("parse.ctrl.union_ctrl.fcv_dfc: %d\n", parse.ctrl.union_ctrl.fcv_dfc);
	mskprintf("parse.ctrl.union_ctrl.fun_code: %d\n", parse.ctrl.union_ctrl.fun_code);
	mskprintf("m_link_state: %d  m_frame_state = %d\n", m_link_state,m_frame_state);

	if (parse.ctrl.union_ctrl.temp == 0) // ����һ��ȷ��֡������������ȷ��Ҳ����������ȷ��
	{
		if (USLS_Reset_Ask_R == m_link_state)
		{
			// ����IEDͨѶ״̬�������ͨѶ״̬
			m_pPtl101->OnLinkSucceed();
			m_link_state = USLS_LinkOK;
			m_frame_state = eNullNull;
			m_T_Timeout.StopCount();
			return;
		}
		else
		{
			if (m_frame_state != eNullNull)
			{
				m_frame_state = eNullNull;
				m_T_Timeout.StopCount();
				return;
			}
			else
				return;
		}
	}

	mskprintf("parse.ctrl.union_ctrl.dir_rese = %d\n", parse.ctrl.union_ctrl.dir_rese);
	
	if (parse.ctrl.union_ctrl.dir_rese) // dir_rese = 1����
	{
		linkCtrl.SetFlag(1,1);
	}
	else
	{
		linkCtrl.SetFlag(1,1);
	}


	if (parse.ctrl.union_ctrl.fcv_dfc) // ����λ����
	{
		mskprintf("parse.ctrl.union_ctrl.fcb_acd = %d,  m_fcb: %d\n", parse.ctrl.union_ctrl.fcb_acd, m_fcb);
		if (parse.ctrl.union_ctrl.fcb_acd == m_fcb) // �ظ����� ˵���Է�û���յ�����Ҫ�ط�
		{
			EthPtl101LnkPackLPDU_FIX(m_pLinkSend, linkCtrl, &m_link_param);
			m_ucSameFCBCnt++;
			if (m_ucSameFCBCnt >= 2)
			{ // �ط�3�����ϣ���Ϊ��·���жϣ�����·��״̬Ϊ��δ��λ��

				mskprintf("\n\nm_link_state\n\n");
				m_link_state = USLS_Linkinit;
				m_ucSameFCBCnt = 0;
			}
			return;
		}
		else
		{
			m_ucSameFCBCnt = 0;
			m_fcb ^= 1;
			mskprintf("parse.ctrl.union_ctrl.fcb_acd = %d,  m_fcb: %d\n", parse.ctrl.union_ctrl.fcb_acd, m_fcb);
		}
	}

	if (USLS_LinkOK == m_link_state) // ��·����״̬
	{
		switch (parse.ctrl.union_ctrl.fun_code)
		{

		case BPC_REQ_LINK:			 // ������Ӧ��·״̬
			linkCtrl.SetValue(0x8B); // BSC_ACK �϶�ȷ��
			EthPtl101LnkPackLPDU_FIX(m_pLinkSend, linkCtrl, &m_link_param);
			m_link_state = USLS_State_Ack_L;
			m_T_Timeout.StopCount();
			m_frame_state = eNullNull;
			m_ucSameFCBCnt = 0;
			break;

		case BSC_STATUS: // ��·״̬�ظ�
			m_frame_state = eNullNull;
			m_T_Timeout.StopCount();
			break;

		case BPC_TEST_STATUS: // ��·����
			mskprintf("m_frame_state = %d\n",m_frame_state);
			if (m_frame_state == eNullNull)
			{
				linkCtrl.SetValue(0x80); // BSC_ACK �϶�ȷ��
				EthPtl101LnkPackLPDU_FIX(m_pLinkSend, linkCtrl, &m_link_param);
			}			
			break;

		default: // ��Ч�����봦��
			break;
		}
	}
	else // ��·δ��λ
	{
		switch (parse.ctrl.union_ctrl.fun_code)
		{
		case BPC_REQ_LINK:			 // ������Ӧ��·״̬
			linkCtrl.SetValue(0x8B); // BSC_ACK �϶�ȷ��
			EthPtl101LnkPackLPDU_FIX(m_pLinkSend, linkCtrl, &m_link_param);
			m_link_state = USLS_State_Ack_L;
			m_T_Timeout.StopCount();
			m_frame_state = eNullNull;
			m_ucSameFCBCnt = 0;
			break;

		case BPC_RESET_LINK: // ��λ��·��
			m_fcb = 1;
			m_localfcb = 1;
			Reset(pCLink, pLink);			
			linkCtrl.SetValue(0x80); // BSC_ACK �϶�ȷ��
			EthPtl101LnkPackLPDU_FIX(m_pLinkSend, linkCtrl, &m_link_param);
			m_link_state = USLS_Reset_Ack_L;
			m_T_Timeout.StopCount();
			m_frame_state = eNullNull;
			m_ucSameFCBCnt = 0;
			break;

		case BSC_STATUS: // ��·״̬�ظ�
			m_link_state = USLS_State_Ack_R;
			m_frame_state = eNullNull;
			m_T_Timeout.StopCount();
			break;

		default: // ��Ч�����봦��
			break;
		}
	}
}
// 101�����յ��Ŀɱ�֡��
void C101PtlLink::EthPtl101Unpack_LPDU_VAR(LPDU_PARSE parse, CCoreClientSocket *pCLink, CEventConnectSocket *pLink, CUartManagerSocket *pULink)
{
	mskprintf("\n\n\n101-EthPtl101Unpack_LPDU_VAR\n\n");
	LINK_CONTROL linkCtrl;
	memset(&linkCtrl, 0, sizeof(linkCtrl));

 	mskprintf("m_link_state:%d\n", m_link_state);
	mskprintf("parse.addr:%d\n", parse.addr);
	mskprintf("parse.ctrl.union_ctrl.dir_rese:%d\n", parse.ctrl.union_ctrl.dir_rese);
	mskprintf("parse.ctrl.union_ctrl.fun_code:%d\n", parse.ctrl.union_ctrl.fun_code);
	mskprintf("parse.ctrl.union_ctrl.fcv_dfc:%d\n", parse.ctrl.union_ctrl.fcv_dfc);
	mskprintf("fcb_acd = %d: m_fcb = %d\n", parse.ctrl.union_ctrl.fcb_acd , m_fcb); 	

	if (USLS_LinkOK == m_link_state) // ��·����״̬
	{
		if (parse.addr != CParamJsonManager::CreateInstance().m_101ParamDevInfo.linkAddr)
			return;

		if (parse.ctrl.union_ctrl.dir_rese) // dir_rese = 1����
			return;


		if (parse.ctrl.union_ctrl.fcv_dfc)		// ����λ����
		{	
			if (parse.ctrl.union_ctrl.fcb_acd == m_fcb)  // �ظ�����
			{
				//SendLPDU(m_pLinkSend, &m_link_param, pLink, m_pPtl101);
				//EthPtl101LnkPackLPDU_VAR(m_pLinkSend, linkCtrl, &m_link_param, m_pLinkSend->link_lpdu_len);	
				//return;
			}
			else
				m_fcb ^= 1;
		}

		
		mskprintf("fun_code = %d\n", parse.ctrl.union_ctrl.fun_code); 	
		

		switch (parse.ctrl.union_ctrl.fun_code)
		{
		case BPC_SEND_CONFIRM: // �������ݣ�����/ȷ��
			
			linkCtrl.SetValue(0x80); // BSC_ACK �϶�ȷ��
			EthPtl101LnkPackLPDU_FIX(m_pLinkSend, linkCtrl, &m_link_param);
			mskprintf("before UnpackASDU \n");	
			m_lpIECApl->UnpackASDU(parse.lpdata, parse.len_asdu);
			break;
			
		default: 
			break;
		}
	}
	// ��·δ��λ
	else if (USLS_Linkinit == m_link_state)
	{
		// ����������
		m_pLinkSend->link_lpdu_len = 0;
	}
}

// ��·����STARTDTʱ����λ��·���ƿ���ڲ��������ͷ��ͻ�����
void C101PtlLink::StartDT_ResetLinkCtrl(LinkCtrl *pLnkCtr)
{
	if (!pLnkCtr)
		return;
	IEC_LOG_RECORD(eDebugType, "Start DT Reset Link Ctrl!");
	pLnkCtr->wACK = 0;
	pLnkCtr->wVR = 0;
	pLnkCtr->wVS = 0;
	pLnkCtr->wCntW = 0;
	pLnkCtr->ApciInf_R.Fmt_I.wNoSend = 0;
	pLnkCtr->ApciInf_R.Fmt_I.wNoRecv = 0;
	pLnkCtr->ApciInf_R.Fmt_S.wNoRecv = 0;
	pLnkCtr->SendQue.Reset();
}


// ��·��֡����-FIX
void C101PtlLink::EthPtl101LnkPackLPDU_FIX(PLINK_SEND_BUFFER lpSendBuf, LINK_CONTROL linkCtrl, const PLINK_PARAM lpLinkParam)
{	
	LinkCtrl *pLnkCtr = &m_LnkData;
	Juint8 *lpSend = pLnkCtr->bySendBuf;
	//Juint8 *lpSend = lpSendBuf->send_buf;
	
	Juint8 byLen = ((2 == lpLinkParam->link_address_len) ? 3 : 2); // byLen�ǿ�����͵�ַ��ĳ��Ⱥ�

	Juint32 dwCnt = 0; // lpdu����

	lpSend[dwCnt++] = LPDU_FIXED_HEADER;				  // lpduͷ
	REFERENCE<LINK_CONTROL>(lpSend + dwCnt++) = linkCtrl; // ������	
	dwCnt += PackLinkAddr(lpSend + dwCnt, lpLinkParam);	  // ��ַ��
	lpSend[dwCnt++] = GetCheckSum(lpSend + 1, byLen);	  // У��� ���������͵�ַ��
	lpSend[dwCnt++] = LPDU_FIXED_TAIL;					  // lpduβ
	lpSendBuf->link_lpdu_len = dwCnt;					  // lpdu����
	mskprintf("\n\ndir_rese = %d fcb_acd=%d fcv_dfc=%d prm=%d fun_code=%d ,dwCnt =%d\n\n",linkCtrl.union_ctrl.dir_rese,linkCtrl.union_ctrl.fcb_acd,linkCtrl.union_ctrl.fcv_dfc,linkCtrl.union_ctrl.prm,linkCtrl.union_ctrl.fun_code,dwCnt);
	
	for(Juint32 i=0;i<dwCnt;i++)
	{
		mskprintf("%02x ",lpSend[i]);
	}
	mskprintf("\n");
	pLnkCtr->bySendBuf_len = dwCnt; // lpdu����
}

// ��·��֡����-VAR
void C101PtlLink::EthPtl101LnkPackLPDU_VAR(PLINK_SEND_BUFFER lpSendBuf, LINK_CONTROL linkCtrl, const PLINK_PARAM lpLinkParam, Juint8 byASDULen)
{
	if (0 == byASDULen)
	{
		EthPtl101LnkPackLPDU_FIX(lpSendBuf, linkCtrl, lpLinkParam);
	}
	else
	{
		Juint8 *lpSend = lpSendBuf->send_buf;
		Juint8 byLen = ((2 == lpLinkParam->link_address_len) ? 3 : 2) + byASDULen;
		Juint32 dwCnt = 0;
		lpSend[dwCnt++] = LPCI_HEADER;
		lpSend[dwCnt++] = byLen;
		lpSend[dwCnt++] = byLen;
		lpSend[dwCnt++] = LPCI_HEADER;

		REFERENCE<LINK_CONTROL>(lpSend + dwCnt++) = linkCtrl;
		//mskprintf("\n\ndir_rese = %d fcb_acd=%d fcv_dfc=%d prm=%d fun_code=%d\n\n",linkCtrl.union_ctrl.dir_rese,linkCtrl.union_ctrl.fcb_acd,linkCtrl.union_ctrl.fcv_dfc,linkCtrl.union_ctrl.prm,linkCtrl.union_ctrl.fun_code);

		dwCnt += PackLinkAddr(lpSend + dwCnt, lpLinkParam);

		dwCnt += byASDULen;

		lpSend[dwCnt++] = GetCheckSum(lpSend + 4, byLen);
		lpSend[dwCnt++] = LPCI_TAIL;
		lpSendBuf->link_lpdu_len = dwCnt;
	}
}

// APCI��Ϣת��Ϊ��׼��֡ͷ
Juint8 C101PtlLink::ApciInfToFrmHead(APCIInf *pInf, Frm_Head *pFrmHead)
{
	Juint8 bRet = 1;
	assert(pInf && pFrmHead);
	if (!pInf || !pFrmHead)
		return !bRet;

	Juint16 wTemp;
	memset(pFrmHead, 0, sizeof(Frm_Head));
	pFrmHead->m_byHead = 0x68;
	pFrmHead->m_byLen = 4;
	switch (pInf->eType)
	{
	case eAPCIFmt_I:
		wTemp = pInf->Fmt_I.wNoSend << 1;
		pFrmHead->m_byCtrlFiled[0] = GETLOBYTE(wTemp);
		pFrmHead->m_byCtrlFiled[1] = GETHIBYTE(wTemp);
		wTemp = pInf->Fmt_I.wNoRecv << 1;
		pFrmHead->m_byCtrlFiled[2] = GETLOBYTE(wTemp);
		pFrmHead->m_byCtrlFiled[3] = GETHIBYTE(wTemp);
		pFrmHead->m_byLen += pInf->byASDULen;
		break;
	case eAPCIFmt_S:
		pFrmHead->m_byCtrlFiled[0] = 1;
		wTemp = pInf->Fmt_S.wNoRecv << 1;
		pFrmHead->m_byCtrlFiled[2] = GETLOBYTE(wTemp);
		pFrmHead->m_byCtrlFiled[3] = GETHIBYTE(wTemp);
		break;
	case eAPCIFmt_U:
		pFrmHead->m_byCtrlFiled[0] = 3;
		if (pInf->Fmt_U.byCmdType == eU_Start) // STARTDT
		{
			if (pInf->Fmt_U.byVldAck)
				pFrmHead->m_byCtrlFiled[0] |= 0x04; // 0100
			else
				pFrmHead->m_byCtrlFiled[0] |= 0x08; // 1000
		}
		else if (pInf->Fmt_U.byCmdType == eU_Stop) // STOPDT
		{
			if (pInf->Fmt_U.byVldAck)
				pFrmHead->m_byCtrlFiled[0] |= 0x10; // 00010000
			else
				pFrmHead->m_byCtrlFiled[0] |= 0x20; // 00100000
		}
		else if (pInf->Fmt_U.byCmdType == eU_Test) // TESTFR
		{
			if (pInf->Fmt_U.byVldAck)
				pFrmHead->m_byCtrlFiled[0] |= 0x40; // 01000000
			else
				pFrmHead->m_byCtrlFiled[0] |= 0x80; // 10000000
		}
		else
		{
			bRet = 0;
			IEC_LOG_RECORD(eDebugType, "ApciInfToFrmHead eU_Start error type = %d", pInf->Fmt_U.byCmdType);
		}
		break;
	default:
		bRet = 0;
		IEC_LOG_RECORD(eDebugType, "pInf->eType error = %d", pInf->eType);
		break;
	}

	return bRet;
}
// ��׼��֡ͷת��ΪAPCI��Ϣ
Juint8 C101PtlLink::FrmHeadToApciInf(Frm_Head *pFrmhead, APCIInf &inf)
{
	Juint8 bRet = 1;
	assert(pFrmhead);
	if (!pFrmhead)
		return !bRet;
	// ��ʼ��
	if (pFrmhead->m_byHead != 0x68)
		return !bRet;
	// ���ȣ����253�ֽڣ�
	if ((pFrmhead->m_byLen > 253) || (pFrmhead->m_byLen < 4))
		return !bRet;

	Juint16 wTemp = 0;
	// I��ʽ
	if ((pFrmhead->m_byCtrlFiled[0] & 0x01) == 0)
	{
		inf.eType = eAPCIFmt_I;
		wTemp = pFrmhead->m_byCtrlFiled[0];
		wTemp += ((Juint16)(pFrmhead->m_byCtrlFiled[1]) << 8);
		inf.Fmt_I.wNoSend = wTemp >> 1;
		wTemp = pFrmhead->m_byCtrlFiled[2];
		wTemp += ((Juint16)(pFrmhead->m_byCtrlFiled[3]) << 8);
		inf.Fmt_I.wNoRecv = wTemp >> 1;
		inf.byASDULen = pFrmhead->m_byLen - 4;
	}
	// S��ʽ
	else if ((pFrmhead->m_byCtrlFiled[0] & 0x02) == 0)
	{
		inf.eType = eAPCIFmt_S;
		wTemp = pFrmhead->m_byCtrlFiled[2];
		wTemp += ((Juint16)(pFrmhead->m_byCtrlFiled[3]) << 8);
		inf.Fmt_S.wNoRecv = wTemp >> 1;
		inf.byASDULen = 0;
	}
	// U��ʽ
	else
	{
		inf.eType = eAPCIFmt_U;
		// ���λ��1�Ƿ���Ч
		Juint8 byBit = 0x04; // 0100
		Juint8 byBitNum = 0;
		Jint32 nValidbit = 0;
		for (Jint32 i = 0; i < 6; i++)
		{
			if (pFrmhead->m_byCtrlFiled[0] & byBit)
			{
				nValidbit = i;
				byBitNum++;
			}
			byBit <<= 1;
		}
		if (byBitNum > 1) // ֻ��һλ��Ч
			bRet = 0;
		else
		{
			inf.Fmt_U.byCmdType = nValidbit / 2;   // ��ͣ��
			inf.Fmt_U.byVldAck = !(nValidbit % 2); // ȷ0��1
			inf.byASDULen = 0;
		}
	}

	return bRet;
}

// ��16����ת���ɹ̶�֡���ṹ��
ControlFrameStruct C101PtlLink::Hexsrc2ControlFrameStruct(unsigned char *Hexsrc)
{
	ControlFrameStruct CFS;
	bitset<8> bit(Hexsrc[1]); // ������C 8bit

	mskprintf("C :%02X\n", Hexsrc[1]);

	cout << bit << endl;
	// ��ֵ
	CFS.RES = bit[7];
	CFS.PRM = bit[6];
	CFS.FCB_acd = bit[5];
	CFS.FCV_dfc = bit[4];

	// ������
	bit[7] = 0;
	bit[6] = 0;
	bit[5] = 0;
	bit[4] = 0;
	CFS.FC = bit.to_ulong();
	mskprintf("s_data.FC: %d \n", CFS.FC);
	CFS.Addr[0] = Hexsrc[2];
	CFS.Addr[1] = Hexsrc[3];
	return CFS;
}
