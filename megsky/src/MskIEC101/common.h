#ifndef __COMMON_H_
#define __COMMON_H_

#include "jansson/jansson.h"
#include <sys/types.h>
#include <sys/stat.h>
#include "tzc_std.h"
#include <unistd.h>


#ifdef __cplusplus
extern "C" {
#endif

const char *common_json_get_string(json_t *object, const char *key);

bool common_json_get_data_string(char *str, json_t *object, const char *key);
bool common_json_get_data_int(json_int_t *data, json_t *object, const char *key);
bool common_json_get_data_real(double *data, json_t *object, const char *key);
bool common_json_get_data_time(SMTimeInfo *time,json_t *object, const char *key);
bool common_json_get_data_time2(SMTimeInfo *time,json_t *object, const char *key);
bool common_json_get_data_hex(json_int_t *data, json_t *object, const char *key);

bool common_str2time(SMTimeInfo *time, const char *timestr);

std::string getSubnetMask();
int folder_mkdirs(const char *folder_path);
bool check_file_exists(const char *path);
int copyFile(std::string ors, std::string dst);


std::string hex_to_string(const std::string& str);

#ifdef  __cplusplus
}
#endif

#endif /* __COMMON_H_ */