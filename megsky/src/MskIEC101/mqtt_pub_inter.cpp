#include "param_json.h"
#include "real_data.h"
#include "mqtt_pub_inter.h"
#include "iec_fileStorage.h"
#include "base64.h"
#include "tiny_base64.h"

volatile MQTTClient_deliveryToken deliveredtoken_inter;
MQTTClient_connectOptions m_conn_opts_inter = MQTTClient_connectOptions_initializer;

CMqttClientInterThread::CMqttClientInterThread(void)
    : m_pMng(NULL)
{
}

CMqttClientInterThread::~CMqttClientInterThread()
{
}

void CMqttClientInterThread::SetPackageAcquireManager(CMqttClientInterManager *p)
{
    m_pMng = p;
}

void CMqttClientInterThread::run(void)
{
    m_pMng->thread_prev();
    while (CSL_thread::IsEnableRun())
    {
        m_pMng->thread_func();
    }
    m_pMng->thread_exit();
}

std::string CMqttClientInterThread::ThreadName(void) const
{
    return std::string("MQTT �ڲ�Broker�߳�");
}

void CMqttClientInterThread::RecordLog(const std::string &sMsg)
{
}

//***************************************************************
// ���Ļ�ȡ������
//***************************************************************
CMqttClientInterManager &CMqttClientInterManager::CreateInstance()
{
    static CMqttClientInterManager Mng;
    return Mng;
}

void CMqttClientInterManager::agent_Delivered(void *context, MQTTClient_deliveryToken dt)
{
    // SGDEV_INFO(SYSLOG_LOG, SGDEV_MODULE, "Message with token value %d delivery confirmed.", dt);
    deliveredtoken_inter = dt;
}

void CMqttClientInterManager::agent_ConnLost(void *context, char *cause)
{
    if (cause != NULL)
    {
        IEC_LOG_RECORD(eErrType, "connLost(cause = %s).", cause);
        CMqttClientInterManager::CreateInstance().m_connectFlag = false;
    }
}

bool CMqttClientInterManager::Init(void)
{    
    bool bRet(false);
    CIIPara myParam;
    std::string sTempValue;

    m_topics.clear();

    // ������Ϣ��ѯ---------------------------------����
    m_topics.push_back("uartManager/" CFG_APP_NAME "/JSON/get/response/+/uartInfo");
    // ���������շ��ӿ�
    m_topics.push_back("uartManager/" CFG_APP_NAME "/JSON/transparant/notification/+/data");
    m_topics.push_back("uartManager/" CFG_APP_NAME "/JSON/transparant/notification/#");
    // ����ģʽ�޸�
    m_topics.push_back("uartManager/" CFG_APP_NAME "/JSON/set/response/+/workMode");
    // ��ģʽ APP ע��
    m_topics.push_back("uartManager/" CFG_APP_NAME "/JSON/set/response/+/appRegister");
    // ���������ϵͳע�ᣬ��������������
    m_topics.push_back("uartManager/" CFG_OS_NAME "/JSON/request/register");

    // ��������֪ͨ--------------------------------����
    m_topics.push_back("btManager/" CFG_APP_NAME "/JSON/get/response/connectInfo");
    m_topics.push_back("btManager/Broadcast/JSON/report/notification/frameNotice");
    // ��������ת��--------------------------------����
    m_topics.push_back("btManager/" CFG_APP_NAME "/JSON/action/response/forward");
    m_topics.push_back("btManager/" CFG_APP_NAME "/JSON/get/response/paramGet");
    

    // scu�¹淶-���Ĳ�ѯ�豸�����������еı���guid -- -��������
    m_topics.push_back(CFG_DATA_CENTER_NAME "/" CFG_APP_NAME "/JSON/get/response/guid");
    //scu�¹淶-����guid�㲥
    m_topics.push_back(CFG_DATA_CENTER_NAME "/Broadcast/JSON/report/notification/devRegister");
    
    // scu�¹淶-�����������л�ȡ����ģ�Ͳ���dataCenter/{app}/JSON/get/response/model
    m_topics.push_back(CFG_DATA_CENTER_NAME "/" CFG_APP_NAME "/JSON/get/response/model");
    // scu�¹淶-�����������л�ȡ�豸�Ķ�ֵ����
    m_topics.push_back(CFG_DATA_CENTER_NAME "/" CFG_APP_NAME "/JSON/get/response/parameter");
    m_topics.push_back(CFG_DATA_CENTER_NAME "/Broadcast/JSON/report/notification/parameter");
    // scu�¹淶-�������豸�Ķ�ֵ������������������
    m_topics.push_back(CFG_DATA_CENTER_NAME "/" CFG_APP_NAME "/JSON/set/response/parameter");
    // scu�¹淶-ʵʱ���ݲ�ѯѯ���ؽ��
    m_topics.push_back(CFG_DATA_CENTER_NAME "/" CFG_APP_NAME "/JSON/get/response/realtime");
    // scu�¹淶-//5.3 ���ݱ仯�ϱ�
    m_topics.push_back("+/Broadcast/JSON/report/notification/+/+");
    //scu�¹淶-//6.5.2.9  ң������ת�������ӿ�
    m_topics.push_back("+/+/JSON/action/response/remoteCtrl"); 
    //scu�¹淶-//6.5.2.9  ң������ת��
    m_topics.push_back("+/Broadcast/JSON/action/request/remoteCtrl"); 
    
    // ����ϵͳ -- ����
    m_topics.push_back(CFG_OS_NAME "/" CFG_APP_NAME "/JSON/response/getEthCfg");//��ѯ��̫������155
    m_topics.push_back(CFG_OS_NAME "/" CFG_APP_NAME "/JSON/response/setEthCfg");//�޸���̫������156
    m_topics.push_back(CFG_OS_NAME "/" CFG_APP_NAME "/JSON/response/devInfo");//��ȡ�豸��Ϣ157
    m_topics.push_back(CFG_OS_NAME "/" CFG_APP_NAME "/JSON/response/devStatus");//��ȡ�豸״̬158
    m_topics.push_back(CFG_OS_NAME "/" CFG_APP_NAME "/JSON/response/getAppInfo");//��ѯӦ����Ϣ159
    m_topics.push_back(CFG_OS_NAME "/" CFG_APP_NAME "/JSON/response/getContainerInfoList");//��ѯ������Ϣ�б�160
    m_topics.push_back(CFG_OS_NAME "/" CFG_APP_NAME "/JSON/response/getAppInfoList");//��ѯ�����ڵ�APP��Ϣ161
    m_topics.push_back(CFG_OS_NAME "/" CFG_APP_NAME "/JSON/response/reboot");//�����ն�162
    m_topics.push_back(CFG_OS_NAME "/" CFG_APP_NAME "/JSON/response/getContainerBasicInfoList");//��ѯ����������Ϣ�б�163
    m_topics.push_back(CFG_OS_NAME "/" CFG_APP_NAME "/JSON/response/getContainerInfo");//��ѯ������Ϣ 164
    m_topics.push_back("wirelessDCM/"  CFG_APP_NAME "/JSON/get/response/netRegInfo");//��ѯ��������ע����Ϣ 165
    m_topics.push_back("ccoRouter/"    CFG_APP_NAME "/JSON/get/response/masterNode");//��ѯ���ڵ��ַ 166
    m_topics.push_back("wirelessDCM/"  CFG_APP_NAME "/JSON/set/response/setApn");//��ѯAPN 167
    m_topics.push_back("wirelessDCM/"  CFG_APP_NAME "/JSON/action/response/redial");//���²��� 168
    m_topics.push_back("wirelessDCM/"  CFG_APP_NAME "/JSON/get/response/moduleInfo");//��ѯ����ģ����Ϣ 169

    //����ϵͳ -- ��־
    m_topics.push_back(CFG_OS_NAME  "/Broadcast/JSON/notify/toRebootEvent");//3.2.27ϵͳ���������¼�
	m_topics.push_back(CFG_OS_NAME  "/Broadcast/JSON/notify/toResetEvent");//Ӳ����
    m_topics.push_back(CFG_OS_NAME  "/Broadcast/JSON/notify/powerStopEvent");//3.2.30ͣ���¼�
	m_topics.push_back(CFG_OS_NAME  "/Broadcast/JSON/notify/powerOnEvent");//3.2.31 �ϵ��¼�
    m_topics.push_back(CFG_OS_NAME "/" CFG_APP_NAME "/JSON/response/register");//OS-system/ע��
    m_topics.push_back(CFG_OS_NAME "/" CFG_APP_NAME "/JSON/request/keepAlive");//Ӧ�ñ�����Ϣ

    m_topics.push_back(CFG_OS_NAME "/" CFG_APP_NAME "/JSON/response/restartApp");//Ӧ������
    m_topics.push_back("rspSample/" CFG_APP_NAME "/JSON/get/response/data");//�������ȡһ��ң���¼�
    m_topics.push_back("acMeter/" CFG_APP_NAME "/JSON/get/response/data");//�������ȡһ��ң���¼�

    m_topics.push_back(CFG_OS_NAME "/" CFG_APP_NAME "/JSON/response/getUartChannelMode");//��ѯ����ͨ��ģʽ
    m_topics.push_back(CFG_OS_NAME "/Broadcast/JSON/notify/uartChannelModeEvent");//3.2.39����ģʽ�仯�¼�
    m_topics.push_back(CFG_OS_NAME "/" CFG_APP_NAME "/JSON/response/setIptables");//����iptable��Ӧ
    
    char str[256] = {0};
    SMTimeInfo local_time = ii_get_current_mtime();
    Juint32 iport = CParamJsonManager::CreateInstance().m_101ParamDevInfo.mainPort;
    sprintf(str, "megskyiec101%d%d%dstd", iport, local_time.nSecond, local_time.nMSecond);

    m_clientid = str;
    m_ip = "**********";
    m_port = "1883";

    char server_uri[256] = {0};
    sprintf(server_uri, "tcp://%s:%s", m_ip.c_str(), m_port.c_str());
    IEC_LOG_RECORD(eRunType, "mqtt connect url =(%s).", server_uri);
    if (agent_mqtt_init((const char *)server_uri, m_clientid.c_str()) != true)
    {
        IEC_LOG_RECORD(eErrType, "agent mqtt init failed.");
        bRet = false;
    }

    if (agent_mqtt_connect())
    { // ��ʼ���ɹ�����һ������
        if (agent_creatSubTopic())
        {
            m_connectFlag = true;
        }
    }

    bRet = m_Thread.start();
    return bRet;
}
bool CMqttClientInterManager::agent_mqtt_init(const char *server_uri, const char *client_id)
{
    CIIString iisTemp;

    int mqtt_ret;
    if (server_uri == NULL || client_id == NULL)
    {
        IEC_LOG_RECORD(eErrType, "MQTT Init param failed.");
        return false;
    }

    m_conn_opts_inter.keepAliveInterval = SG_KEEP_ALIVE_INTERVAL;
    m_conn_opts_inter.cleansession = 1;
    if (m_user.size() != 0 && m_password.size() != 0)
    {
        m_conn_opts_inter.username = m_user.c_str();
        m_conn_opts_inter.password = m_password.c_str();
    }

    mqtt_ret = MQTTClient_create(&m_client, server_uri, client_id, MQTTCLIENT_PERSISTENCE_NONE, NULL);
    if (mqtt_ret != MQTTCLIENT_SUCCESS)
    {
        IEC_LOG_RECORD(eErrType, "MQTTClient create failed(server_uri = %s,clientid = %s,ret = %d).", server_uri, client_id, mqtt_ret);
        return false;
    }

    IEC_LOG_RECORD(eRunType, "MQTTClient create success(server_uri = %s,clientid = %s).", server_uri, client_id);

    mqtt_ret = MQTTClient_setCallbacks(m_client, NULL, agent_ConnLost, agent_MqttMsgArrvd, agent_Delivered);
    if (mqtt_ret != MQTTCLIENT_SUCCESS)
    {
        agent_mqtt_destroy();
        return false;
    }

    IEC_LOG_RECORD(eRunType, "agent mqtt init success(server_uri = %s,clientid = %s).", server_uri, client_id);
    return true;
}
void CMqttClientInterManager::Exit(void)
{
    m_Thread.close();
    m_Thread.wait_for_end();
    agent_destrySubTopic();
    agent_mqtt_disconnect();
    agent_mqtt_destroy();
    m_mqttSendQueue.Clear();
    m_mqttRecvQueue.Clear();
}

void CMqttClientInterManager::thread_prev(void)
{
}

void CMqttClientInterManager::thread_func(void)
{
    OnRun();
}

void CMqttClientInterManager::thread_exit(void)
{
}
void CMqttClientInterManager::OnRun(void)
{
    mqtt_data_info_s Sitem;
    mqtt_data_info_s Ritem;
    if (Get_SendItem(Sitem))
    {
        agent_MqttMsgPub(Sitem.msg_send, Sitem.pubtopic, Sitem.retained); // ȡ�����Ͷ������ݺ���
    }

    while (Get_RecvItem(Ritem))
    {
        CRealDataManager::CreateInstance().UnpackData(Ritem); // �����յ�������
    }

    CRealDataManager::CreateInstance().RemoteCommandPro(); // ʵʱ���ң�ض���  ������mqtt��Ϣ
    CRealDataManager::CreateInstance().MqttModuleInitPro();

    if (m_T.CalOvertime())
    {
        // fileStorage_SOE();
        // fileStorage_FIXPT();
        //fileStorage_EXV();
        m_T.StartCount();
    }

    ii_sleep(100);
}

void CMqttClientInterManager::Start(void)
{
}

void CMqttClientInterManager::Stop(void)
{
    m_mqttSendQueue.Clear();
    m_mqttRecvQueue.Clear();
}

bool CMqttClientInterManager::getMqttConnect(void)
{
    if (!MQTTClient_isConnected(m_client))
    {
        m_connectFlag = false;
    }
    else
    {
        m_connectFlag = true;
    }
    return m_connectFlag;
}

bool CMqttClientInterManager::agent_mqtt_connect(void)
{
    int mqtt_ret = VOS_OK;
    m_connectFlag = false;
    if (m_client == NULL)
    {
        IEC_LOG_RECORD(eErrType, "mqtt client id handle invalid).");
        return false;
    }

    IEC_LOG_RECORD(eRunType, "mqtt connecting ....");
    mqtt_ret = MQTTClient_connect(m_client, &m_conn_opts_inter);
    if (mqtt_ret != MQTTCLIENT_SUCCESS)
    {
        IEC_LOG_RECORD(eErrType, "mqtt connect failed(ret = %d)).", mqtt_ret);
        m_connectFlag = false;
        return false;
    }

    IEC_LOG_RECORD(eRunType, "mqtt connect success.");
    m_connectFlag = true;
    return true;
}

void CMqttClientInterManager::agent_mqtt_disconnect(void)
{
    int mqtt_ret;

    if (m_client != NULL)
    {
        mqtt_ret = MQTTClient_disconnect(m_client, MQTT_DISCONNECT_TIMEOUT);
        m_connectFlag = false;
        if (mqtt_ret != MQTTCLIENT_SUCCESS)
        {
            IEC_LOG_RECORD(eRunType, "mqtt disconnect error.");
        }
    }
}

void CMqttClientInterManager::agent_mqtt_destroy(void)
{
    if (m_client != NULL)
    {
        MQTTClient_destroy(&m_client);
        IEC_LOG_RECORD(eRunType, "mqtt destroy.");
        m_client = NULL;
    }
}

bool CMqttClientInterManager::agent_MqttMsgPub(char *msg_send, char *pub_topic, int retained)
{
    int mqtt_ret = 0;
    int msglen = 0;
    CIIString iisTemp;
    MQTTClient_deliveryToken token;
    MQTTClient_message pubmsg = MQTTClient_message_initializer;
    if (msg_send == NULL || pub_topic == NULL)
    {
        IEC_LOG_RECORD(eErrType, "mqtt publish param msg_send or pubtopicinvalid.");
        return false;
    }

    if (m_client == NULL)
    {
        IEC_LOG_RECORD(eErrType, "mqtt client id handle invalid.");
        return false;
    }

    if (!MQTTClient_isConnected(m_client))
    {
        IEC_LOG_RECORD(eErrType, "mqtt client disconnected.");
        m_connectFlag = false;
    }
    else
    {
        m_connectFlag = true;
    }

    if (m_connectFlag == false)
    {
        return true;
    }
    msglen = (int)strlen(msg_send) ;//+ 1;
    pubmsg.payload = msg_send;
    pubmsg.payloadlen = msglen;
    pubmsg.qos = QOS;
    // if (strcmp(pub_topic, "") == 0)
    pubmsg.retained = retained;

    mqtt_ret = MQTTClient_publishMessage(m_client, pub_topic, &pubmsg, &token);
    if (mqtt_ret != MQTTCLIENT_SUCCESS)
    {
        iisTemp.Format("mqtt publish failed.(topic=%s, ret=%d)", pub_topic, mqtt_ret);
        IEC_LOG_RECORD(eErrType, iisTemp.GetBuf());
        return false;
    }

    mqtt_ret = MQTTClient_waitForCompletion(m_client, token, TIMEOUT);
    if (mqtt_ret != MQTTCLIENT_SUCCESS)
    {
        iisTemp.Format("MQTTClient waitForCompletion ret=%d).", mqtt_ret);
        IEC_LOG_RECORD(eErrType, iisTemp.GetBuf());
        return false;
    }
    return true;
}

int CMqttClientInterManager::agent_MqttMsgArrvd(void *context, char *topicName, int topicLen, MQTTClient_message *message)
{
    CIIAutoMutex lock(&CMqttClientInterManager::CreateInstance().m_cs);
    char *content_str = NULL;
    if (message == NULL)
    {
        IEC_LOG_RECORD(eErrType, "mqtt msg is null.");
        MQTTClient_free(topicName);
        return 1;
    }
    // mskprintf("Mqtt Msg Arrvd topicName = (%s)............. \n", topicName);

    if (message->payloadlen > 0)
    {
        content_str = (char *)malloc((size_t)(message->payloadlen + 1));
        if (content_str)
        {
            memcpy(content_str, message->payload, (size_t)(message->payloadlen));
            content_str[message->payloadlen] = 0;
            mqtt_data_info_s item = {0};
            sprintf(item.pubtopic, "%s", topicName);
            // if (strncmp(topicName,"esdk/notify/event/terminal/status/",strlen("esdk/notify/event/terminal/status/")) == 0)
            //{
            //     item.retained = 1;
            // }
            memcpy_safe(item.msg_send, sizeof(item.msg_send), content_str, message->payloadlen, message->payloadlen);
            item.msg_send_lenth = message->payloadlen;
            CMqttClientInterManager::CreateInstance().Push_RecvItem(item);
            (void)memset(content_str, 0, (size_t)(message->payloadlen + 1));
            (void)memset(message->payload, 0, (size_t)(message->payloadlen));
            free(content_str);
            content_str = NULL;
        }
        else
        {
            IEC_LOG_RECORD(eErrType, "mqtt msg malloc failed.");
        }
    }
    MQTTClient_freeMessage(&message);
    MQTTClient_free(topicName);
    return 1;
}

bool CMqttClientInterManager::agent_mqtt_msg_subscribe(char *topic, int qos)
{
    int mqtt_ret;
    if (topic == NULL)
    {
        IEC_LOG_RECORD(eErrType, "mqtt subscribe topic invalid.");
        return false;
    }

    if (strlen(topic) > 256)
    {
        IEC_LOG_RECORD(eErrType, "mqtt subscribe topic over max 256.");
        return false;
    }

    if (m_client == NULL)
    {
        IEC_LOG_RECORD(eErrType, "mqtt subscribe client id handle invalid).");
        return false;
    }

    mqtt_ret = MQTTClient_subscribe(m_client, topic, qos);
    if (mqtt_ret != MQTTCLIENT_SUCCESS)
    {
        IEC_LOG_RECORD(eErrType, "mqtt subscribe failed(ret = %d,pub_topic = %s)).", mqtt_ret, topic);
        return false;
    }
    IEC_LOG_RECORD(eErrType, "mqtt subscribe succeed(pub_topic = %s)).", topic);
    return true;
}

bool CMqttClientInterManager::agent_creatSubTopic(void)
{
    bool ret = true;
    std::list<std::string>::iterator it = m_topics.begin();
    for (; it != m_topics.end(); ++it)
    {
        if (!agent_mqtt_msg_subscribe((char *)(*it).c_str(), QOS))
        {
            ret = false;
        }
    }

    return ret;
}
void CMqttClientInterManager::agent_destrySubTopic()
{
    std::list<std::string>::iterator it = m_topics.begin();
    for (; it != m_topics.end(); ++it)
    {
        MQTTClient_unsubscribe(m_client, (*it).c_str());
    }
    // MQTTClient_unsubscribe(m_client, m_topic_other);
}

Juint32 CMqttClientInterManager::MqttGetRandomUuid(char *pucUuid, Juint32 ulLen)
{
    Juint32 ulIndex;
    Juint32 randomNum;
    char *pucUuidItem = pucUuid;

    if (NULL == pucUuid)
    {
        MG_LOG_E("VOS_GetRandomUuid in put invalid.\n");
        return VOS_ERR;
    }

    srand(time(0));
    for (ulIndex = 0; ulIndex < 16; ulIndex++)
    {
        randomNum = (rand() % 255);
        sprintf(pucUuidItem, "%02x", randomNum);
        pucUuidItem += 2;

        switch (ulIndex)
        {
        /* 3 5 7 9 ����ִ������- */
        case 3:
        case 5:
        case 7:
        case 9:
            *pucUuidItem++ = '-';
            break;
        }
    }
    *pucUuidItem = '\0';

    return VOS_OK;
}
/*****************************************************************************
 �� �� ��  : MqttGenerateToken
 ��������  : ����token
 �������  :
 �������  :
 �� �� ֵ  :
 ���ú���  :
 ��������  :
*****************************************************************************/
int CMqttClientInterManager::MqttGenerateToken(char *token, size_t buf_len)
{

    if (MQTT_OK != MqttGetRandomUuid(token, buf_len))
    {
        MG_LOG_E("generate token failed\n");
        return MQTT_ERR;
    }
    // MG_LOG("generated token [%s]\n", token);
    return MQTT_OK;
}
int CMqttClientInterManager::MqttHeaderFill(mqtt_header_s *header, const char *request_token)
{
    // time_t now_time;
    int ret = MQTT_ERR;

    if (request_token) // �������token�ǿգ�˵��������������Ӧ�е�token����Ӧtoken������token��ͬ
    {
        //cout<<request_token;
        (void)memcpy(header->token, request_token, sizeof(header->token));
    }

    else
    {
        // MG_LOG("\nheader token len is %d.\n", sizeof(header->token));
        ret = MqttGenerateToken(header->token, sizeof(header->token));
        if (ret != MQTT_OK)
        {
            MG_LOG_E("\nFill json header failed for generate token.\n");
            return MQTT_ERR;
        }
    }

    // ʹ��UTCʱ��
    ret = MqttTimeStr(header->timestamp, sizeof(header->timestamp));
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nFill json header failed for generate time str.\n");
        return MQTT_ERR;
    }
    return MQTT_OK;
}

// 101��֡mqtt.base���룬���͵���Ϣ����
int CMqttClientInterManager::MqttPackUartManagerFill(Juint8 *send_buf, int length)
{
    mskprintf("MqttPackUartManagerFill:.\n");
    // uartManager/JSON/ transparant /notification
    Juint32 type = CParamJsonManager::CreateInstance().m_101ParamDevInfo.connectType;
    std::string src_topic;
    if(type == 1)
    {
        //����RS232-1����Ϊ��ռģʽ
        src_topic = CFG_APP_NAME "/" CFG_UARTMANAGER_NAME "/JSON/transparant/notification/RS232-1/data";
    }
    else if(type == 2)
    {
        //����RS232-2����Ϊ��ռģʽ
        src_topic = CFG_APP_NAME "/" CFG_UARTMANAGER_NAME "/JSON/transparant/notification/RS232-2/data";
    }

    mqtt_header_s jsheader = {0};

    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, "123");
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return false;
    }

    
    neb::CJsonObject oJson;
    oJson.Add("token", 123);
    oJson.Add("timestamp", jsheader.timestamp);

    if(type == 1)
    {
        //����RS232-1   
        oJson.Add("port", "RS232-1");
    }
    else if(type == 2)
    {
        //����RS232-2
        oJson.Add("port", "RS232-2");
    }    

    oJson.Add("prio", 0);
    oJson.Add("prm", 0);
    oJson.Add("byteTimeout", 100);
    oJson.Add("frameTimeout", 100);
    oJson.Add("taskTimeout", 100);
    oJson.Add("estSize", 100);

    mskprintf("write data: ->-> ");
    for (int i = 0; i < length; i++)
    {
        mskprintf("%02X ", send_buf[i]);
    }

    unsigned char dst[9999];
    int length1;
    tiny_base64_encode(send_buf, length, dst, &length1);
    tiny_base64_encode(send_buf, length, dst, &length1);

    mskprintf("\nbase64: %s\n", dst);

    string strdst = (char *)(dst);
    oJson.Add("data", strdst);

    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mskprintf("topic:%s\r\n", src_topic.c_str());
        mskprintf("msg:%s\r\n", mssm.c_str());
        CMqttClientInterManager::CreateInstance().agent_MqttMsgPub((char *)(mssm.c_str()), (char *)src_topic.c_str(), 0);
    }   

    return 0;
}

//�������ݷ���
int CMqttClientInterManager::MqttPackBtManagerManagerFill(Juint8 *send_buf, int length)
{
     // uartManager/JSON/ transparant /notification
    char pub_topic[255] = CFG_APP_NAME "/" CFG_BTMANAGER_NAME "/JSON/action/request/sendData";
    mqtt_header_s jsheader = {0};

    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, "123");
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return false;
    }

    
    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);    
    oJson.Add("prio", 1);
    oJson.Add("port",CRealDataManager::CreateInstance().m_btManager_port);

    mskprintf("write data: ->-> ");
    for (int i = 0; i < length; i++)
    {
        mskprintf("%02X ", send_buf[i]);
    }
    unsigned char dst[9999];
    int length1;
    tiny_base64_encode(send_buf, length, dst, &length1);
    tiny_base64_encode(send_buf, length, dst, &length1);

    mskprintf("\nlength1 : = %d  base64: %s\n", length1,dst);

    string strdst = (char *)(dst);
    oJson.Add("data", strdst);

    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mskprintf("topic:%s\r\n", pub_topic);
        mskprintf("msg:%s\r\n", mssm.c_str());
        CMqttClientInterManager::CreateInstance().agent_MqttMsgPub((char *)(mssm.c_str()), pub_topic, 0);
    }   

    return 0;
}

int CMqttClientInterManager::MqttPackUartManagerFill(string base_buf)
{    
    mskprintf("MqttPackUartManagerFill:.\n");
    // uartManager/JSON/ transparant /notification
    Juint32 type = CParamJsonManager::CreateInstance().m_101ParamDevInfo.connectType;
    std::string src_topic;
    if(type == 1)
    {
        //����RS232-1����Ϊ��ռģʽ
        src_topic = CFG_APP_NAME "/" CFG_UARTMANAGER_NAME "/JSON/transparant/notification/RS232-1/data";
    }
    else if(type == 2)
    {
        //����RS232-2����Ϊ��ռģʽ
        src_topic = CFG_APP_NAME "/" CFG_UARTMANAGER_NAME "/JSON/transparant/notification/RS232-2/data";
    }

    mqtt_header_s jsheader = {0};

    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, "123");
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return false;
    }

    
    neb::CJsonObject oJson;
    oJson.Add("token", 123);
    oJson.Add("timestamp", jsheader.timestamp);

    if(type == 1)
    {
        //����RS232-1   
        oJson.Add("port", "RS232-1");
    }
    else if(type == 2)
    {
        //����RS232-2
        oJson.Add("port", "RS232-2");
    }    

    oJson.Add("prio", 0);
    oJson.Add("prm", 0);
    oJson.Add("byteTimeout", 100);
    oJson.Add("frameTimeout", 100);
    oJson.Add("taskTimeout", 100);
    oJson.Add("estSize", 100);   

    mskprintf("\nbase64: %s\n", base_buf.c_str());

    string strdst = (char *)(base_buf.c_str());
    oJson.Add("data", strdst);

    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mskprintf("topic:%s\r\n", src_topic.c_str());
        mskprintf("msg:%s\r\n", mssm.c_str());
        CMqttClientInterManager::CreateInstance().agent_MqttMsgPub((char *)(mssm.c_str()), (char *)src_topic.c_str(), 0);
    }   

    return 0;
}

/*****************************************************************************
 �� �� ��  : MqttTimeStr
 ��������  : ����ʱ���
 �������  :
 �������  :
 �� �� ֵ  :
 ���ú���  :
 ��������  :
*****************************************************************************/
int CMqttClientInterManager::MqttTimeStr(char *time_buff, size_t buff_len)
{
    int ret;
    struct timeval tv;
    struct timezone tz;
    struct tm *t;

    gettimeofday(&tv, &tz);
    t = localtime(&tv.tv_sec);
    ret = snprintf(time_buff, buff_len, "%04d-%02d-%02dT%02d:%02d:%02d.%03d+0800",
                   1900 + t->tm_year, 1 + t->tm_mon, t->tm_mday, t->tm_hour, t->tm_min, t->tm_sec, (int)tv.tv_usec / 1000);

    return ret > 0 ? MQTT_OK : MQTT_ERR;
}

void CMqttClientInterManager::Push_RecvItem(mqtt_data_info_s item)
{
    m_mqttRecvQueue.AddTail(item);
}

bool CMqttClientInterManager::Get_RecvItem(mqtt_data_info_s &item)
{
    return m_mqttRecvQueue.GetHead(item);
}

void CMqttClientInterManager::Push_SendItem(mqtt_data_info_s item)
{
    m_mqttSendQueue.AddTail(item);
}

bool CMqttClientInterManager::Get_SendItem(mqtt_data_info_s &item)
{
    return m_mqttSendQueue.GetHead(item);
}

CMqttClientInterManager::CMqttClientInterManager()
{
    m_Thread.SetPackageAcquireManager(this);
    m_mqttSendQueue.Clear();
    m_mqttRecvQueue.Clear();
    m_topics.clear();
    m_T.SetParam(200 * 1000); // Ĭ��200��
    m_T.StartCount();
    m_client = NULL;
}

CMqttClientInterManager::~CMqttClientInterManager()
{
}
