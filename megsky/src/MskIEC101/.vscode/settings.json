{"files.associations": {"new": "cpp", "array": "cpp", "deque": "cpp", "list": "cpp", "string": "cpp", "unordered_map": "cpp", "vector": "cpp", "string_view": "cpp", "initializer_list": "cpp", "type_traits": "cpp", "*.tcc": "cpp", "system_error": "cpp", "atomic": "cpp", "bitset": "cpp", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "exception": "cpp", "algorithm": "cpp", "map": "cpp", "memory": "cpp", "memory_resource": "cpp", "optional": "cpp", "tuple": "cpp", "utility": "cpp", "fstream": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "ostream": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "typeinfo": "cpp"}}