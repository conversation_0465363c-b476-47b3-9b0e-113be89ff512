#include "iec_fileStorage.h"
#include "public_struct.h"
#include <stdio.h>
#include "tinyxml.h"
#include <iostream>
#include <cstring>
#include <dirent.h>
#include "real_data.h"


using namespace std;


#define CFILE_ERR -1   /* �����ļ�ʧ�� */
#define CFILE_NEW 1    /* �����ļ��ɹ� */
#define CFILE_EXISTS 0 /* �ļ��Ѵ��ڣ�����Ҫ���� */

#define HISTORY_FILE_STATE_STARTSYMBLE 0XA5A5   // �ļ�״̬�Ŀ�ʼ��ʶ
#define HISTORY_LINE_LEN_MAX 150                // ��ʷ��¼ÿ����󳤶�
#define HISTORY_SOE_LINE_LEN 32                 // �̶��� ��4+1+1+1+23+2��
#define HISTORY_SOE_LINE_LEN_MAX 192                 // ���̶��� ��4+1+1+1+23+2��
#define HISTORY_CO_LINE_LEN 38                  // �̶���
#define HISTORY_FIXPT_LINE_LEN_MAX 200           // ������
#define HISTORY_EXV_LINE_LEN_MAX 50           // ������



#define SOE_FILE_NAMEPATH IEC_HISTORY_PATH "HISTORY/SOE/soe.msg"
#define SOE_FILE_NAMEPATH_XML IEC_HISTORY_PATH "HISTORY/SOE/soe.xml"
#define CO_FILE_NAMEPATH_XML IEC_HISTORY_PATH "HISTORY/CO/co.xml"
#define EXV_FILE_NAMEPATH_XML IEC_HISTORY_PATH "HISTORY/EXV/exv"
#define FIXPT_FILE_NAMEPATH_XML IEC_HISTORY_PATH "HISTORY/FIXPT/fixpt"
#define FRZ_FILE_NAMEPATH_XML IEC_HISTORY_PATH "HISTORY/FRZ/frz"
#define ULOG_FILE_NAMEPATH_XML IEC_HISTORY_PATH "HISTORY/ULOG/ulog.xml"
#define FLOWREV_FILE_NAMEPATH_XML IEC_HISTORY_PATH "HISTORY/FLOWREV/flowrev.xml"

#define EXV_FILE_NAMEPATH IEC_HISTORY_PATH "HISTORY/EXV"    
#define FIXPT_FILE_NAMEPATH IEC_HISTORY_PATH "HISTORY/FIXPT"
#define FRZ_FILE_NAMEPATH IEC_HISTORY_PATH "HISTORY/FRZ"


/* ����ļ��Ƿ���ڣ��������򴴽� */
static int fileStorage_Create(const char *path)
{
    if (path == NULL)
    {
        return false;
    }

    FILE *fd = NULL;
    fd = fopen(path, "r");
    if (!fd)
    {
        fd = fopen(path, "w");
        if (!fd)
        {
            IEC_LOG_RECORD(eErrType, "can not write file(%s)!", path);
            return CFILE_ERR;
        }
        fclose(fd);
        return CFILE_NEW;
    }

    fclose(fd);
    return CFILE_EXISTS;
}

static int fileStorage_GetFileDate(char *date_buffer, int len)
{
    if (len < 8 + 1)
    {
        return -1;
    }

    struct timeval tv;
    struct timezone tz;
    struct tm *t;
    gettimeofday(&tv, &tz);
    t = localtime(&tv.tv_sec);
    snprintf(date_buffer, len - 1, "%04d%02d%02d", 1900 + t->tm_year, 1 + t->tm_mon, t->tm_mday);
    return 1;
}

/* �ļ��洢��ʼ�� */
void fileStorage_Init(void)
{
    checkFileDate(EXV_FILE_NAMEPATH);
    checkFileDate(FIXPT_FILE_NAMEPATH);
    checkFileDate(FRZ_FILE_NAMEPATH);
    //fileStorage_SOE(0, 0);
    //fileStorage_FIXPT();
    //fileStorage_EXV();

    // ����������ʱ����
    //  /* ��ȡʱ��buffer */
    //  char datebuffer[9] = {0};
    //  fileStorage_GetFileDate(datebuffer, sizeof(datebuffer));

    // /* ����FIXPT�ļ� */
    // char fixptNamePath[128] = {0};
    // snprintf(fixptNamePath, sizeof(fixptNamePath) - 1, IEC_EXCFG_PATH "HISTORY/FIXPT/fixpt%s.msg", datebuffer);
    // fileStorage_Create(fixptNamePath);

    // /* ����EXV�ļ� */
    // char exvNamePath[128] = {0};
    // snprintf(exvNamePath, sizeof(exvNamePath) - 1, IEC_EXCFG_PATH "HISTORY/EXV/exv%s.msg", datebuffer);
    // fileStorage_Create(exvNamePath);

    // /* ����SOE�ļ� */
    // fileStorage_Create(SOE_FILE_NAMEPATH);
}

/* ����SOE�ļ���SOEĿǰ����Ҫ�洢���ݣ�����������ʱΪ�� */

void fileStorage_SOE(int addr, int value, int bl,int sb, int nt, int iv, char* time)
{
    return;
    // soe.msg,v1.0
    if (CFILE_NEW == fileStorage_Create(SOE_FILE_NAMEPATH))
    {
        FILE *fp = fopen(SOE_FILE_NAMEPATH, "w+");
        if (NULL != fp)
        {
            const char *soe_tag = "soe.msg,v1.0\r\n";
            fwrite(soe_tag, strlen(soe_tag), 1, fp);
            fclose(fp);
        }
    }

    /* ����SOE��¼׷��д���ļ����롣���������� */
    mskprintf("\n********start write soe file**************\n");
    FILE *fp = fopen(SOE_FILE_NAMEPATH, "a+");
    int ch;
    int rows = 0;
    while ((ch = fgetc(fp)) != EOF)
    {
        if (ch == '\n')
        {
            rows++;
        }
    }    
    mskprintf("\n********file rows : %d**************\n",rows);

    char dstFile[100] = {0}; 
    memset(dstFile, 0, sizeof(dstFile));
    snprintf(dstFile, HISTORY_SOE_LINE_LEN_MAX, "%04X, %d, %s\r\n",  addr, value, time);	
   
    if (NULL != fp)
    {        
        fwrite(dstFile, strlen(dstFile), 1, fp);
        fclose(fp);
    }   

}

/* ����FIXPT�ļ���FIXPTĿǰ����Ҫ�洢���ݣ�����������ʱΪ��*/
void fileStorage_FIXPT(void)
{
    return;
    // fixpt20210926.msg,v1.0
    char datebuffer[32] = {0};
    fileStorage_GetFileDate(datebuffer, sizeof(datebuffer));

    char fixptNamePath[128] = {0};
    snprintf(fixptNamePath, sizeof(fixptNamePath) - 1, IEC_EXCFG_PATH "HISTORY/FIXPT/fixpt%s.msg", datebuffer);

    if (CFILE_NEW == fileStorage_Create(fixptNamePath))
    {
        FILE *fp = fopen(fixptNamePath, "w+");
        if (NULL != fp)
        {
            char fixpt_tag[32] = {0};
            snprintf(fixpt_tag, sizeof(fixpt_tag) - 1, "fixpt%s.msg,v1.0\r\n", datebuffer);
            fwrite(fixpt_tag, strlen(fixpt_tag), 1, fp);
            fclose(fp);
        }
    }
    /* ����FIXPT��¼׷��д���ļ����롣���������� */
}

/* ����EXV�ļ���EXVĿǰ����Ҫ�洢���ݣ�����������ʱΪ��*/
void fileStorage_EXV(void)
{
    return;
    // exv20210926.msg,v1.0
    char datebuffer[32] = {0};
    fileStorage_GetFileDate(datebuffer, sizeof(datebuffer));

    char exvNamePath[128] = {0};
    snprintf(exvNamePath, sizeof(exvNamePath) - 1, IEC_EXCFG_PATH "HISTORY/EXV/exv%s.msg", datebuffer);

    if (CFILE_NEW == fileStorage_Create(exvNamePath))
    {
        FILE *fp = fopen(exvNamePath, "w+");
        if (NULL != fp)
        {
            char exv_tag[32] = {0};
            snprintf(exv_tag, sizeof(exv_tag) - 1, "exv%s.msg,v1.0\r\n", datebuffer);
            fwrite(exv_tag, strlen(exv_tag), 1, fp);
            fclose(fp);
        }
    }
   /* ����EXV��¼׷��д���ļ����롣���������� */
}

//����ļ�������ѭ���洢31�죺 ���� ��ֵ  ����
int checkFileDate(std::string filepath)
{
    // EXV_FILE_NAMEPATH_XML
    
    //��ǰ����
    time_t base;
	time(&base);
    struct tm *tm = localtime(&base);
    tm->tm_mday -= 31;  //��ǰ����ǰ31��
    time_t next = mktime(tm);      
    STimeInfo tmInfo = ii_localtime(&next);
    mskprintf("checkFileDate. %04d-%02d-%02d.\n",tmInfo.nYear,tmInfo.nMonth,tmInfo.nDay); 

    int currentpre31day = ii_localtime_dayofyear(&next) + tmInfo.nYear * 365; //31?????
    mskprintf("currentpre31day. %d.\n",currentpre31day); 


    DIR *pDir;
    struct dirent *ptr;

    if (!(pDir = opendir(filepath.c_str())))
    {
        return 0;
    }
        
    while ((ptr = readdir(pDir)) != 0)
    {
        if (strcmp(ptr->d_name, ".") != 0 && strcmp(ptr->d_name, "..") != 0)
        {
            mskprintf("file name : %s\n",ptr->d_name);
            string filename = ptr->d_name;
            string filenameDate = ptr->d_name;
            if(filename.size() < 12)
            {
                continue;
            }
            else
            {
                filenameDate = filenameDate.substr(filename.size()-12, 8);
            }
            int year =  atoi(filenameDate.substr(0,4).c_str());            
            int month =  atoi(filenameDate.substr(4,2).c_str());
            int day =  atoi(filenameDate.substr(6,2).c_str());
            
            mskprintf("%d : %d : %d \n",year,month,day);

            struct tm *tmfile = localtime(&base);
            tmfile->tm_year = year;  
            tmfile->tm_mon  = month-1;  
            tmfile->tm_mday = day;  
            time_t fileTM = mktime(tmfile);    
            int fileday = ii_localtime_dayofyear(&fileTM) + year*365;
            mskprintf("fileday. %d.\n",fileday);   

            //31����ļ�ɾ��
            if(fileday < currentpre31day)
            {
                std::string rmFileName = filepath;
                rmFileName += "/";
                rmFileName += filename;
                int ret = remove(rmFileName.c_str());
                mskprintf("remove ret. %d.  %s.\n",ret, rmFileName.c_str());   
            }

        }            
    }
    closedir(pDir);
    return 0;
}

int writeXml_EXV(std::map<Juint32,EXVstruct> exv,std::string YMD)
{    
    mskprintf("#######writeXml_EXV\n");
    fileStorage_Init();
    mskprintf("exvYMD:%s, %d\n",YMD.c_str(),exv.size());

    TiXmlDocument *writeDoc = new TiXmlDocument; //xml�ĵ�ָ��

    //�ĵ���ʽ����
    TiXmlDeclaration *decl = new TiXmlDeclaration("1.0", "UTF-8", "");
    writeDoc->LinkEndChild(decl); //д���ĵ�

    //дdatafile�ڵ�
    TiXmlElement *RootElement = new TiXmlElement("DataFile");//��Ԫ��    
    writeDoc->LinkEndChild(RootElement);

    //дHeader��Ϣ
    TiXmlElement *HeadElement = new TiXmlElement("Header");//Header
    HeadElement->SetAttribute("fileType","EXV");
    HeadElement->SetAttribute("fileVer","1.00");
    HeadElement->SetAttribute("devName","SCU");
    RootElement->LinkEndChild(HeadElement);//���ڵ�д���ĵ�

    //дDataRec ��Ϣ
    TiXmlElement *DataAttrElement = new TiXmlElement("DataAttr");//DataAttr 
    DataAttrElement->SetAttribute("num",exv.size());
    RootElement->LinkEndChild(DataAttrElement);//���ڵ�д���ĵ�

    // дDataRec ��Ϣ
    std::map<Juint32, EXVstruct>::iterator it = exv.begin();
    // д��Ϣ��
    for (; it != exv.end(); ++it)
    {
        TiXmlElement *DIElement = new TiXmlElement("DI");//Header
        DIElement->SetAttribute("ioa",it->second.ioa.c_str());
        DIElement->SetAttribute("type",it->second.type.c_str());
        DIElement->SetAttribute("unit",it->second.unit.c_str());
        DataAttrElement->LinkEndChild(DIElement);
        mskprintf("iddr:%s,maxtm:%s,maxvalue:%s,mintm:%s,minvalue:%s\n",it->second.ioa.c_str(),it->second.max_tm.c_str(),it->second.max.c_str(),it->second.min_tm.c_str(),it->second.min.c_str());
    }

    //дDataRec ��Ϣ
    TiXmlElement *DataRecElement = new TiXmlElement("DataRec");//DataRec 

    RootElement->LinkEndChild(DataRecElement);//���ڵ�д���ĵ�
    //д��Ϣ��
    // дDataRec ��Ϣ
    std::map<Juint32, EXVstruct>::iterator it2 = exv.begin();
    // д��Ϣ��
    for (; it2 != exv.end(); ++it2)
    {
         TiXmlElement *DIElement = new TiXmlElement("DI");//Header
        DIElement->SetAttribute("max",it2->second.max.c_str());
        DIElement->SetAttribute("max_tm",it2->second.max_tm.c_str());
        DIElement->SetAttribute("min",it2->second.min.c_str());
        DIElement->SetAttribute("min_tm",it2->second.min_tm.c_str());
        DataRecElement->LinkEndChild(DIElement);
    }
   
    std::string path = EXV_FILE_NAMEPATH_XML;
    path += YMD;
    path += ".xml";
    writeDoc->SaveFile(path.c_str());
    delete writeDoc;    

    return 0;
}

int readXml_EXV(std::map<Juint32,EXVstruct> &m_exv,std::string YMD)
{
    std::string path = EXV_FILE_NAMEPATH_XML;
    path += YMD;
    path += ".xml";

    mskprintf("exv path : %s\n",path.c_str());

    TiXmlDocument mydoc(path.c_str());//xml�ĵ�����
    bool loadOk=mydoc.LoadFile();//�����ĵ�
    if(!loadOk)
    {
        mskprintf("could not load the EXV file.Error: %s\n",mydoc.ErrorDesc());
        IEC_LOG_RECORD(eRunType, "could not load the EXV file.Error(%s).\n", mydoc.ErrorDesc());
        return -1;
    }
    TiXmlElement *RootElement = mydoc.RootElement(); // ��Ԫ��, RootElement
    //mskprintf("%s\n", RootElement->Value());
    std::vector<int> vindex;
    // �����ý��
    for (TiXmlElement *SonElement = RootElement->FirstChildElement(); SonElement != NULL; SonElement = SonElement->NextSiblingElement()) // ???????????
    {
        //mskprintf("%s\n", SonElement->Value());
        std::string strvalue = SonElement->Value();
        //mskprintf("strvalue=%s.\n", strvalue.c_str());
        
        if("DataAttr" == strvalue)
        {            
            
            //mskprintf("SonElement->Value() = %d\n", num);
            for (TiXmlElement *valElement = SonElement->FirstChildElement(); valElement != NULL; valElement = valElement->NextSiblingElement()) // ???????????
            {                                  
                std::string ioa  = valElement->Attribute("ioa");
                std::string type = valElement->Attribute("type");
                std::string unit = valElement->Attribute("unit");
                
                EXVstruct exv;
                exv.ioa =  ioa;
                exv.type = type;
                exv.unit = unit;
                Juint32 addr = atoi(ioa.c_str());
                m_exv[addr] = exv;
                vindex.push_back(addr);
                //mskprintf("%s,%s,%s,%s\n",valElement->Value(), ioa.c_str(),type.c_str(),unit.c_str());               
            }
        }    
        if("DataRec" == strvalue)
        {       
            int i=0;     
            for (TiXmlElement *valElement = SonElement->FirstChildElement(); valElement != NULL; valElement = valElement->NextSiblingElement()) // ???????????
            {                   
                std::string max     = valElement->Attribute("max");
                std::string max_tm  = valElement->Attribute("max_tm");
                std::string min     = valElement->Attribute("min");
                std::string min_tm  = valElement->Attribute("min_tm");
                std::string ymd     = max_tm.substr(0,6);
                //mskprintf("vindexsize : %d\n",vindex.size());
                //mskprintf("vindex[i] : %d\n",vindex[i]);
                m_exv[vindex[i]].max    = max;
                m_exv[vindex[i]].max_tm = max_tm;
                m_exv[vindex[i]].min    = min;
                m_exv[vindex[i]].min_tm = min_tm;
                m_exv[vindex[i]].YMD    = ymd;
                

                mskprintf("%s,%s,%s,%s,%s\n", m_exv[vindex[i]].ioa.c_str(),m_exv[vindex[i]].max.c_str(),m_exv[vindex[i]].max_tm.c_str(),m_exv[vindex[i]].min.c_str(),m_exv[vindex[i]].min_tm.c_str()); 
                i++;
            }
        }     
    }

    return 0;
}

//дsoe�¼�
int writeXml_SOE(std::vector<SOEstruct> m_vsoes)
{
    mskprintf("writeXml_SOE size: %d\n", m_vsoes.size()); 
                
    TiXmlDocument *writeDoc = new TiXmlDocument; //xml�ĵ�ָ��

    //�ĵ���ʽ����
    TiXmlDeclaration *decl = new TiXmlDeclaration("1.0", "UTF-8", "");
    writeDoc->LinkEndChild(decl); //д���ĵ�

    //дdatafile�ڵ�
    TiXmlElement *RootElement = new TiXmlElement("DataFile");//��Ԫ��    
    writeDoc->LinkEndChild(RootElement);

    //дHeader��Ϣ
    TiXmlElement *HeadElement = new TiXmlElement("Header");//Header
    HeadElement->SetAttribute("fileType","SOE");
    HeadElement->SetAttribute("fileVer","1.00");
    HeadElement->SetAttribute("devName","SCU");
    RootElement->LinkEndChild(HeadElement);//���ڵ�д���ĵ�

    //дDataRec ��Ϣ
    TiXmlElement *DataRecElement = new TiXmlElement("DataRec");//DataRec 
    if(m_vsoes.size()>1025)
    {
        DataRecElement->SetAttribute("num",1025);
    }
    else
    {
        DataRecElement->SetAttribute("num",m_vsoes.size());
    }
    
    RootElement->LinkEndChild(DataRecElement);//���ڵ�д���ĵ�

    //д��Ϣ�� //����ȡ1025����Ϣ
    size_t i = 0;
    if(m_vsoes.size()>1025)
    {
        i = m_vsoes.size() - 1025;
    }    
    for(; i < m_vsoes.size(); i++)
    {
        TiXmlElement *DIElement = new TiXmlElement("DI");//Header
        DIElement->SetAttribute("ioa",m_vsoes[i].ioa.c_str());
        DIElement->SetAttribute("tm",m_vsoes[i].tm.c_str());
        DIElement->SetAttribute("val",m_vsoes[i].val.c_str());
        DataRecElement->LinkEndChild(DIElement);        
    }  

    writeDoc->SaveFile(SOE_FILE_NAMEPATH_XML);
    delete writeDoc;    
    return 0;
}

//��soe�ļ�
int readXml_SOE(std::vector<SOEstruct> &m_vsoes)
{ 
    TiXmlDocument mydoc(SOE_FILE_NAMEPATH_XML);//xml�ĵ�����
    bool loadOk=mydoc.LoadFile();//�����ĵ�
    if(!loadOk)
    {
        mskprintf("could not load the SOE file.Error: %s\n",mydoc.ErrorDesc());
        IEC_LOG_RECORD(eRunType, "could not load the SOE file.Error(%s).\n", mydoc.ErrorDesc());
        return -1;
    }

    TiXmlElement *RootElement = mydoc.RootElement(); // ��Ԫ��, RootElement
    //mskprintf("%s\n", RootElement->Value());
    
    // �����ý��
    for (TiXmlElement *SonElement = RootElement->FirstChildElement(); SonElement != NULL; SonElement = SonElement->NextSiblingElement()) // ???????????
    {
        //mskprintf("%s\n", SonElement->Value());
        std::string strvalue = SonElement->Value();
        //mskprintf("strvalue=%s.\n", strvalue.c_str());

        if("DataRec" == strvalue)
        {            
           
           
            for (TiXmlElement *valElement = SonElement->FirstChildElement(); valElement != NULL; valElement = valElement->NextSiblingElement()) // ???????????
            {                   
                std::string ioa = valElement->Attribute("ioa");
                std::string tm = valElement->Attribute("tm");
                std::string val = valElement->Attribute("val");
                SOEstruct soe;
                soe.ioa = ioa;
                soe.tm = tm;
                soe.val = val;
                m_vsoes.push_back(soe);
                mskprintf("%s,%s,%s,%s\n",valElement->Value(), ioa.c_str(),tm.c_str(),val.c_str());               
            }
        }       
    }

    return 0;
}

//д�ļ�
int writeXml_FIXPT(std::vector<std::map<Juint32, FIXPTstruct>> vfixpt,std::string YMD)
{
    fileStorage_Init();
    mskprintf("YMD:%s, %d\n",YMD.c_str(),vfixpt.size());
    TiXmlDocument *writeDoc = new TiXmlDocument; //xml�ĵ�ָ��

    //�ĵ���ʽ����
    TiXmlDeclaration *decl = new TiXmlDeclaration("1.0", "UTF-8", "");
    writeDoc->LinkEndChild(decl); //д���ĵ�

     //дdatafile�ڵ�
    TiXmlElement *RootElement = new TiXmlElement("DataFile");//��Ԫ��    
    writeDoc->LinkEndChild(RootElement);

    //дHeader��Ϣ
    TiXmlElement *HeadElement = new TiXmlElement("Header");//Header
    HeadElement->SetAttribute("fileType","FIXPT");
    HeadElement->SetAttribute("fileVer","1.00");
    HeadElement->SetAttribute("devName","SCU");
    RootElement->LinkEndChild(HeadElement);//���ڵ�д���ĵ�

    for(size_t i=0; i<vfixpt.size();i++)
    {
        std::map<Juint32, FIXPTstruct> fixpt = vfixpt[i];
        if (i == 0)// DataAttr ��Ϣ
        {
            // DataAttr ��Ϣ
            TiXmlElement *DataAttrElement = new TiXmlElement("DataAttr"); // DataAttr
            DataAttrElement->SetAttribute("dataNum", vfixpt[0].size());
            DataAttrElement->SetAttribute("sectNum", vfixpt.size());
            DataAttrElement->SetAttribute("interval", "15min");
            RootElement->LinkEndChild(DataAttrElement); // ���ڵ�д���ĵ�
            
            std::map<Juint32, FIXPTstruct>::iterator it = fixpt.begin();
            // д��Ϣ��
            for (; it != fixpt.end(); ++it)
            {
                TiXmlElement *DIElement = new TiXmlElement("DI"); // Header
                DIElement->SetAttribute("ioa", it->second.ioa.c_str());
                DIElement->SetAttribute("type", it->second.type.c_str());
                DIElement->SetAttribute("unit", it->second.unit.c_str());
                DataAttrElement->LinkEndChild(DIElement);
            }
        }

        std::map<Juint32, FIXPTstruct>::iterator it = fixpt.begin();
        TiXmlElement *DataAttrElement = new TiXmlElement("DataRec"); // DataAttr
        DataAttrElement->SetAttribute("sect", std::to_string(i).c_str());
        DataAttrElement->SetAttribute("tm", it->second.tm.c_str());        
        RootElement->LinkEndChild(DataAttrElement); // ���ڵ�д���ĵ�
        // д��Ϣ��
        for (; it != fixpt.end(); ++it)
        {
            TiXmlElement *DIElement = new TiXmlElement("DI"); // Header
            DIElement->SetAttribute("Val", it->second.val.c_str());
            DataAttrElement->LinkEndChild(DIElement);
        }
    }    

    std::string path = FIXPT_FILE_NAMEPATH_XML;
    path += YMD;
    path += ".xml";
    writeDoc->SaveFile(path.c_str());
    delete writeDoc;    
    return 0;
}

int readXml_FIXPT(std::vector<std::map<Juint32, FIXPTstruct>> &vfixpt,std::string YMD)
{
    std::string path = FIXPT_FILE_NAMEPATH_XML;
    path += YMD;
    path += ".xml";
    mskprintf("FIXPT path : %s\n",path.c_str());
     TiXmlDocument mydoc(path.c_str());//xml�ĵ�����
    bool loadOk=mydoc.LoadFile();//�����ĵ�
    if(!loadOk)
    {
        mskprintf("could not load the FIXPT file.Error: %s\n",mydoc.ErrorDesc());
        IEC_LOG_RECORD(eRunType, "could not load the FIXPT file.Error(%s).\n", mydoc.ErrorDesc());
        return -1;
    }
    TiXmlElement *RootElement = mydoc.RootElement(); // ��Ԫ��, RootElement
    //mskprintf("%s\n", RootElement->Value());
    std::vector<int> vindex;

    std::map<Juint32, FIXPTstruct> fixpt;


    // �����ý��
    for (TiXmlElement *SonElement = RootElement->FirstChildElement(); SonElement != NULL; SonElement = SonElement->NextSiblingElement()) // ???????????
    {
        //mskprintf("%s\n", SonElement->Value());
        std::string strvalue = SonElement->Value();
        //mskprintf("strvalue=%s.\n", strvalue.c_str());
        
        if("DataAttr" == strvalue)
        {            

            
            for (TiXmlElement *valElement = SonElement->FirstChildElement(); valElement != NULL; valElement = valElement->NextSiblingElement()) // ???????????
            {                                  
                std::string ioa  = valElement->Attribute("ioa");
                std::string type = valElement->Attribute("type");
                std::string unit = valElement->Attribute("unit");
                
                FIXPTstruct pt;
                pt.ioa =  ioa;
                pt.type = type;
                pt.unit = unit;
                Juint32 addr = atoi(ioa.c_str());
                fixpt[addr] = pt;
                vindex.push_back(addr);
                //mskprintf("%s,%s,%s,%s\n",valElement->Value(), ioa.c_str(),type.c_str(),unit.c_str());               
            }
        }    
        if("DataRec" == strvalue)
        {    
            std::string tm  = SonElement->Attribute("tm");   
            int i=0; 
                
            for (TiXmlElement *valElement = SonElement->FirstChildElement(); valElement != NULL; valElement = valElement->NextSiblingElement()) // ???????????
            {                   
                std::string val  = valElement->Attribute("Val");
                fixpt[vindex[i]].val  = val;
                fixpt[vindex[i]].tm   = tm;

                //mskprintf("addr: %d, val:%s\n",vindex[i], fixpt[vindex[i]].val.c_str()); 
                i++;
            }

            vfixpt.push_back(fixpt);
        }     
    }
    return 0;
}

int writeXml_CO(std::vector<COstruct> m_vcos)
{
    mskprintf("writeXml_CO size: %d\n", m_vcos.size()); 
                
    TiXmlDocument *writeDoc = new TiXmlDocument; //xml�ĵ�ָ��

    //�ĵ���ʽ����
    TiXmlDeclaration *decl = new TiXmlDeclaration("1.0", "UTF-8", "");
    writeDoc->LinkEndChild(decl); //д���ĵ�

    //дdatafile�ڵ�
    TiXmlElement *RootElement = new TiXmlElement("DataFile");//��Ԫ��    
    writeDoc->LinkEndChild(RootElement);

    //дHeader��Ϣ
    TiXmlElement *HeadElement = new TiXmlElement("Header");//Header
    HeadElement->SetAttribute("fileType","CO");
    HeadElement->SetAttribute("fileVer","1.00");
    HeadElement->SetAttribute("devName","SCU");
    RootElement->LinkEndChild(HeadElement);//���ڵ�д���ĵ�

    //дDataRec ��Ϣ
    TiXmlElement *DataRecElement = new TiXmlElement("DataRec");//DataRec 
    if(m_vcos.size()>1024)
    {
        DataRecElement->SetAttribute("num",1024);
    }
    else
    {
        DataRecElement->SetAttribute("num",m_vcos.size());
    }
    
    RootElement->LinkEndChild(DataRecElement);//���ڵ�д���ĵ�

    //д��Ϣ�� //����ȡ1024����Ϣ
    size_t i = 0;
    if(m_vcos.size()>1024)
    {
        i = m_vcos.size() - 1024;
    }    
    for(; i < m_vcos.size(); i++)
    {
        TiXmlElement *DIElement = new TiXmlElement("DI");//Header
        DIElement->SetAttribute("ioa",m_vcos[i].ioa.c_str());
        DIElement->SetAttribute("tm",m_vcos[i].tm.c_str());
        DIElement->SetAttribute("cmd",m_vcos[i].cmd.c_str());
        DIElement->SetAttribute("val",m_vcos[i].val.c_str());
        DataRecElement->LinkEndChild(DIElement);        
    }  

    writeDoc->SaveFile(CO_FILE_NAMEPATH_XML);
    delete writeDoc;    
    return 0;
}

int readXml_CO(std::vector<COstruct> &m_vcos)
{
    TiXmlDocument mydoc(CO_FILE_NAMEPATH_XML);//xml�ĵ�����
    bool loadOk=mydoc.LoadFile();//�����ĵ�
    if(!loadOk)
    {
        mskprintf("could not load the SOE file.Error: %s\n",mydoc.ErrorDesc());
        IEC_LOG_RECORD(eRunType, "could not load the SOE file.Error(%s).\n", mydoc.ErrorDesc());
        return -1;
    }

    TiXmlElement *RootElement = mydoc.RootElement(); // ��Ԫ��, RootElement
    //mskprintf("%s\n", RootElement->Value());
    
    // �����ý��
    for (TiXmlElement *SonElement = RootElement->FirstChildElement(); SonElement != NULL; SonElement = SonElement->NextSiblingElement()) // ???????????
    {
        //mskprintf("%s\n", SonElement->Value());
        std::string strvalue = SonElement->Value();
        //mskprintf("strvalue=%s.\n", strvalue.c_str());

        if("DataRec" == strvalue)
        {            
        
            //mskprintf("SonElement->Value() = %d\n", num);
            for (TiXmlElement *valElement = SonElement->FirstChildElement(); valElement != NULL; valElement = valElement->NextSiblingElement()) // ???????????
            {                   
                std::string ioa = valElement->Attribute("ioa");
                std::string tm = valElement->Attribute("tm");
                std::string val = valElement->Attribute("val");
                std::string cmd = valElement->Attribute("cmd");
                COstruct co;
                co.ioa = ioa;
                co.tm = tm;
                co.cmd = cmd;
                co.val = val;
                m_vcos.push_back(co);
                //mskprintf("%s,%s,%s,%s,%s\n",valElement->Value(), ioa.c_str(),tm.c_str(),val.c_str(),cmd.c_str());               
            }
        }       
    }

    return 0;
}

int writeXml_FRO(std::vector<std::map<Juint32, FRZstruct>> vfro, std::map<Juint32, FRZstruct> dayfro, std::string YMD)
{
    fileStorage_Init();
    mskprintf("YMD:%s, %d\n",YMD.c_str(),vfro.size());
    TiXmlDocument *writeDoc = new TiXmlDocument; //xml�ĵ�ָ��

    //�ĵ���ʽ����
    TiXmlDeclaration *decl = new TiXmlDeclaration("1.0", "UTF-8", "");
    writeDoc->LinkEndChild(decl); //д���ĵ�

    //дdatafile�ڵ�
    TiXmlElement *RootElement = new TiXmlElement("DataFile");//��Ԫ��    
    writeDoc->LinkEndChild(RootElement);

    //дHeader��Ϣ
    TiXmlElement *HeadElement = new TiXmlElement("Header");//Header
    HeadElement->SetAttribute("fileType","FRZ");
    HeadElement->SetAttribute("fileVer","1.00");
    HeadElement->SetAttribute("devName","SCU");
    RootElement->LinkEndChild(HeadElement);///���ڵ�д���ĵ�

    for(size_t i=0; i<vfro.size();i++)
    {
        std::map<Juint32, FRZstruct> fro = vfro[i];
        if (i == 0)// DataAttr ��Ϣ
        {
            // DataAttr ��Ϣsect
            TiXmlElement *DataAttrElement = new TiXmlElement("DataAttr"); // DataAttr
            DataAttrElement->SetAttribute("type", "FixFrz");
            DataAttrElement->SetAttribute("dataNum", vfro[0].size());
            DataAttrElement->SetAttribute("sectNum", vfro.size());
            DataAttrElement->SetAttribute("interval", "15min");
            RootElement->LinkEndChild(DataAttrElement); // ���ڵ�д���ĵ�
            
            std::map<Juint32, FRZstruct>::iterator it = fro.begin();
            // д��Ϣ��
            for (; it != fro.end(); ++it)
            {
                TiXmlElement *DIElement = new TiXmlElement("DI"); // Header
                DIElement->SetAttribute("ioa", it->second.ioa.c_str());
                DIElement->SetAttribute("type", it->second.type.c_str());
                DIElement->SetAttribute("unit", it->second.unit.c_str());
                DataAttrElement->LinkEndChild(DIElement);
            }

            // DataAttr ��Ϣ
            TiXmlElement *DataAttrElementDay = new TiXmlElement("DataAttr"); // DataAttr
            DataAttrElementDay->SetAttribute("type", "DayFrz");
            DataAttrElementDay->SetAttribute("dataNum", dayfro.size());
            RootElement->LinkEndChild(DataAttrElementDay); // ���ڵ�д���ĵ�
            
            std::map<Juint32, FRZstruct>::iterator itday = dayfro.begin();
            // д��Ϣ��
            for (; itday != dayfro.end(); ++itday)
            {
                TiXmlElement *DIElement = new TiXmlElement("DI"); // Header
                DIElement->SetAttribute("ioa", itday->second.ioa.c_str());
                DIElement->SetAttribute("type", itday->second.type.c_str());
                DIElement->SetAttribute("unit", itday->second.unit.c_str());
                DataAttrElementDay->LinkEndChild(DIElement);
            }
        }

        std::map<Juint32, FRZstruct>::iterator it = fro.begin();
        TiXmlElement *DataAttrElement = new TiXmlElement("DataRec"); // DataAttr
        DataAttrElement->SetAttribute("sect", std::to_string(i+1).c_str());
        DataAttrElement->SetAttribute("tm", it->second.tm.c_str());        
        RootElement->LinkEndChild(DataAttrElement); // ���ڵ�д���ĵ�
        // д��Ϣ��
        for (; it != fro.end(); ++it)
        {
            TiXmlElement *DIElement = new TiXmlElement("DI"); // Header
            DIElement->SetAttribute("Val", it->second.val.c_str());
            DataAttrElement->LinkEndChild(DIElement);
        }
    }    

    //�ն���
    std::map<Juint32, FRZstruct>::iterator it = dayfro.begin();
    TiXmlElement *DataAttrElement = new TiXmlElement("DataRec"); // DataAttr
    DataAttrElement->SetAttribute("type", "DayFrz");
    DataAttrElement->SetAttribute("tm", it->second.tm.c_str());
    RootElement->LinkEndChild(DataAttrElement); // ���ڵ�д���ĵ�
    // д��Ϣ��
    for (; it != dayfro.end(); ++it)
    {
        TiXmlElement *DIElement = new TiXmlElement("DI"); // Header
        DIElement->SetAttribute("Val", it->second.val.c_str());
        DataAttrElement->LinkEndChild(DIElement);
    }

    std::string path = FRZ_FILE_NAMEPATH_XML;
    path += YMD;
    path += ".xml";
    writeDoc->SaveFile(path.c_str());
    delete writeDoc;    
    return 0;
}

int readXml_FRO(std::vector<std::map<Juint32, FRZstruct>> &vfro, std::string YMD)
{
    std::string path = FRZ_FILE_NAMEPATH_XML;
    path += YMD;
    path += ".xml";
    mskprintf("FRZ path : %s\n",path.c_str());
    TiXmlDocument mydoc(path.c_str());//xml�ĵ�����
    bool loadOk=mydoc.LoadFile();//�����ĵ�
    if(!loadOk)
    {
        mskprintf("could not load the FRZ file.Error: %s\n",mydoc.ErrorDesc());
        IEC_LOG_RECORD(eRunType, "could not load the FRZ file.Error(%s).\n", mydoc.ErrorDesc());
        return -1;
    }
    TiXmlElement *RootElement = mydoc.RootElement(); // ��Ԫ�� RootElement
    //mskprintf("%s\n", RootElement->Value());
    std::vector<int> vindex;

    std::map<Juint32, FRZstruct> frz;


    // �����ý��
    for (TiXmlElement *SonElement = RootElement->FirstChildElement(); SonElement != NULL; SonElement = SonElement->NextSiblingElement()) // ???????????
    {
        mskprintf("%s\n", SonElement->Value());
        std::string strvalue = SonElement->Value();
        mskprintf("strvalue=%s.\n", strvalue.c_str());
        static size_t count = 0;
        
        if("DataAttr" == strvalue)
        {            
            std::string strtype = SonElement->Attribute("type") ;
            mskprintf("strtype: %s.\n",strtype.c_str());
            if(strtype == "DayFrz")
            {
                mskprintf("00\n");
                continue;
            }
            mskprintf("01\n");

            int dataNum = atoi(SonElement->Attribute("dataNum"));
            mskprintf("dataNum = %d\n", dataNum);
            int sectNum = atoi(SonElement->Attribute("sectNum"));
            mskprintf("sectNum= %d\n", sectNum);
            count = sectNum;
            
            for (TiXmlElement *valElement = SonElement->FirstChildElement(); valElement != NULL; valElement = valElement->NextSiblingElement()) // ???????????
            {                                  
                std::string ioa  = valElement->Attribute("ioa");
                std::string type = valElement->Attribute("type");
                std::string unit = valElement->Attribute("unit");
                
                FRZstruct pt;
                pt.ioa =  ioa;
                pt.type = type;
                pt.unit = unit;
                Juint32 addr = atoi(ioa.c_str());
                frz[addr] = pt;
                vindex.push_back(addr);
                mskprintf("%s,%s,%s,%s\n",valElement->Value(), ioa.c_str(),type.c_str(),unit.c_str());               
            }
        }  

        if("DataRec" == strvalue)
        {  
            std::string tm  = SonElement->Attribute("tm");   
            int i=0; 
                
            for (TiXmlElement *valElement = SonElement->FirstChildElement(); valElement != NULL; valElement = valElement->NextSiblingElement()) // ???????????
            {                   
                std::string val  = valElement->Attribute("Val");
                frz[vindex[i]].val  = val;
                frz[vindex[i]].tm   = tm;

                if(tm.size()>0)
                {
                    mskprintf("tm: %s. addr: %d, val:%s\n",tm.c_str(), vindex[i], frz[vindex[i]].val.c_str()); 
                }
                
                i++;
            }

            if(vfro.size() < count)
            {
                vfro.push_back(frz);
            }
            
        }     
    }
    return 0;
}

int writeXml_FLOWREV(std::vector< FLOWREVstruct> vflowrev, std::string YMD)
{
    mskprintf("writeXml_FLOWREV size: %d\n", vflowrev.size()); 
                
    TiXmlDocument *writeDoc = new TiXmlDocument; //xml�ĵ�ָ��

    //�ĵ���ʽ����
    TiXmlDeclaration *decl = new TiXmlDeclaration("1.0", "UTF-8", "");
    writeDoc->LinkEndChild(decl); //д���ĵ�

     //дdatafile�ڵ�
    TiXmlElement *RootElement = new TiXmlElement("DataFile");//��Ԫ��    
    writeDoc->LinkEndChild(RootElement);

    //дHeader��Ϣ
    TiXmlElement *HeadElement = new TiXmlElement("Header");//Header
    HeadElement->SetAttribute("fileType","FLOWREV");
    HeadElement->SetAttribute("fileVer","1.00");
    HeadElement->SetAttribute("devName","SCU");
    RootElement->LinkEndChild(HeadElement);//???????

    //DataRec 
    TiXmlElement *DataRecElement = new TiXmlElement("DataRec");//DataRec 
    if(vflowrev.size()>1024)
    {
        DataRecElement->SetAttribute("num",1024);
    }
    else
    {
        DataRecElement->SetAttribute("num",vflowrev.size());
    }
    
    RootElement->LinkEndChild(DataRecElement);//???????

    size_t i = 0;
    if(vflowrev.size()>1024)
    {
        i = vflowrev.size() - 1024;
    }    
    for(; i < vflowrev.size(); i++)
    {
        TiXmlElement *DIElement = new TiXmlElement("DI");//Header
        DIElement->SetAttribute("ioa",vflowrev[i].ioa.c_str());
        DIElement->SetAttribute("tm",vflowrev[i].tm.c_str());
        DIElement->SetAttribute("val",vflowrev[i].val.c_str());
        DataRecElement->LinkEndChild(DIElement);        
    }  

    writeDoc->SaveFile(FLOWREV_FILE_NAMEPATH_XML);
    delete writeDoc;    
    return 0;
}

int readXml_FLOWREV(std::vector< FLOWREVstruct> &m_vFLOWREVInfoData)
{
    TiXmlDocument mydoc(FLOWREV_FILE_NAMEPATH_XML);//xml�ĵ�����
    bool loadOk=mydoc.LoadFile();//�����ĵ�
    if(!loadOk)
    {
        mskprintf("could not load the flowrev file.Error: %s\n",mydoc.ErrorDesc());
        IEC_LOG_RECORD(eRunType, "could not load the flowrev file.Error(%s).\n", mydoc.ErrorDesc());
        return -1;
    }

    TiXmlElement *RootElement = mydoc.RootElement(); // ��Ԫ��, RootElement
    //mskprintf("%s\n", RootElement->Value());
    
    // �����ý��
    for (TiXmlElement *SonElement = RootElement->FirstChildElement(); SonElement != NULL; SonElement = SonElement->NextSiblingElement()) // ???????????
    {
        //mskprintf("%s\n", SonElement->Value());
        std::string strvalue = SonElement->Value();
        //mskprintf("strvalue=%s.\n", strvalue.c_str());

        if("DataRec" == strvalue)
        {            
            int num = atoi(SonElement->Attribute("num"));
            mskprintf("SonElement->Value() = %d\n", num);
            for (TiXmlElement *valElement = SonElement->FirstChildElement(); valElement != NULL; valElement = valElement->NextSiblingElement()) // ???????????
            {                   
                std::string ioa = valElement->Attribute("ioa");
                std::string tm = valElement->Attribute("tm");
                std::string val = valElement->Attribute("val");
                FLOWREVstruct flowrev;
                flowrev.ioa = ioa;
                flowrev.tm = tm;
                flowrev.val = val;
                m_vFLOWREVInfoData.push_back(flowrev);
                //mskprintf("%s,%s,%s,%s\n",valElement->Value(), ioa.c_str(),tm.c_str(),val.c_str());               
            }
        }       
    }

    return 0;
}

int writeXml_ULOG(std::vector<ULOGstruct> m_vulogs)
{
    IEC_LOG_RECORD(eRunType, "start write ulog.xml");
    mskprintf("writeXml_ULOG size: %d\n", m_vulogs.size()); 
                
    TiXmlDocument *writeDoc = new TiXmlDocument; //xml�ĵ�ָ��

    //�ĵ���ʽ����
    TiXmlDeclaration *decl = new TiXmlDeclaration("1.0", "UTF-8", "");
    writeDoc->LinkEndChild(decl); //д���ĵ�

    //дdatafile�ڵ�
    TiXmlElement *RootElement = new TiXmlElement("DataFile");//��Ԫ��    
    writeDoc->LinkEndChild(RootElement);

    //дHeader��Ϣ
    TiXmlElement *HeadElement = new TiXmlElement("Header");//Header
    HeadElement->SetAttribute("fileType","ULOG");
    HeadElement->SetAttribute("fileVer","1.00");
    HeadElement->SetAttribute("devName",CRealDataManager::CreateInstance().m_ParamInfoData[0x8009].data.charValue);
    RootElement->LinkEndChild(HeadElement);//���ڵ�д���ĵ�

    //дDataRec ��Ϣ
    TiXmlElement *DataRecElement = new TiXmlElement("DataRec");//DataRec 
    if(m_vulogs.size()>1024)
    {
        DataRecElement->SetAttribute("num",1024);
    }
    else
    {
        DataRecElement->SetAttribute("num",m_vulogs.size());
    }
    
    RootElement->LinkEndChild(DataRecElement);//���ڵ�д���ĵ�

    //д��Ϣ�� //����ȡ1024����Ϣ
    size_t i = 0;
    if(m_vulogs.size()>1024)
    {
        i = m_vulogs.size() - 1024;
    }    
    for(; i < m_vulogs.size(); i++)
    {
        TiXmlElement *DIElement = new TiXmlElement("DI");//Header
        DIElement->SetAttribute("logType",m_vulogs[i].logType.c_str());
        DIElement->SetAttribute("tm",m_vulogs[i].tm.c_str());
        DIElement->SetAttribute("txt",m_vulogs[i].txt.c_str());
        DIElement->SetAttribute("val",m_vulogs[i].val.c_str());
        DataRecElement->LinkEndChild(DIElement);        
    }  

    writeDoc->SaveFile(ULOG_FILE_NAMEPATH_XML);
    delete writeDoc;    

    IEC_LOG_RECORD(eRunType, "end write ulog.xml");
    return 0;   
}

int readXml_ULOG(std::vector<ULOGstruct> &m_vulogs)
{
    TiXmlDocument mydoc(ULOG_FILE_NAMEPATH_XML);//xml�ĵ�����
    bool loadOk=mydoc.LoadFile();//�����ĵ�
    if(!loadOk)
    {
        mskprintf("could not load the ulog file.Error: %s\n",mydoc.ErrorDesc());
        IEC_LOG_RECORD(eRunType, "could not load the ULOG file.Error(%s).\n", mydoc.ErrorDesc());
        return -1;
    }

    TiXmlElement *RootElement = mydoc.RootElement(); // ��Ԫ�� RootElement
    //mskprintf("%s\n", RootElement->Value());
    
    // �����ý��
    for (TiXmlElement *SonElement = RootElement->FirstChildElement(); SonElement != NULL; SonElement = SonElement->NextSiblingElement()) // ???????????
    {
        //mskprintf("%s\n", SonElement->Value());
        std::string strvalue = SonElement->Value();
        //mskprintf("strvalue=%s.\n", strvalue.c_str());

        if("DataRec" == strvalue)
        {            
            
            //mskprintf("SonElement->Value() = %d\n", num);
            for (TiXmlElement *valElement = SonElement->FirstChildElement(); valElement != NULL; valElement = valElement->NextSiblingElement()) // ???????????
            {                   
                std::string logType = valElement->Attribute("logType");
                std::string tm      = valElement->Attribute("tm");                
                std::string txt     = valElement->Attribute("txt");
                std::string val     = valElement->Attribute("val");

                ULOGstruct ulog;
                ulog.logType = logType;
                ulog.tm = tm;
                ulog.txt = txt;
                ulog.val = val;
                m_vulogs.push_back(ulog);
                //mskprintf("%s,%s,%s,%s,%s\n",valElement->Value(), logType.c_str(),tm.c_str(),val.c_str(),txt.c_str());               
            }
        }       
    }

    return 0;
}
