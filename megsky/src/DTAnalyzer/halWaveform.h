// #ifndef __WAVEFORM__H__
// #define __WAVEFORM__H__

// #define HW_DEVICE_ID_WAVEFORM_SAMPLER	"waveform_sampler"

// // 定义波形采样设备结构
// typedef struct tag_WAVEFORM_SAMPLER_DEVICE {
// 	struct tag_HW_DEVICE base;

// 	/**
// 	 * @brief 读取波形数据
// 	 * @param[in] dev: 设备描述
// 	 * @param[out] data: 波形数据缓冲区（返回上n个完整周波数据）
// 	 * @param[in] dsize: 数据缓冲区大小
// 	 * @param[in] lastn: 上n个周波，n最大为20
// 	 * @return 成功返回接收数据长度；失败返回错误码。
// 	 */
// 	int32 (*read_waveform)(struct tag_WAVEFORM_SAMPLER_DEVICE* dev,
// 			void* data,
// 			int32 dsize,
// 			uint32_t lastn);

// 	/**
// 	 * @brief 读取实时监测量数据
// 	 * @param[in] dev: 设备描述
// 	 * @param[out] data: 实时监测量数据缓冲区（返回上n个数据）
// 	 * @param[in] dsize: 数据缓冲区大小
// 	 * @param[in] lastn: 上n个周波实时监测量数据，n最大为500
// 	 * @return 成功返回接收数据长度；失败返回错误码。
// 	 */
// 	int32 (*read_real_monitor_data)(struct tag_WAVEFORM_SAMPLER_DEVICE* dev,
// 			void* data,
// 			int32 dsize,
// 			uint32_t lastn);
// 	// 用户扩展部分
// 	int fd;
// } WAVEFORM_SAMPLER_DEVICE_T;

// #endif

