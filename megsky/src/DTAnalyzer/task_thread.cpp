#include <openssl/md5.h>
#include "mqtt_pub_inter.h"
#include "param_json.h"
#include "task_thread.h"
#include <cmath>
#include <iostream>
#include <sys/types.h>
#include <dirent.h>
#include <vector>
#include <string.h>

CDataObj::CDataObj(void)
{
    m_startFlag = false;
}

CDataObj::~CDataObj()
{
}

CTaskThread::CTaskThread(void)
    : m_pMng(NULL)
{
}

CTaskThread::~CTaskThread()
{
}

void CTaskThread::SetPackageAcquireManager(CTaskManager *p)
{
    m_pMng = p;
}

void CTaskThread::run(void)
{
    m_pMng->thread_prev();
    while (CSL_thread::IsEnableRun())
    {
        m_pMng->thread_func();
    }
    m_pMng->thread_exit();
}

std::string CTaskThread::ThreadName(void) const
{
    return std::string("MQTT 内部Broker线程");
}

void CTaskThread::RecordLog(const std::string &sMsg)
{
}

//***************************************************************
// 报文获取管理者
//***************************************************************
CTaskManager &CTaskManager::CreateInstance()
{
    static CTaskManager Mng;
    return Mng;
}

bool CTaskManager::Init(void)
{
    bool bRet(false);
 
    std::list<CFileObj>::iterator iter = CParamManager::CreateInstance().m_FileObjs.begin();
    for (; iter != CParamManager::CreateInstance().m_FileObjs.end(); ++iter)
    {
        CFileObj obj = *iter;
        std::string topic = obj.m_Topic + "+";
        CMqttClientInterManager::CreateInstance().m_topics.push_back(topic);
        CMqttClientInterManager::CreateInstance().agent_mqtt_msg_subscribe((char *)topic.c_str(), QOS);
    }

    n_ops = OPS_CHECK;  //1加密校验

    SMTimeInfo currenttime = ::ii_get_current_mtime();
    currenttime.nHour = 0;
    currenttime.nMinute = 0;
    currenttime.nSecond = 0;
    m_baseTime = CEPTime(currenttime);
    m_dDay = 0;
    m_dmin = 0;

    bRet = m_Thread.start();
    return bRet;
}

void CTaskManager::Exit(void)
{

    m_Thread.close();
    m_Thread.wait_for_end();
}

void CTaskManager::thread_prev(void)
{
}

void CTaskManager::thread_func(void)
{
    OnRun();
}

void CTaskManager::thread_exit(void)
{
}

void CTaskManager::OnRun(void)
{
    ii_sleep(100);

    switch (n_ops)
    {
    // 加密校验
    case OPS_CHECK:
        mskprintf(":1\n");
        MqttGetDevEsn();
        ii_sleep(1000);
        n_ops = OPS_INIT;  
        break;
        // 向操作系统注册
    case OPS_OS_SYSTEM:
        mskprintf(":2\n");
        MqttOS_REG();
        ii_sleep(1000);
        break;
    case OPS_INIT:       
        mskprintf("设置模型\n");
        MqttPackSetModelInfo_PQA();        
        ii_sleep(1000);
        break;
    case OPS_INIT_OK:
        mskprintf("注册guid\n");
        MqttPackDevRegister();
        ii_sleep(1000);
        break;
    case OPS_REG: 
        mskprintf("获取GUID\n");
        MqttPackGetDeviceGuid(); //获取设备的guid
        ii_sleep(1000);
        MqttPackGetGuid(); // 获取GUID
        

		m_pdAnalyzer_T.StartCount();
        //开关
        m_MCCBAnalyzer_T.StartCount();
        //电表
        m_METERAnalyzer_T.StartCount();  
        ii_sleep(1000);
		//licong
        MqttPackGetParameter();
        MqttPackGetRealTimeData();
        
        ii_sleep(1000);
		//m_pdAnalyzer_T.StartCount();  
		//
        break;        
    case OPS_REG_OK: // 获取GUID成功，获取一次数据
        mskprintf(":OPS_REG_OK\n");
        mskprintf("获取实时数据和定值\n");

        //交采,
        MqttPackGetRealTimeData();
        MqttPackGetParameter();
        ii_sleep(1000);
        m_pdAnalyzer_T.StartCount();   

        break;
    case OPS_IDLE: // 成功后，定时计算电能质量数据并发送        
        break;
    default:
        break;
    }

   
    
    
    // 成功后，定时计算电能质量数据并发送  
    if(m_pdAnalyzer_T.CalOvertime())
    {
        
        MqttPackGetRealTimeData();//定期从数据中心读取数据
        pdAnalyzer_analysis();
        //MqttPackGetRealTimeData_DTA();  //测试
    }
    
    if(m_MCCBAnalyzer_T.CalOvertime())
    {
        MqttPackGetRealTimeData_DTA();  //获取端设备实时数据
        //pdAnalyzer_analysis();
    }

    if(m_METERAnalyzer_T.CalOvertime())
    {
        MqttPackGetRealTimeData_METER();   //获取电表实时数据
        //pdAnalyzer_analysis();   
    }
    
    

    

    //交采计算 
	if(m_ocLoadRate_T.CalOvertime())//1秒计算一次
    {
        CParamManager::CreateInstance().Unpack_ocAnalyzerData(); //计算可开放性容量负载
        MqttPackSetRealTimeData_oc();  //周期上送  
        MqttPackNotificationData_1_oc();
    }
	
    if(m_ocAnalyzer_T.CalOvertime())//每一分钟上报一次
    {
        //终端累计运行时间
        MqttGetDevStatus(); 

        //判断是新的一分钟还是上一分钟
        SMTimeInfo now_octm = ii_get_current_mtime();
        mskprintf("%04d-%02d-%02d %02d:%02d:%02d:%03d\n",now_octm.nYear,now_octm.nMonth,now_octm.nDay,now_octm.nHour,now_octm.nMinute,now_octm.nSecond,now_octm.nMinute);

        if(now_octm.nMinute != m_lastTime_oc.nMinute)//按分钟计算配变负载率计算周期
        {
            m_dmin++;
            if(m_dmin == (int)CParamManager::CreateInstance().m_Param_value["LoadRate_CalcCyc"])  //计算周期 清零一次
            {
                //初始化
                CParamManager::CreateInstance().initocData();  
                m_dmin = 0;
            }           
        }
        if(now_octm.nDay != m_lastTime_oc.nDay)//按天计算可开放容量计算周期
        {
            m_dDay++;
            //可开放容量计算周期 
            if(m_dDay == (int)CParamManager::CreateInstance().m_Param_value["ResLoad_CalcCyc"])
            {
                CParamManager::CreateInstance().initoc_Load_Data(); 
                m_dDay = 0;
            }              
        }
        //时间同步  
        m_lastTime_oc = now_octm;
        
        
        //计算
        //CParamManager::CreateInstance().Unpack_ocAnalyzerData();
        //MqttPackSetRealTimeData_oc();  //周期上送
        //MqttPackNotificationData_1_oc();
        //m_T.StartCount();   
    }
}

void CTaskManager::Start(void)
{
    std::list<CDataObj *>::iterator itInit = m_dataObj.begin();
    for (; itInit != m_dataObj.end(); itInit++)
    {
        CDataObj *objd = *itInit;
        if (objd->m_startFlag)
        {
            MqttPackOnline(*itInit); // 一直发上线就可以了
            IEC_LOG_RECORD(eRunType, "guid (%s)online.", objd->m_guid.guid.c_str());
            ii_sleep(1000);
        }
    }
}

void CTaskManager::Stop(void)
{
}

bool CTaskManager::UnpackData(mqtt_data_info_s &real_data)
{
    neb::CJsonObject oJson;
    if (!oJson.Parse(real_data.msg_send))
    {
        IEC_LOG_RECORD(eErrType, "json_loadb real_data.msg_send failed.");
        return false;
    }

    std::string mssm = oJson.ToString();

    if (mssm.size() > 0)
    {
        if(!strstr(real_data.pubtopic, "/Broadcast/JSON/report/notification/"))
        {
            // mskprintf("unpack mqtt topic:%s.\r\n", real_data.pubtopic);
            // mskprintf("unpack mqtt msg:%s.\r\n", mssm.c_str());
        }        
    }

    C256String pub_topic;
    std::string appName = GetFirsAppName(real_data.pubtopic); // 截取 首字符
    // 获取token值
    const char *tokenStr = NULL;
    std::string stokenStr = UnpackToken(oJson);
    if (stokenStr.size() > 0)
    {
        tokenStr = stokenStr.c_str();
        //mskprintf("tokenStr:%s.\r\n", tokenStr);
    }

    // 解析模型设置成功
    if (strstr(real_data.pubtopic, CFG_DATA_CENTER_NAME "/" CFG_APP_NAME "/JSON/set/response/model"))
    {
        std::string status;
        oJson.Get("status", status);
        mskprintf("status = %s\n", status.c_str());
        if (status == "OK")
        {
            n_ops = OPS_INIT_OK;
        }
    }
    // 设备注册成功
    else if (strstr(real_data.pubtopic, CFG_DATA_CENTER_NAME "/" CFG_APP_NAME "/JSON/set/response/devRegister"))
    {   
        std::string status;
        oJson.Get("status", status);
        mskprintf("status = %s\n", status.c_str());
        if (status == "OK")
        {
            n_ops = OPS_REG;
        }
    }
    // 设备guid查询
    else if (strstr(real_data.pubtopic, CFG_DATA_CENTER_NAME "/" CFG_APP_NAME "/JSON/get/response/guid"))
    {
        neb::CJsonObject oBodyJson;
        oJson.Get("body", oBodyJson);
        mskprintf("oBodyJson = %s\n", oBodyJson.ToString().c_str());
        if (!oBodyJson.IsArray())
        {
            return false;
        }
        int asize = oBodyJson.GetArraySize();
        mskprintf("asize = %d\n", asize);
        bool flag_devguid = false;  
        for (int i = 0; i < asize; i++)
        {
            neb::CJsonObject body_data;
            if (!oBodyJson.Get(i, body_data))
            {
                return false;
            }

            mskprintf("body_data = %s\n", body_data.ToString().c_str());

            std::string model;
            std::string port;
            std::string addr;
            std::string desc;
            std::string guid;
            std::string dev;

            body_data.Get("model", model);
            body_data.Get("port", port);
            body_data.Get("addr", addr);
            body_data.Get("desc", desc);
            body_data.Get("guid", guid);
            body_data.Get("dev", dev);

            mskprintf("model = %s\n", model.c_str());
            mskprintf("port = %s\n", port.c_str());

            if (model == CParamManager::CreateInstance().m_devModel && port == CParamManager::CreateInstance().m_devPort && addr == CParamManager::CreateInstance().m_devAddr && desc == CParamManager::CreateInstance().m_devDesc)
            {
                flag_devguid = true;
                CParamManager::CreateInstance().m_devGuid = guid;
                CParamManager::CreateInstance().m_devDev = dev;
                n_ops = OPS_REG_OK;
                mskprintf("guid = %s\n", guid.c_str());
                mskprintf("dev = %s\n", dev.c_str());
                mskprintf("model = %s\n", model.c_str()); 

                char topic_str[256] = {0};
                // 订阅ADC数据变化上报主题 
                snprintf(topic_str, 128, "acMeter/Broadcast/JSON/report/notification/%s/%s", model.c_str(), dev.c_str());
                CMqttClientInterManager::CreateInstance().agent_mqtt_msg_subscribe(topic_str, QOS);
                mskprintf("subscribe: %s\r\n", topic_str);
                break;
            }
        }
        //未查询到guid,注册guid
        if(!flag_devguid)
        {
            MqttPackDevRegister();
        }
    }
    // 设备guid查询
    else if (strstr(real_data.pubtopic, CFG_DATA_CENTER_NAME "/" CFG_APP_NAME "/JSON/get/response/devRegister"))
    {
        neb::CJsonObject oBodyJson;
        if (!oJson.Get("body", oBodyJson))
        {
            IEC_LOG_RECORD(eErrType, "devRegister response: body = NULL.");
            return false;
        }
        if (!oBodyJson.IsArray())
        {
            IEC_LOG_RECORD(eErrType, "devRegister response: body is not Array.");
            return false;
        }

        int asize = oBodyJson.GetArraySize();
        bool flag_devguid = false;
        for (int i = 0; i < asize; i++)
        {
            neb::CJsonObject outerObj;
            if (!oBodyJson.Get(i, outerObj))
            {
                return false;
            }

            std::string model;
            std::string port;
            outerObj.Get("model", model);
            outerObj.Get("port", port);

            neb::CJsonObject innerArr;
            if (!outerObj.Get("body", innerArr) || !innerArr.IsArray())
            {
                continue;
            }

            int innerSize = innerArr.GetArraySize();
            for (int j = 0; j < innerSize; ++j)
            {
                neb::CJsonObject devObj;
                if (!innerArr.Get(j, devObj))
                {
                    continue;
                }
                std::string dev;
                devObj.Get("dev", dev);
                if (!dev.empty())
                {
                    CParamManager::CreateInstance().m_dev_model[dev] = model;
                    mskprintf("devRegister add: model=%s, port=%s, dev=%s\n", model.c_str(), port.c_str(), dev.c_str());
                }
            }
        }
    }   
    // 模型内容查询 
    else if (strstr(real_data.pubtopic, CFG_DATA_CENTER_NAME "/" CFG_APP_NAME "/JSON/get/response/model"))
    {
        //dataCenter/MskpqAnlayzer/JSON/get/response/model
        /* if (UnpackDevModelData(oJson))
        {
            n_ops = OPS_REG_OK; 
        } */
    }
    // 定值内容查询 // 定值变更通知  
    else if (strstr(real_data.pubtopic, CFG_DATA_CENTER_NAME "/" CFG_APP_NAME "/JSON/get/response/parameter")
    || strstr(real_data.pubtopic, "dataCenter/Broadcast/JSON/report/notification/parameter")||strstr(real_data.pubtopic, "MskIEC104/dataCenter/JSON/set/request/parameter"))
    {
		IEC_LOG_RECORD(eRunType,"the fist get parameter of datacenter   "); 
        mskprintf("parameter\n");
        UnpackDevParameterData(oJson);
        n_ops = OPS_IDLE;
    }   
    // 实时数据查询
    else if (strstr(real_data.pubtopic, CFG_DATA_CENTER_NAME "/" CFG_APP_NAME "/JSON/get/response/realtime"))
    {
        mskprintf("realtime\n");
        UnpackRealData(oJson);
        n_ops = OPS_IDLE;
    }
    /* 变化上报主题 */
    else if (strstr(real_data.pubtopic, "/Broadcast/JSON/report/notification/"))
    {
        mskprintf("notification\n");
        std::string datatype;
        oJson.Get("datatype", datatype);
        if (datatype == "0") // 遥测，计算
        {
            UnpackNotificationData(oJson);
        }
    }
    //获取设备ESN,用于注册码解码
    else if (strstr(real_data.pubtopic, CFG_OS_NAME "/" CFG_APP_NAME "/JSON/response/devInfo"))
    {
        neb::CJsonObject body;
        oJson.Get("body", body);

        std::string devesn;
        body.Get("esn", devesn);
        //body.Get("osVer", devesn); // esn 目前是空的，用osVer代替
        mskprintf("esn:%s\n", devesn.c_str());
        char currentCode[4096] = ""; // 注册码
        CalMachinecode((char *)devesn.c_str(), currentCode);
        //mskprintf("code: %s\n", currentCode);

        // 文件路径
        std::vector<std::string> file_name;
        std::string path = "/data/app/common";
        GetFileNames(path, file_name);
        std::string filepathall;
        for (int i = 0; i < file_name.size(); i++)
        {
            // cout << file_name[i] << endl;
            filepathall += file_name[i];
        }
        std::string::size_type idx;
        idx = filepathall.find(currentCode); // 在a中查找b.
        if (idx == std::string::npos)             // 不存在。
        {
            mskprintf("Unauthorized equipment! \n");
            IEC_LOG_RECORD(eErrType, "Unauthorized equipment! code:");
            exit(0);
        }
        else
        {
            mskprintf("authorized equipment! code:\n");
            n_ops = OPS_OS_SYSTEM;
        }
    }
    //获取设备累计运行时间
    else if (strstr(real_data.pubtopic,CFG_OS_NAME "/" CFG_APP_NAME "/JSON/response/devStatus"))//3.2.7获取设备状态
    {
        neb::CJsonObject body;
        oJson.Get("body", body);

        int devRunTime;
        body.Get("devRunTime", devRunTime);        
        CParamManager::CreateInstance().m_pdAnalyzer_value["TTU_Run_Tm"] = devRunTime/60.0;

        mskprintf("@@@TTU_Run_Tm: %f\n",CParamManager::CreateInstance().m_pdAnalyzer_value["TTU_Run_Tm"]);
        
    }
    // 注册
    else if (strstr(real_data.pubtopic, CFG_OS_NAME "/" CFG_APP_NAME "/JSON/response/register"))
    {
        oJson.Get("statusCode", CParamManager::CreateInstance().m_OS_statusCode);
        oJson.Get("statusDesc", CParamManager::CreateInstance().m_OS_statusDesc);

        mskprintf("statusCode:%d  statusDesc: %s\n", CParamManager::CreateInstance().m_OS_statusCode, CParamManager::CreateInstance().m_OS_statusDesc.c_str());
        n_ops = OPS_INIT;
        return true;
    }
    // 保活
    else if (strstr(real_data.pubtopic, CFG_OS_NAME "/" CFG_APP_NAME "/JSON/request/keepAlive"))
    {
        mqtt_header_s jsheader = {0};
        int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, "123");
        if (ret != MQTT_OK)
        {
            MG_LOG_E("\nJson head fill failed.\n");
            return false;
        }

        neb::CJsonObject oJson;

        oJson.Add("token", 123);
        oJson.Add("timestamp", jsheader.timestamp);
        oJson.Add("statusCode", CParamManager::CreateInstance().m_OS_statusCode);
        oJson.Add("statusDesc", CParamManager::CreateInstance().m_OS_statusDesc);
        char pub_topic[100] = CFG_APP_NAME "/smiOS/JSON/response/keepAlive";
        std::string mssm = oJson.ToString();
        if (mssm.size() > 0)
        {
            mskprintf("find dev list topic:%s\r\n", pub_topic);
            mskprintf("find dev list msg:%s\r\n", mssm.c_str());
            CMqttClientInterManager::CreateInstance().agent_MqttMsgPub((char *)(mssm.c_str()), (char *)pub_topic, 0);
        }
        return true;
    }
    else if (strstr(real_data.pubtopic, CFG_APP_NAME "/JSON/action/response/transTxFrm"))
    {
        std::string TransType;
        oJson.Get("TransType", TransType);
        mskprintf("TransType:%s\n",TransType);
        std::string code;
        oJson.Get("code",code);
        if(code != "0")
        {
            mskprintf("返回结果失败\n");
            return false;
        }
        else
        {
            if (TransType == "dataGet")  
            {
                mskprintf(" 实时数据\n");
                UnPackRealData_MCCB(oJson);
            }
            else if(TransType == "cmdGet")
            {
                IEC_LOG_RECORD(eRunType,"the fist get parameter from acMeter ");
                mskprintf("parameter\n");
                //UnpackDevParameterData(oJson);
            }
            else if(TransType == "cmdSet")
            {
                mskprintf("cmdset\n");
                mskprintf("下发参数成功\n");
            }
        }
        n_ops = OPS_IDLE;
    }
    if(strstr(real_data.pubtopic,CFG_DATA_CENTER_NAME "/" CFG_APP_NAME "/JSON/get/response/meterRealtime" ))
    {
        std::string meterdev;
        oJson.Get("dev",meterdev);
        mskprintf("dev:",meterdev);
        UnPackRealData_Meter(oJson);
        n_ops = OPS_IDLE;
    } 
    //mskprintf("n_ops = %d\n", n_ops);
    return true;
}

// 解析模型数据 
bool CTaskManager::UnpackDevModelData(neb::CJsonObject &obj)
{
    neb::CJsonObject obj_body;
    if (!obj.Get("body", obj_body))
    {
        IEC_LOG_RECORD(eErrType, "UnpackDevModelData body = NULL.");
        return false;
    }
    if (!obj_body.IsArray())
    {
        IEC_LOG_RECORD(eErrType, "UnpackDevModelData body is not Array.");
        return false;
    }        

    int asize = obj_body.GetArraySize();
    for (int i = 0; i < asize; i++)
    {
        neb::CJsonObject obj_data;
        if (!obj_body.Get(i, obj_data))
        {
            mskprintf("UnpackDevModelData body data get error i = %d.\n", i);
            return false;
        }
        std::string modelname;
        obj_data.Get("model", modelname);
        
        

        mskprintf("modelname = %s, %s\n", modelname.c_str(), CParamManager::CreateInstance().m_devModel.c_str());

        if (modelname != CParamManager::CreateInstance().m_devModel)
        {
            continue;
        }
        
        neb::CJsonObject obj_model_data;
        obj_data.Get("body", obj_model_data);
        int bsize = obj_model_data.GetArraySize();
        for (int j = 0; j < bsize; j++)
        {
            neb::CJsonObject obj_value;
            if (!obj_model_data.Get(j, obj_value))
            {
                mskprintf("UnpackDevModelData body data get error j = %d.\n", j);
                return false;
            }
            
            std::string modelvaluename;
            obj_value.Get("name", modelvaluename);
            CParamManager::CreateInstance().m_ADC_value[modelvaluename] = 0.0f;
        }
        
    }

    // 打印变量信息
    std::map<std::string, float>::iterator iter;
    for (iter = CParamManager::CreateInstance().m_ADC_value.begin(); iter != CParamManager::CreateInstance().m_ADC_value.end(); iter++)
    {
        mskprintf("name =  %s , value = %f\n", iter->first.c_str(), iter->second);
    }

    if (CParamManager::CreateInstance().m_ADC_value.size() > 0)
    {
        return true;
    }
    else
    {
        return false;
    }
}

// 解析定值数据
bool CTaskManager::UnpackDevParameterData(neb::CJsonObject &obj)
{
    neb::CJsonObject obj_body;
    if (!obj.Get("body", obj_body))
    {
        IEC_LOG_RECORD(eErrType, "UnpackDevParameterData body = NULL.");
        return false;
    }
    if (!obj_body.IsArray())
    {
        IEC_LOG_RECORD(eErrType, "UnpackDevParameterData body is not Array.");
        return false;
    }


    int asize = obj_body.GetArraySize();
    for (int i = 0; i < asize; i++)
    {
        neb::CJsonObject obj_data;
        if (!obj_body.Get(i, obj_data))
        {
            mskprintf("UnpackDevParameterData body data get error i = %d.\n", i);
            return false;
        }
        std::string devName;
        obj_data.Get("dev", devName);

        if (devName != CParamManager::CreateInstance().m_devDev)
        {
            continue;
        }
        mskprintf("modelname = %s, %s\n", devName.c_str(), CParamManager::CreateInstance().m_devDev.c_str());

        neb::CJsonObject obj_model_data;
        obj_data.Get("body", obj_model_data);
        int bsize = obj_model_data.GetArraySize();
        for (int j = 0; j < bsize; j++)
        {
            
            neb::CJsonObject obj_value;
            if (!obj_model_data.Get(j, obj_value))
            {
                mskprintf("UnpackDevParameterData body data get error j = %d.\n", j);
                IEC_LOG_RECORD(eErrType, "UnpackDevParameterData body data get error j = %d.\n", j);
                return false;
            }

            std::string valuename;
            obj_value.Get("name", valuename);   

            std::string value;
            obj_value.Get("val", value);
            float _value = atof(value.c_str());
   
            mskprintf("valuename = %s, value =%s\n", valuename.c_str(), value.c_str());
			IEC_LOG_RECORD(eRunType, "set para valuename = %s, value =%s", valuename.c_str(), value.c_str());
            if (!CParamManager::CreateInstance().SetConstParam(valuename, _value))
            {
                IEC_LOG_RECORD(eErrType, "UnpackDevParameterData body is not Array.");
            }
        }
    }
    //n_ops = OPS_REG_OK;
    return 0;
}

// 解析初始化数据
bool CTaskManager::UnpackRealData(neb::CJsonObject &obj)
{
    neb::CJsonObject obj_body;
    if (!obj.Get("body", obj_body))
    {
        IEC_LOG_RECORD(eErrType, "UnpackRealData body = NULL.");
        return false;
    }
    if (!obj_body.IsArray())
    {
        IEC_LOG_RECORD(eErrType, "UnpackRealData body is not Array.");
        return false;
    }

    int asize = obj_body.GetArraySize();
    for (int i = 0; i < asize; i++)
    {
        neb::CJsonObject obj_data;
        if (!obj_body.Get(i, obj_data))
        {
            mskprintf("UnpackRealData body data get error i = %d.\n", i);
            return false;
        }
        std::string devname;
        obj_data.Get("dev", devname);

        mskprintf("devname = %s, %s\n", devname.c_str(), CParamManager::CreateInstance().m_devDev.c_str());

        if (devname != CParamManager::CreateInstance().m_devDev)
        {
            continue;
        }
        
        neb::CJsonObject obj_model_data;
        obj_data.Get("body", obj_model_data);
        int bsize = obj_model_data.GetArraySize();
        for (int j = 0; j < bsize; j++)
        {
            neb::CJsonObject obj_value;
            if (!obj_model_data.Get(j, obj_value))
            {
                mskprintf("UnpackRealData body data get error j = %d.\n", j);
                return false;
            }

            std::string name;
            obj_value.Get("name", name);

            std::string value;
            obj_value.Get("val", value);
            float _value = atof(value.c_str());
            mskprintf("name = %s, value =%s\n", name.c_str(), value.c_str());

            if (name.compare("PhV_phsA")  == 0|| name.compare("PhV_phsB")  == 0|| name.compare("PhV_phsC") == 0|| name.compare("SeqV_c0")  == 0
                || name.compare("A_phsA")  == 0|| name.compare("A_phsB") == 0|| name.compare("A_phsC") == 0 || name.compare("A_phsN") == 0  
                || name.compare("SeqA_c0")  == 0|| name.compare("ResA")  == 0 || name.compare("Hz")  == 0
                || name.compare("PhPF_phsA")  == 0 || name.compare("PhPF_phsB") == 0 || name.compare("PhPF_phsC")  == 0|| name.compare("TotPF") == 0
                || name.compare("PhVA_phsA") == 0 || name.compare("PhVA_phsB") == 0 || name.compare("PhVA_phsC")  == 0 || name.compare("TotVA") == 0
                || name.compare("PhVAng_phsA") == 0 || name.compare("PhVAng_phsB") == 0 || name.compare("PhVAng_phsC")  == 0 ||name.compare("SeqA_c1")  == 0||name.compare("SeqA_c2")  == 0||name.compare("SeqV_c1")  == 0||name.compare("SeqV_c2")  == 0)
            {
                CParamManager::CreateInstance().SetAdcData(name, _value);
            }
        }
    }

    //CParamManager::CreateInstance().Unpack_pdAnalyzerData();//计算
   /*  // 打印变量信息
    std::map<std::string, float>::iterator iter;
    for (iter = CParamManager::CreateInstance().m_ADC_value.begin(); iter != CParamManager::CreateInstance().m_ADC_value.end(); iter++)
    {
        mskprintf("name =  %s , value = %f\n", iter->first.c_str(), iter->second);
    } */
    return true;

    /*
    {
        "token" : "526e95e4-397c-3f1d-d929-8009e2df7b80",
        "timestamp" : "2025-08-16T14:13:25.668+0800",
        "body" : [ {
            "dev" : "TTU_f833b65f53e20695",
            "body" : [ {
            "name" : "PhV_phsA",
            "val" : "226.098480",
            "quality" : "0",
            "timestamp" : "2025-08-26T14:13:24.523+0800",
            "secret" : "0"
            }
    */
}

bool CTaskManager::UnPackRealData_MCCB(neb::CJsonObject &obj)
{
    /* 
    {
        "token" : "dbd8d64b-dc38-1c16-824f-1f155c520a03",
        "timestamp" : "2025-08-16T14:07:21.475+0800",
        "dev" : "MCCB_f25c11f329b11d0a",
        "TransType" : "dataGet",
        "code" : "0",
        "body" : {
            "PhV_phsA" : "220.1",
            "PhV_phsB" : "220.2",
            "PhV_phsC" : "220.3",
            "A_phsA" : "5.1",
            "A_phsB" : "5.2",
            "A_phsC" : "5.3",
            "SwPos" : "5.4"
        }
    }
    */

    std::string dev;
    obj.Get("dev", dev);

    neb::CJsonObject bodyObj;
    if (!obj.Get("body", bodyObj))
    {
        IEC_LOG_RECORD(eErrType, "UnPackRealData_MCCB: body = NULL.");
        return false;
    }
    if (bodyObj.IsArray())
    {
        IEC_LOG_RECORD(eErrType, "UnPackRealData_MCCB: body is Array, expect Object.");
        return false;
    }

    std::map<std::string, float> &devMap = CParamManager::CreateInstance().m_dev_value[dev];

    auto set_val = [&](const char* name)
    {
        std::string sval;
        if (bodyObj.Get(name, sval))
        {
            float fval = static_cast<float>(atof(sval.c_str()));
            devMap[name] = fval;
            mskprintf("MCCB %s: %s = %f\n", dev.c_str(), name, fval);
        }
    };

    set_val("PhV_phsA");
    set_val("PhV_phsB");
    set_val("PhV_phsC");
    set_val("A_phsA");
    set_val("A_phsB");
    set_val("A_phsC");
    set_val("SwPos");

    // mskprintf("UnPackRealData_MCCB dev=%s parsed. PhV_A=%.3f PhV_B=%.3f PhV_C=%.3f A_A=%.3f A_B=%.3f A_C=%.3f SwPos=%.3f\n",
    //           dev.c_str(),
    //           devMap["PhV_phsA"], devMap["PhV_phsB"], devMap["PhV_phsC"],
    //           devMap["A_phsA"], devMap["A_phsB"], devMap["A_phsC"],
    //           devMap["SwPos"]);

    return true;
}

bool CTaskManager::UnPackRealData_Meter(neb::CJsonObject &obj)
{
    /*
    {
        "token" : 123,
        "timestamp" : "2019-03-01T09:30:08.230+0800",
        "body" : [ {
            "dev" : "MultiMeter_guid",
            "body" : [ {
            "name" : "PhV_phsA",
            "val" : "220.331",
            "quality" : "1",
            "timestamp" : "2019-11-22T14:00:08.230+0800"
            }, {
            "name" : "PhV_phsB",
            "val" : "220.317",
            "quality" : "1",
            "timestamp" : "2019-11-22T14:00:08.230+0800"
            } ]
        } ]
        }
    */
    neb::CJsonObject obj_body;
    if (!obj.Get("body", obj_body))
    {
        IEC_LOG_RECORD(eErrType, "UnpackRealData body = NULL.");
        return false;
    }
    if (!obj_body.IsArray())
    {
        IEC_LOG_RECORD(eErrType, "UnpackRealData body is not Array.");
        return false;
    }

    int asize = obj_body.GetArraySize();
    for (int i = 0; i < asize; i++)
    {
        neb::CJsonObject obj_data;
        if (!obj_body.Get(i, obj_data))
        {
            mskprintf("UnpackRealData body data get error i = %d.\n", i);
            return false;
        }
        std::string devname;
        obj_data.Get("dev", devname);
        
        neb::CJsonObject obj_model_data;
        obj_data.Get("body", obj_model_data);
        int bsize = obj_model_data.GetArraySize();
        for (int j = 0; j < bsize; j++)
        {
            neb::CJsonObject obj_value;
            if (!obj_model_data.Get(j, obj_value))
            {
                mskprintf("UnpackRealData body data get error j = %d.\n", j);
                return false;
            }

            std::string name;
            obj_value.Get("name", name);

            std::string value;
            obj_value.Get("val", value);
            float _value = atof(value.c_str());
            mskprintf("METER_name = %s, value =%s\n", name.c_str(), value.c_str());
            if (name.compare("PhV_phsA")  == 0|| name.compare("PhV_phsB")  == 0|| name.compare("PhV_phsC") == 0|| name.compare("SeqV_c0")  == 0
                || name.compare("A_phsA")  == 0|| name.compare("A_phsB") == 0|| name.compare("A_phsC") == 0 || name.compare("A_phsN") == 0  
                || name.compare("SeqA_c0")  == 0|| name.compare("ResA")  == 0 || name.compare("Hz")  == 0
                || name.compare("PhPF_phsA")  == 0 || name.compare("PhPF_phsB") == 0 || name.compare("PhPF_phsC")  == 0|| name.compare("TotPF") == 0
                || name.compare("PhVA_phsA") == 0 || name.compare("PhVA_phsB") == 0 || name.compare("PhVA_phsC")  == 0 || name.compare("TotVA") == 0
                || name.compare("PhVAng_phsA") == 0 || name.compare("PhVAng_phsB") == 0 || name.compare("PhVAng_phsC")  == 0 ||name.compare("SeqA_c1")  == 0||name.compare("SeqA_c2")  == 0||name.compare("SeqV_c1")  == 0||name.compare("SeqV_c2")  == 0)
            {
                CIIAutoMutex mutex(&m_cs);
                // CParamManager::CreateInstance().m_dev_model[devname][name] = value;

                float f = static_cast<float>(atof(value.c_str()));
                CParamManager::CreateInstance().m_dev_value[devname][name] = f;
            }
        }
    }   
    return true;
}

//数据变化上报
bool CTaskManager::UnpackNotificationData(neb::CJsonObject &obj)
{    
    neb::CJsonObject obj_body;
    if (!obj.Get("body", obj_body))
    {
        IEC_LOG_RECORD(eErrType, "UnpackNotificationData body = NULL.");
        return false;
    }
    if (!obj_body.IsArray())
    {
        IEC_LOG_RECORD(eErrType, "UnpackNotificationData body is not Array.");
        return false;
    }

    bool ADC_PQ_flag = false;
	bool ADC_OC_flag = false;
    int asize = obj_body.GetArraySize();
    for (int i = 0; i < asize; i++)
    {
        neb::CJsonObject obj_data;
        if (!obj_body.Get(i, obj_data))
        {
            mskprintf("UnpackNotificationData body data get error i = %d.\n", i);
            return false;
        }
        std::string name;
        std::string value;
        obj_data.Get("name", name);
        obj_data.Get("val", value);
        //mskprintf("name = %s, value =%s\n", name.c_str(), value.c_str());
        //交采更新
         if (name.compare("PhV_phsA")  == 0|| name.compare("PhV_phsB")  == 0|| name.compare("PhV_phsC") == 0|| name.compare("SeqV_c0")  == 0
                || name.compare("A_phsA")  == 0|| name.compare("A_phsB") == 0|| name.compare("A_phsC") == 0 || name.compare("A_phsN") == 0  || name.compare("SeqA_c0")  == 0 || name.compare("ResA")  == 0
                || name.compare("Hz")  == 0
                || name.compare("PhPF_phsA")  == 0 || name.compare("PhPF_phsB") == 0 || name.compare("PhPF_phsC")  == 0|| name.compare("TotPF") == 0
                || name.compare("PhVA_phsA") == 0 || name.compare("PhVA_phsB") == 0 || name.compare("PhVA_phsC")  == 0 || name.compare("TotVA") == 0
                || name.compare("PhVAng_phsA") == 0 || name.compare("PhVAng_phsB") == 0 || name.compare("PhVAng_phsC")  == 0 ||name.compare("SeqA_c1")  == 0||name.compare("SeqA_c2")  == 0||name.compare("SeqV_c1")  == 0||name.compare("SeqV_c2")  == 0)
        {
            float _value = atof(value.c_str());
            if (!CParamManager::CreateInstance().SetAdcData(name, _value))
            {
                IEC_LOG_RECORD(eErrType, "UnpackNotificationData SetAdcData is failed.");
            }
            
        }

        if(name.compare("PhV_phsA") == 0|| name.compare("PhV_phsB") == 0||name.compare("PhV_phsC") == 0
            || name.compare("A_phsA") == 0||name.compare("A_phsB") == 0
            ||name.compare("A_phsC") == 0||name.compare("A_phsN") == 0||name.compare("A_phsN") == 0)
        {
            ADC_PQ_flag =  true;
        }
        
		if (name.compare("PhVA_phsA")==0 || name.compare("PhVA_phsB")==0 || name.compare("PhVA_phsC")==0 || name.compare("TotVA")==0)
        {
            ADC_OC_flag = true;
        }
    }

    if(ADC_PQ_flag)
    {
        CParamManager::CreateInstance().Unpack_pdAnalyzerImb();//计算 
    }
    //CParamManager::CreateInstance().Unpack_pdAnalyzerData();//计算

    // 打印变量信息
    /* std::map<std::string, float>::iterator iter;
    for (iter = CParamManager::CreateInstance().m_ADC_value.begin(); iter != CParamManager::CreateInstance().m_ADC_value.end(); iter++)
    {
        mskprintf("name =  %s , value = %f\n", iter->first.c_str(), iter->second);
    } */
    return true;
}

bool CTaskManager::UnpackRealData(CDataObj *dobj, neb::CJsonObject obj)
{
    bool b(false);
    if (!dobj->m_startFlag)
    {
        dobj->m_startFlag = true;
        dobj->m_startTime = ii_get_current_mtime();
    }

    if (dobj == NULL)
    {
        IEC_LOG_RECORD(eErrType, "UnpackRealData dobj = NULL.");
        return false;
    }
    bool bRet = true;
    neb::CJsonObject obj_body;
    if (!obj.Get("body", obj_body))
    {
        IEC_LOG_RECORD(eErrType, "UnpackRealData body = NULL.");
        return false;
    }
    if (!obj_body.IsArray())
    {
        IEC_LOG_RECORD(eErrType, "UnpackRealData body is not Array.");
        return false;
    }
    int asize = obj_body.GetArraySize();
    // 清空当前的变化name
    dobj->m_SpontObj.m_Spontnames.clear();

    for (int i = 0; i < asize; i++)
    {
        neb::CJsonObject obj_data;
        if (!obj_body.Get(i, obj_data))
        {
            mskprintf("UnpackRealData body data get error i = %d.\n", i);
            return false;
        }
        std::string name;
        std::string val;
        std::string stype;
        bRet &= obj_data.Get("name", name);
        bRet &= obj_data.Get("val", val);

        if (name.size() > 0)
        {
            dobj->m_RealDatas[name] = val;
            obj_data.Get("type", stype);
            if (stype.compare("cycle") == 0)
            { // 周期数据赋值
                continue;
            }
            mskprintf("yx change name = (%s),val = (%s).\n", name.c_str(), val.c_str());
            // 如果name在遥信中则发送变化
            std::string serveiceName;
            if (IsFindDiscrete(dobj, name, serveiceName))
            {
                dobj->m_SpontObj.m_Spontnames.push_back(name);
                dobj->m_SpontObj.m_serviceName = serveiceName;
                b = true;
            }
        }
    }
    if (b)
    {
        MqttPackSpontData(dobj);
    }
    return bRet;
}

std::string CTaskManager::UnpackToken(neb::CJsonObject obj)
{
     std::string tokenStr;
    if (obj.Get("token", tokenStr))
    {
        return tokenStr;
    }

    int token;
    if (obj.Get("token", token))
    {
        return std::to_string(token);
    }
    return NULL;
}

void CTaskManager::MqttPackEsn()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;

    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    const char *pub_topic = CFG_APP_NAME "/get/request/esdk/deviceInfo";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
        // CMqttClientInterManager::CreateInstance().agent_MqttMsgPub((char*)(mssm.c_str()), (char *)pub_topic, 0);
    }
}

void CTaskManager::MqttGetDevEsn()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);   

    const char *pub_topic = CFG_APP_NAME "/smiOS/JSON/request/devInfo";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

void CTaskManager::MqttOS_REG()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;

    oJson.Add("token", 1);
    oJson.Add("timestamp", jsheader.timestamp);

    neb::CJsonObject body;
    body.Add("name", CParamManager::CreateInstance().m_appName);
    body.Add("version", CParamManager::CreateInstance().m_appVersion);
    body.Add("releaseDate", CParamManager::CreateInstance().m_appRealseDate);
    oJson.Add("body",body);

    const char *pub_topic = CFG_APP_NAME "/smiOS/JSON/request/register";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

void CTaskManager::MqttPackGetGuid()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;

    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.AddEmptySubArray("body");

    //

    neb::CJsonObject body;
    body.Add("model", CParamManager::CreateInstance().m_devModel);
    body.Add("port", CParamManager::CreateInstance().m_devPort);
    body.Add("addr", CParamManager::CreateInstance().m_devAddr);
    body.Add("desc", CParamManager::CreateInstance().m_devDesc);
    oJson["body"].Add(body);

    const char *pub_topic = CFG_APP_NAME "/" CFG_DATA_CENTER_NAME "/JSON/get/request/guid";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}
void CTaskManager::MqttPackGetModel()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if(ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }
    neb::CJsonObject oJson;
    
    oJson.Add("token",jsheader.token);
    oJson.Add("timestamp",jsheader.timestamp);
    neb::CJsonObject body;

    //读取配置文件中配置的所有数据模型
    body.Add("model", CParamManager::CreateInstance().m_devModel);
    oJson.Add("body",body);

    const char *pub_topic = CFG_APP_NAME "/" CFG_DATA_CENTER_NAME "/JSON/get/request/model";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
    
}

void CTaskManager::MqttPackGetModelInfo()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }
    
    neb::CJsonObject oJson;

    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    neb::CJsonObject body;
    body.Add("IFT");
    oJson.Add("body", body);

    const char *pub_topic = CFG_APP_NAME "/" CFG_DATA_CENTER_NAME "/JSON/get/request/model";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

// 获取一次定值  MskpqAnalyzer/dataCenter/JSON/get/request/parameter
void CTaskManager::MqttPackGetParameter()
{

    CTransientManager::CreateInstance().Init();
    const uint8_t data_array[] = {
        0x68, 0x36, 0x14, 0x1c, 0x00, 0x00, 0x2d, 0x18, 0xa8, 0x0d, 0x00, 0x19, 0x06, 0x18, 0x0b, 0x20,
        0x04, 0x18, 0xa8, 0x0d, 0x00, 0x52, 0x3b, 0xfb, 0xff, 0x16, 0x95, 0x02, 0x00, 0xdf, 0x3c, 0x02,
        0x00, 0x55, 0xf3, 0xff, 0xff, 0xbe, 0x1b, 0x00, 0x00, 0xfc, 0xf0, 0xff, 0xff, 0xb1, 0x0c, 0x00,
        0x00, 0x39, 0x3a, 0xfb, 0xff, 0x0f, 0x7b, 0x02, 0x00, 0xc2, 0x57, 0x02, 0x00, 0xbb, 0xf2, 0xff,
        0xff, 0xc4, 0x1b, 0x00, 0x00, 0x91, 0xf1, 0xff, 0xff, 0x47, 0x0d, 0x00, 0x00, 0x91, 0x3a, 0xfb,
        0xff, 0x0a, 0x61, 0x02, 0x00, 0xc0, 0x71, 0x02, 0x00, 0x22, 0xf2, 0xff, 0xff, 0xc9, 0x1b, 0x00,
        0x00, 0x26, 0xf2, 0xff, 0xff, 0xe3, 0x0d, 0x00, 0x00, 0xc4, 0x3a, 0xfb, 0xff, 0x84, 0x46, 0x02,
        0x00, 0xd6, 0x8b, 0x02, 0x00, 0x8a, 0xf1, 0xff, 0xff, 0xc4, 0x1b, 0x00, 0x00, 0xc2, 0xf2, 0xff,
        0xff, 0x7c, 0x0e, 0x00, 0x00, 0x35, 0x3c, 0xfb, 0xff, 0x9e, 0x2c, 0x02, 0x00, 0x64, 0xa5, 0x02,
        0x00, 0xf7, 0xf0, 0xff, 0xff, 0xbf, 0x1b, 0x00, 0x00, 0x5d, 0xf3, 0xff, 0xff, 0x11, 0x0f, 0x00,
        0x00, 0x21, 0x3e, 0xfb, 0xff, 0x9c, 0x10, 0x02, 0x00, 0xcc, 0xbe, 0x02, 0x00, 0x61, 0xf0, 0xff,
        0xff, 0xb3, 0x1b, 0x00, 0x00, 0xfa, 0xf3, 0xff, 0xff, 0xa7, 0x0f, 0x00, 0x00, 0xdb, 0x40, 0xfb,
        0xff, 0xf3, 0xf4, 0x01, 0x00, 0x11, 0xd7, 0x02, 0x00, 0xd3, 0xef, 0xff, 0xff, 0xa5, 0x1b, 0x00,
        0x00, 0x97, 0xf4, 0xff, 0xff, 0x31, 0x10, 0x00, 0x00, 0x66, 0x44, 0xfb, 0xff, 0xdb, 0xd9, 0x01,
        0x00, 0x0e, 0xef, 0x02, 0x00, 0x47, 0xef, 0xff, 0xff, 0x94, 0x1b, 0x00, 0x00, 0x3c, 0xf5, 0xff,
        0xff, 0xc0, 0x10, 0x00, 0x00, 0x22, 0x49, 0xfb, 0xff, 0x28, 0xbd, 0x01, 0x00, 0xaf, 0x06, 0x03,
        0x00, 0xbc, 0xee, 0xff, 0xff, 0x7a, 0x1b, 0x00, 0x00, 0xdc, 0xf5, 0xff, 0xff, 0x49, 0x11, 0x00,
        0x00, 0x03, 0x4e, 0xfb, 0xff, 0xdc, 0xa0, 0x01, 0x00, 0x0b, 0x1e, 0x03, 0x00, 0x34, 0xee, 0xff,
        0xff, 0x5c, 0x1b, 0x00, 0x00, 0x7f, 0xf6, 0xff, 0xff, 0xd6, 0x11, 0x00, 0x00, 0xc9, 0x53, 0xfb,
        0xff, 0x1c, 0x84, 0x01, 0x00, 0x1a, 0x35, 0x03, 0x00, 0xaf, 0xed, 0xff, 0xff, 0x3a, 0x1b, 0x00,
        0x00, 0x28, 0xf7, 0xff, 0xff, 0x5a, 0x12, 0x00, 0x00, 0x50, 0x5a, 0xfb, 0xff, 0x36, 0x67, 0x01,
        0x00, 0xff, 0x4a, 0x03, 0x00, 0x2b, 0xed, 0xff, 0xff, 0x17, 0x1b, 0x00, 0x00, 0xcd, 0xf7, 0xff,
        0xff, 0xd9, 0x12, 0x00, 0x00, 0x42, 0x61, 0xfb, 0xff, 0xe5, 0x49, 0x01, 0x00, 0x01, 0x61, 0x03,
        0x00, 0xac, 0xec, 0xff, 0xff, 0xef, 0x1a, 0x00, 0x00, 0x76, 0xf8, 0xff, 0xff, 0x59, 0x13, 0x00,
        0x00, 0xb0, 0x69, 0xfb, 0xff, 0xaf, 0x2c, 0x01, 0x00, 0x0d, 0x76, 0x03, 0x00, 0x30, 0xec, 0xff,
        0xff, 0xc2, 0x1a, 0x00, 0x00, 0x20, 0xf9, 0xff, 0xff, 0xda, 0x13, 0x00, 0x00, 0xdd, 0x71, 0xfb,
        0xff, 0xb1, 0x0f, 0x01, 0x00, 0x6e, 0x8a, 0x03, 0x00, 0xb9, 0xeb, 0xff, 0xff, 0x91, 0x1a, 0x00,
        0x00, 0xca, 0xf9, 0xff, 0xff, 0x5b, 0x14, 0x00, 0x00, 0x6e, 0x7b, 0xfb, 0xff, 0xf2, 0xf1, 0x00,
        0x00, 0x38, 0x9f, 0x03, 0x00, 0x41, 0xeb, 0xff, 0xff, 0x5c, 0x1a, 0x00, 0x00, 0x74, 0xfa, 0xff,
        0xff, 0xcc, 0x14, 0x00, 0x00, 0xa4, 0x85, 0xfb, 0xff, 0x31, 0xd4, 0x00, 0x00, 0x63, 0xb2, 0x03,
        0x00, 0xd0, 0xea, 0xff, 0xff, 0x22, 0x1a, 0x00, 0x00, 0x21, 0xfb, 0xff, 0xff, 0x3d, 0x15, 0x00,
        0x00, 0x6d, 0x90, 0xfb, 0xff, 0xb9, 0xb5, 0x00, 0x00, 0xdf, 0xc5, 0x03, 0x00, 0x5f, 0xea, 0xff,
        0xff, 0xe5, 0x19, 0x00, 0x00, 0xce, 0xfb, 0xff, 0xff, 0xac, 0x15, 0x00, 0x00, 0xdf, 0x9b, 0xfb,
        0xff, 0xd1, 0x97, 0x00, 0x00, 0xea, 0xd7, 0x03, 0x00, 0xf5, 0xe9, 0xff, 0xff, 0xa4, 0x19, 0x00,
        0x00, 0x7b, 0xfc, 0xff, 0xff, 0x18, 0x16, 0x00, 0x00, 0xf3, 0xa7, 0xfb, 0xff, 0xe4, 0x79, 0x00,
        0x00, 0x46, 0xea, 0x03, 0x00, 0x8b, 0xe9, 0xff, 0xff, 0x5d, 0x19, 0x00, 0x00, 0x2c, 0xfd, 0xff,
        0xff, 0x82, 0x16, 0x00, 0x00, 0x25, 0xb5, 0xfb, 0xff, 0x28, 0x5c, 0x00, 0x00, 0xf7, 0xfa, 0x03,
        0x00, 0x22, 0xe9, 0xff, 0xff, 0x14, 0x19, 0x00, 0x00, 0xd8, 0xfd, 0xff, 0xff, 0xe1, 0x16, 0x00,
        0x00, 0x72, 0xc2, 0xfb, 0xff, 0xbc, 0x3d, 0x00, 0x00, 0x9c, 0x0b, 0x04, 0x00, 0xc3, 0xe8, 0xff,
        0xff, 0xc6, 0x18, 0x00, 0x00, 0x89, 0xfe, 0xff, 0xff, 0x49, 0x17, 0x00, 0x00, 0x3c, 0xd1, 0xfb,
        0xff, 0x27, 0x1f, 0x00, 0x00, 0x68, 0x1c, 0x04, 0x00, 0x62, 0xe8, 0xff, 0xff, 0x76, 0x18, 0x00,
        0x00, 0x35, 0xff, 0xff, 0xff, 0xa4, 0x17, 0x00, 0x00, 0x7a, 0xdf, 0xfb, 0xff, 0x0e, 0x01, 0x00,
        0x00, 0x08, 0x2b, 0x04, 0x00, 0x09, 0xe8, 0xff, 0xff, 0x23, 0x18, 0x00, 0x00, 0xe4, 0xff, 0xff,
        0xff, 0xff, 0x17, 0x00, 0x00, 0x46, 0xef, 0xfb, 0xff, 0x36, 0xe2, 0xff, 0xff, 0x38, 0x3a, 0x04,
        0x00, 0xb4, 0xe7, 0xff, 0xff, 0xc7, 0x17, 0x00, 0x00, 0x98, 0x00, 0x00, 0x00, 0x57, 0x18, 0x00,
        0x00, 0x12, 0xff, 0xfb, 0xff, 0x7c, 0xc4, 0xff, 0xff, 0xff, 0x47, 0x04, 0x00, 0x62, 0xe7, 0xff,
        0xff, 0x6b, 0x17, 0x00, 0x00, 0x46, 0x01, 0x00, 0x00, 0xa9, 0x18, 0x00, 0x00, 0xb7, 0x0f, 0xfc,
        0xff, 0x29, 0xa6, 0xff, 0xff, 0x8e, 0x55, 0x04, 0x00, 0x10, 0xe7, 0xff, 0xff, 0x09, 0x17, 0x00,
        0x00, 0xf6, 0x01, 0x00, 0x00, 0xfc, 0x18, 0x00, 0x00, 0x0b, 0x21, 0xfc, 0xff, 0xd0, 0x87, 0xff,
        0xff, 0xc2, 0x61, 0x04, 0x00, 0xc5, 0xe6, 0xff, 0xff, 0xa5, 0x16, 0x00, 0x00, 0xa4, 0x02, 0x00,
        0x00, 0x47, 0x19, 0x00, 0x00, 0x14, 0x33, 0xfc, 0xff, 0x98, 0x6a, 0xff, 0xff, 0xf9, 0x6d, 0x04,
        0x00, 0x80, 0xe6, 0xff, 0xff, 0x40, 0x16, 0x00, 0x00, 0x51, 0x03, 0x00, 0x00, 0x91, 0x19, 0x00,
        0x00, 0xcd, 0x45, 0xfc, 0xff, 0x66, 0x8d, 0x0c, 0x00, 0x16
    };
    // 计算数组长度
    const size_t data_length = sizeof(data_array) / sizeof(data_array[0]);
    CTransientManager::CreateInstance().ReceiveWaveformPacket(data_array, data_length);

    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.AddEmptySubArray("body");

    neb::CJsonObject body;
    body.Add("dev", CParamManager::CreateInstance().m_devDev);
    body.Add("totalcall", "1");
    body.AddEmptySubArray("body");
    oJson["body"].Add(body);

    if (!CParamManager::CreateInstance().m_dev_model.empty())
    {
        std::map<std::string, std::string>::iterator it = CParamManager::CreateInstance().m_dev_model.begin();
        for (; it != CParamManager::CreateInstance().m_dev_model.end(); ++it)
        {
            const std::string &devVal = it->first;
            if (devVal.empty()) continue;
            neb::CJsonObject body;
            body.Add("dev", devVal);
            body.Add("totalcall", "1");
            body.AddEmptySubArray("body");
            oJson["body"].Add(body); 
        } 
    }

    const char *pub_topic = CFG_APP_NAME "/" CFG_DATA_CENTER_NAME "/JSON/get/request/parameter";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    } 
}
void CTaskManager::MqttPackGetParameter_DTA()  //端设备定值参数
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("dev",CParamManager::CreateInstance().m_devDev);
    oJson.Add("TransType","dataGet");
    oJson.Add("TimeOut",10);
    oJson.Add("ByteTimeOut",10);
    oJson.Add("Prio",3);
    oJson.AddEmptySubArray("body");

    neb::CJsonObject bodybogy;

    oJson.Add("body",bodybogy);
    oJson["body"].Add(bodybogy);

    const char *pub_topic;


    pub_topic = CFG_APP_NAME "/" MCCB_NAME "/JSON/action/request/transTxFrm";
    
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

//获取实时数据 MskpqAnalyzer/dataCenter/JSON/get/request/realtime
void CTaskManager::MqttPackGetRealTimeData()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }


    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.AddEmptySubArray("body");

    neb::CJsonObject body;
    body.Add("dev", CParamManager::CreateInstance().m_devDev);
    body.Add("totalcall", "0");

    neb::CJsonObject bodybogy;
    bodybogy.Add("PhV_phsA");
    bodybogy.Add("PhV_phsB");
    bodybogy.Add("PhV_phsC");
    bodybogy.Add("SeqV_c0");
    bodybogy.Add("SeqV_c1");
    bodybogy.Add("SeqV_c2");
    bodybogy.Add("A_phsA");
    bodybogy.Add("A_phsB");
    bodybogy.Add("A_phsC");
    bodybogy.Add("A_phsN");
    bodybogy.Add("SeqA_c0");
    bodybogy.Add("SeqA_c1");
    bodybogy.Add("SeqA_c2");
    bodybogy.Add("ResA");
    bodybogy.Add("Hz");
    bodybogy.Add("PhPF_phsA");
    bodybogy.Add("PhPF_phsB");
    bodybogy.Add("PhPF_phsC");
    bodybogy.Add("TotPF");
    bodybogy.Add("PhVA_phsA");
    bodybogy.Add("PhVA_phsB");
    bodybogy.Add("PhVA_phsC");
    bodybogy.Add("TotVA");
    bodybogy.Add("PhVAng_phsA");
    bodybogy.Add("PhVAng_phsB");
    bodybogy.Add("PhVAng_phsC");
    bodybogy.Add("PhAAng_phsA");
    bodybogy.Add("PhAAng_phsB");
    bodybogy.Add("PhAAng_phsC");

    body.Add("body",bodybogy);
    oJson["body"].Add(body);

    const char *pub_topic = CFG_APP_NAME "/" CFG_DATA_CENTER_NAME "/JSON/get/request/realtime";

    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

void CTaskManager::MqttPackGetDeviceGuid()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    /*
        {
        "token": "123",
        "timestamp": "2019-03-01T09:30:08.230+0800",
        "body": ["MultiMeter","MCCB"]
        }
    */

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.AddEmptySubArray("body");

    // 添加设备类型到body数组
    std::deque<std::string> model = CParamManager::CreateInstance().m_models;
    for(auto item:model)
    {
        oJson["body"].Add(item);
    }
    // oJson["body"].Add("MultiMeter");
    // oJson["body"].Add("MCCB");

    const char *pub_topic = CFG_APP_NAME "/" CFG_DATA_CENTER_NAME "/JSON/get/request/devRegister";

    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

void CTaskManager::MqttPackGetRealTimeData_DTA()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject metrics;
    metrics.Add("PhV_phsA", "");
    metrics.Add("PhV_phsB", "");
    metrics.Add("PhV_phsC", "");
    metrics.Add("A_phsA", "");
    metrics.Add("A_phsB", "");
    metrics.Add("A_phsC", "");
    metrics.Add("SwPos", "");

    for (auto it = CParamManager::CreateInstance().m_dev_model.begin();
        it != CParamManager::CreateInstance().m_dev_model.end(); ++it)
    {
        const std::string &devguid  = it->first;   
        const std::string &devmodel = it->second; 
        if(devmodel != "MultiMeter")
        {
            neb::CJsonObject oJson;
            oJson.Add("token", jsheader.token);              
            oJson.Add("timestamp", jsheader.timestamp);     
            oJson.Add("dev", devguid);
            oJson.Add("TransType", "dataGet");
            oJson.Add("TimeOut", "10");
            oJson.Add("ByteTimeOut", "10");
            oJson.Add("Prio", "1");
            oJson.Add("body", metrics);                      

            const char *pub_topic;
            pub_topic = CFG_APP_NAME "/" MCCB_NAME "/JSON/action/request/transTxFrm";

            std::string mssm = oJson.ToString();
            if (mssm.size() > 0)
            {
                mqtt_data_info_s item = {0};
                memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
                memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
                item.msg_send_lenth = mssm.size();
                item.retained = 0;
                CMqttClientInterManager::CreateInstance().Push_SendItem(item);
            }  
        }
    }
}

void CTaskManager::MqttPackGetRealTimeData_METER()
{
    /*
        {
            "token": 123,
            "timestamp": "2019-03-01T09:30:08.230+0800",
            "expTime": "3",
            "body": [{ 
                    "dev": "MultiMeter_guid",
                    "additionalcheck":"88888888",
                    "totalcall": "1",
                    "body": []
                }
            ]
        }
    */
   mqtt_header_s jsheader = {0};
   int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
   if (ret != MQTT_OK)
   {
       MG_LOG_E("\nJson head fill failed.\n");
       return;
   }


   neb::CJsonObject oJson;
   oJson.Add("token", jsheader.token);
   oJson.Add("timestamp", jsheader.timestamp);
   oJson.Add("expTime","3");
   oJson.AddEmptySubArray("body");

   if (!CParamManager::CreateInstance().m_dev_model.empty())
   {
       std::map<std::string, std::string>::iterator it = CParamManager::CreateInstance().m_dev_model.begin();
       for (; it != CParamManager::CreateInstance().m_dev_model.end(); ++it)
       {
            const std::string &devmodel = it->second;
            const std::string &devguid = it->first;
           
            if (devmodel.empty()) continue;
            neb::CJsonObject body;
            body.Add("dev", devguid);
            body.Add("additionalcheck","88888888");
            body.Add("totalcall", "0");
            if(devmodel == "MultiMeter")
            {
                neb::CJsonObject bodybogy;
                bodybogy.Add("PhV_phsA");
                bodybogy.Add("PhV_phsB");
                bodybogy.Add("PhV_phsC");
                bodybogy.Add("SeqV_c0");
                bodybogy.Add("SeqV_c1");
                bodybogy.Add("SeqV_c2");
                bodybogy.Add("A_phsA");
                bodybogy.Add("A_phsB");
                bodybogy.Add("A_phsC");
                bodybogy.Add("A_phsN");
                bodybogy.Add("SeqA_c0");
                bodybogy.Add("SeqA_c1");
                bodybogy.Add("SeqA_c2");
                bodybogy.Add("ResA");
                bodybogy.Add("Hz");
                bodybogy.Add("PhPF_phsA");
                bodybogy.Add("PhPF_phsB");
                bodybogy.Add("PhPF_phsC");
                bodybogy.Add("TotPF");
                bodybogy.Add("PhVA_phsA");
                bodybogy.Add("PhVA_phsB");
                bodybogy.Add("PhVA_phsC");
                bodybogy.Add("TotVA");
                body.Add("body",bodybogy);
                oJson["body"].Add(body);
           }
       } 
   }

   const char *pub_topic = CFG_APP_NAME "/" CFG_DATA_CENTER_NAME "/JSON/get/request/meterRealtime";
   // const char *pub_topic;  
   // if(CParamManager::CreateInstance().m_devModel == "Meter")
   // {
   //     pub_topic = CFG_APP_NAME "/" CFG_DATA_CENTER_NAME "JSON/get/request/meterRealtime";
   // }
   std::string mssm = oJson.ToString();
   if (mssm.size() > 0)
   {
       mqtt_data_info_s item = {0};
       memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
       memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
       item.msg_send_lenth = mssm.size();
       item.retained = 0;
       CMqttClientInterManager::CreateInstance().Push_SendItem(item);
   }

}


void CTaskManager::MqttGetDevStatus()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", 123);
    oJson.Add("timestamp", jsheader.timestamp);
    
    
    const char *pub_topic = CFG_APP_NAME "/" CFG_OS_NAME "/JSON/request/devStatus";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

// 电能质量分析模型  /MskpqAnalyzer/dataCenter/JSON/set/request/model 设置数据模型 
void CTaskManager::MqttPackSetModelInfo_PQA()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }
    
    neb::CJsonObject oJson;

    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("model", CParamManager::CreateInstance().m_devModel);
    
    neb::CJsonObject body;
    for (int i = 0; i < CParamManager::CreateInstance().m_vmodel.size(); i++)
    {
        neb::CJsonObject bodyitem;
        bodyitem.Add("name", CParamManager::CreateInstance().m_vmodel[i].name);
        bodyitem.Add("type", CParamManager::CreateInstance().m_vmodel[i].type);
        bodyitem.Add("unit", CParamManager::CreateInstance().m_vmodel[i].unit);
        bodyitem.Add("deadzone", CParamManager::CreateInstance().m_vmodel[i].deadzone);
        bodyitem.Add("ratio", CParamManager::CreateInstance().m_vmodel[i].ratio);
        bodyitem.Add("isReport", CParamManager::CreateInstance().m_vmodel[i].isReport);
        bodyitem.Add("userdefine", CParamManager::CreateInstance().m_vmodel[i].userdefine);

        body.Add(bodyitem);
    }
    oJson.Add("body",body);

    const char *pub_topic = CFG_APP_NAME "/" CFG_DATA_CENTER_NAME "/JSON/set/request/model";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

void CTaskManager::MqttPackSetModelInfo_MCCB()
{

}

////可开放容量分析模型
void CTaskManager::MqttPackSetModelInfo_OCFc()
{

}

// 注册  MskpqAnalyzer/dataCenter/JSON/set/request/devRegister
void CTaskManager::MqttPackDevRegister()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;

    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.AddEmptySubArray("body");

    neb::CJsonObject body;
    body.Add("model", CParamManager::CreateInstance().m_devModel);
    body.Add("port", CParamManager::CreateInstance().m_devPort);
    body.Add("addr", CParamManager::CreateInstance().m_devAddr);
    body.Add("desc", CParamManager::CreateInstance().m_devDesc);
    body.Add("manuID", CParamManager::CreateInstance().m_devmanuID);
    body.Add("manuName", CParamManager::CreateInstance().m_devmanuName);
    body.Add("ProType", CParamManager::CreateInstance().m_devProType);
    body.Add("deviceType", CParamManager::CreateInstance().m_devdeviceType);
    body.Add("isReport", CParamManager::CreateInstance().m_devisReport);
    body.Add("nodeID", CParamManager::CreateInstance().m_devnodeID);
    body.Add("productID", CParamManager::CreateInstance().m_devproductID);
    
    oJson["body"].Add(body);

    const char *pub_topic = CFG_APP_NAME "/" CFG_DATA_CENTER_NAME "/JSON/set/request/devRegister";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
    
}



//上报不平衡度
bool CTaskManager::MqttPack_pdAnalyzerImb()
{
    mskprintf("\n上报一次不平衡度数据\n");
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return false;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("datatype", "0");    //遥测数据

    neb::CJsonObject body;   
    

    //电压不平衡度
    neb::CJsonObject sbodydata;
    sbodydata.Add("name", "ImbNgV");
    sbodydata.Add("id", "1");
    sbodydata.Add("val", std::to_string(CParamManager::CreateInstance().m_pdAnalyzer_value["ImbNgV"]*100));
    sbodydata.Add("unit", "");
    sbodydata.Add("quality", "0");
    sbodydata.Add("timestamp", jsheader.timestamp);
    body.Add(sbodydata);

    //电流不平衡度
    sbodydata.Replace("name", "ImbNgA");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_pdAnalyzer_value["ImbNgA"]*100));
    body.Add(sbodydata);

    //负荷不平衡度
    sbodydata.Replace("name", "ImbNgLoad");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_pdAnalyzer_value["ImbNgLoad"]*100));
    body.Add(sbodydata);

    oJson.Add("body",body);
    
    char pub_topic[256] = {0};
    snprintf(pub_topic, 128, CFG_APP_NAME"/Broadcast/JSON/report/notification/%s/%s",CParamManager::CreateInstance().m_devModel.c_str(), CParamManager::CreateInstance().m_devDev.c_str());



    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }

    mskprintf("\n上报遥测数据结束\n");

    return true;
}

bool CTaskManager::MqttPack_pdAnalyzerData()
{
    // 2、数据变化上报，104接收；2.4 极值推送，
    MqttPackNotificationData_4();

    // 2、数据变化上报，104接收；2.5  日月合格率推送
    MqttPackNotificationData_5();

    // 2、数据变化上报，104接收；2.6  日月越限推送
    MqttPackNotificationData_6();

    // 2、数据变化上报，104接收；2.7  日重载、日过载推送
    MqttPackNotificationData_7();
    
    return false;
    
}

bool CTaskManager::MqttPack_pdAnalyzerData(std::string guid ,std::string model)
{
    
}

//获取实时数据 
void CTaskManager::MqttPackSetRealTimeData()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("data_row", "single");    //实时数据 
    
    neb::CJsonObject body;   

    // 打印变量信息
    std::map<std::string, float>::iterator iter;
    for (iter = CParamManager::CreateInstance().m_pdAnalyzer_value.begin(); iter != CParamManager::CreateInstance().m_pdAnalyzer_value.end(); iter++)
    {
        // 跳过初始值
        float fvlaue = iter->second;
        if (fvlaue > 9998 || fvlaue < -9998)
        {
            continue;
        }
        
        std::string name = iter->first;

        //电压偏差  
        if (name.compare("ImbNgV") == 0 || name.compare("ImbNgA") == 0 || name.compare("ImbNgLoad") == 0 ||name.compare("LoadRate") == 0 || name.compare("ImbA0") == 0 
        || name.compare("ImbA2") == 0|| name.compare("ImbV0") == 0|| name.compare("ImbV2") == 0)
        {
            neb::CJsonObject sbodydata;
            sbodydata.Add("name", iter->first.c_str());
            sbodydata.Add("val", std::to_string(100*iter->second));
            sbodydata.Add("quality", "0");
            sbodydata.Add("secret", "1");
            sbodydata.Add("timestamp", jsheader.timestamp);
            body.Add(sbodydata);
        }
        else
        {
            neb::CJsonObject sbodydata;
            sbodydata.Add("name", iter->first.c_str());
            sbodydata.Add("val", std::to_string(iter->second));
            sbodydata.Add("quality", "0");
            sbodydata.Add("secret", "1");
            sbodydata.Add("timestamp", jsheader.timestamp);
            body.Add(sbodydata);
        }
    }
   
    oJson.Add("body",body);
    
    char pub_topic[256] = {0};
    snprintf(pub_topic, 128, CFG_APP_NAME "/dataCenter/JSON/set/request/%s/%s",CParamManager::CreateInstance().m_devModel.c_str(), CParamManager::CreateInstance().m_devDev.c_str());
    std::string mssm = oJson.ToString(); 
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);   
    }
}

void CTaskManager::MqttPackSetRealTimeData(std::string guid,std::string model)
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("data_row", "single");    //实时数据 
    
    neb::CJsonObject body;   

    // 打印变量信息
    std::map<std::string, float>::iterator iter;
    for (iter = CParamManager::CreateInstance().m_dev_Analyzer_value[guid].begin(); iter != CParamManager::CreateInstance().m_dev_Analyzer_value[guid].end(); iter++)
    {
        // 跳过初始值
        float fvlaue = iter->second;
        if (fvlaue > 9998 || fvlaue < -9998)
        {
            continue;
        }
        
        std::string name = iter->first;

        //电压偏差  
        if (name.compare("ImbNgV") == 0 || name.compare("ImbNgA") == 0 || name.compare("ImbNgLoad") == 0 ||name.compare("LoadRate") == 0 || name.compare("ImbA0") == 0 
        || name.compare("ImbA2") == 0|| name.compare("ImbV0") == 0|| name.compare("ImbV2") == 0)
        {
            neb::CJsonObject sbodydata;
            sbodydata.Add("name", iter->first.c_str());
            sbodydata.Add("val", std::to_string(100*iter->second));
            sbodydata.Add("quality", "0");
            sbodydata.Add("secret", "1");
            sbodydata.Add("timestamp", jsheader.timestamp);
            body.Add(sbodydata);
        }
        else
        {
            neb::CJsonObject sbodydata;
            sbodydata.Add("name", iter->first.c_str());
            sbodydata.Add("val", std::to_string(iter->second));
            sbodydata.Add("quality", "0");
            sbodydata.Add("secret", "1");
            sbodydata.Add("timestamp", jsheader.timestamp);
            body.Add(sbodydata);
        }
    }
   
    oJson.Add("body",body);
    
    char pub_topic[256] = {0};
    snprintf(pub_topic, 128, CFG_APP_NAME "/dataCenter/JSON/set/request/%s/%s",model.c_str(),guid.c_str());
    std::string mssm = oJson.ToString(); 
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);   
    }
}

//可开放容量分析实时数据推送
void CTaskManager::MqttPackSetRealTimeData_oc()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("data_row", "single");    //实时数据
    neb::CJsonObject body;   

    //组配变负载率
    neb::CJsonObject sbodydata;        
    sbodydata.Add("name", "LoadRate");
    sbodydata.Add("val", std::to_string(CParamManager::CreateInstance().m_LoadRate));//licong
    sbodydata.Add("quality", "0");
    sbodydata.Add("secret", "1");
    sbodydata.Add("timestamp", jsheader.timestamp);    
    body.Add(sbodydata);

    sbodydata.Replace("name","LoadRate_phsA");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_LoadRate_phsA));
    body.Add(sbodydata);

    sbodydata.Replace("name","LoadRate_phsB");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_LoadRate_phsB));
    body.Add(sbodydata);

    sbodydata.Replace("name","LoadRate_phsC");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_LoadRate_phsC));
    body.Add(sbodydata);

    //组最大负荷 
    sbodydata.Replace("name","LoadMax");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_MaxLoad));
    body.Add(sbodydata);

   /*  sbodydata.Replace("name","LoadMax_phsA");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_MaxLoad_phsA));
    body.Add(sbodydata);

    sbodydata.Replace("name","LoadMax_phsB");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_MaxLoad_phsB));
    body.Add(sbodydata);

    sbodydata.Replace("name","LoadMax_phsC");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_MaxLoad_phsC));
    body.Add(sbodydata); */

    //组可开放容量   
    sbodydata.Replace("name","TotResLoad");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_TotResLoad));
    body.Add(sbodydata);

    sbodydata.Replace("name","ResLoad_phsA");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_ResLoad_phsA));
    body.Add(sbodydata);

    sbodydata.Replace("name","ResLoad_phsB");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_ResLoad_phsB));
    body.Add(sbodydata);

    sbodydata.Replace("name","ResLoad_phsC");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_ResLoad_phsC));
    body.Add(sbodydata);
  
    oJson.Add("body",body);
    
    char pub_topic[256] = {0};
    snprintf(pub_topic, 128, CFG_APP_NAME"/dataCenter/JSON/set/request/%s/%s",CParamManager::CreateInstance().m_devModel.c_str(), CParamManager::CreateInstance().m_devDev.c_str());
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}
// 推送一次实时数据-遥信
void CTaskManager::MqttPackSetRealTimeData_yx(std::string name)
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("data_row", "single"); // 实时数据

    neb::CJsonObject body;

    // 打印变量信息
    neb::CJsonObject sbodydata;
    sbodydata.Add("name", name.c_str());
    if (name.compare("Hz") == 0)
    {
        sbodydata.Add("val", std::to_string(CParamManager::CreateInstance().m_ADC_value[name]));
    }
    else
    {
        sbodydata.Add("val", std::to_string(CParamManager::CreateInstance().m_pdAnalyzer_value_YX[name]));
    }
    sbodydata.Add("quality", "0");
    sbodydata.Add("secret", "1");
    sbodydata.Add("timestamp", jsheader.timestamp);
    body.Add(sbodydata);

    oJson.Add("body", body);

    // char pub_topic[256] = {0};
    // snprintf(pub_topic, 128, CFG_APP_NAME "/dataCenter/JSON/set/request/%s/%s",CParamManager::CreateInstance().m_devModel.c_str(), CParamManager::CreateInstance().m_devDev.c_str());
    std::string pub_topic = CFG_APP_NAME;
    pub_topic += "/dataCenter/JSON/set/request/";
    pub_topic += CParamManager::CreateInstance().m_devModel;
    pub_topic += "/";
    pub_topic += CParamManager::CreateInstance().m_devDev;


    std::string mssm = oJson.ToString();

    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), mssm.size(), mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)(pub_topic.c_str()), pub_topic.size(), pub_topic.size());
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

//交采可开放性容量分析遥测数据 广播  CFG_APP_NAME"/Broadcast/JSON/report/notification
void CTaskManager::MqttPackNotificationData_1_oc() 
{
    mskprintf("\n上报一次可开放容量分析遥测数据\n");
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("datatype", "0");    //遥测数据

    neb::CJsonObject body;   

    //组配变负载率
    neb::CJsonObject sbodydata;        
    sbodydata.Add("name", "LoadRate");
    sbodydata.Add("id", "1");
    sbodydata.Add("val", std::to_string(CParamManager::CreateInstance().m_LoadRate*100));//licong
    sbodydata.Add("unit", "");
    sbodydata.Add("quality", "0");
    sbodydata.Add("timestamp", jsheader.timestamp);    
    body.Add(sbodydata);

    sbodydata.Replace("name","LoadRate_phsA");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_LoadRate_phsA));
    body.Add(sbodydata);

    sbodydata.Replace("name","LoadRate_phsB");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_LoadRate_phsB));
    body.Add(sbodydata);

    sbodydata.Replace("name","LoadRate_phsC");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_LoadRate_phsC));
    body.Add(sbodydata);

    //组最大负荷 
    sbodydata.Replace("name","LoadMax");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_MaxLoad));
    body.Add(sbodydata);

    /* sbodydata.Replace("name","LoadMax_phsA");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_MaxLoad_phsA));
    body.Add(sbodydata);

    sbodydata.Replace("name","LoadMax_phsB");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_MaxLoad_phsB));
    body.Add(sbodydata);

    sbodydata.Replace("name","LoadMax_phsC");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_MaxLoad_phsC));
    body.Add(sbodydata); */

    //组可开放容量   
    sbodydata.Replace("name","TotResLoad");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_TotResLoad));
    body.Add(sbodydata);

    sbodydata.Replace("name","ResLoad_phsA");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_ResLoad_phsA));
    body.Add(sbodydata);

    sbodydata.Replace("name","ResLoad_phsB");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_ResLoad_phsB));
    body.Add(sbodydata);

    sbodydata.Replace("name","ResLoad_phsC");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_ResLoad_phsC));
    body.Add(sbodydata);
   
    oJson.Add("body",body);
    
    char pub_topic[256] = {0};
    snprintf(pub_topic, 128, CFG_APP_NAME"/Broadcast/JSON/report/notification/%s/%s",CParamManager::CreateInstance().m_devModel.c_str(), CParamManager::CreateInstance().m_devDev.c_str());
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }

    mskprintf("\n上报遥测数据结束\n");

}

//推送一次 数据变化上报 遥测数据
void CTaskManager::MqttPackNotificationData_1()
{
    mskprintf("\n上报一次遥测数据\n");
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("datatype", "0");    //遥测数据

    neb::CJsonObject body;   

    // 打印变量信息
    std::map<std::string, float>::iterator iter;
    for (iter = CParamManager::CreateInstance().m_pdAnalyzer_value.begin(); iter != CParamManager::CreateInstance().m_pdAnalyzer_value.end(); iter++)
    { 
        //跳过初始值
        float fvlaue = iter->second;
        if(fvlaue > 9998 || fvlaue <-9998)
        {
            continue;
        }   
        // neb::CJsonObject sbodydata;        
        // sbodydata.Add("name", iter->first.c_str());
        // sbodydata.Add("id", "1");
        // sbodydata.Add("val", std::to_string(iter->second));
        // sbodydata.Add("unit", "");
        // sbodydata.Add("quality", "0");        
        // sbodydata.Add("timestamp", jsheader.timestamp);        
        // body.Add(sbodydata); 
 		std::string name = iter->first;
		 if (name.compare("ImbNgV") == 0 || name.compare("ImbNgA") == 0 || name.compare("ImbNgLoad") == 0 ||name.compare("LoadRate") == 0  )
        {
            neb::CJsonObject sbodydata;
            sbodydata.Add("name", iter->first.c_str());
            sbodydata.Add("val", std::to_string(100*iter->second));
            sbodydata.Add("quality", "0");
            sbodydata.Add("secret", "1");
            sbodydata.Add("timestamp", jsheader.timestamp);
            body.Add(sbodydata);
        }
        else
        {
            neb::CJsonObject sbodydata;
            sbodydata.Add("name", iter->first.c_str());
            sbodydata.Add("val", std::to_string(iter->second));
            sbodydata.Add("quality", "0");
            sbodydata.Add("secret", "1");
            sbodydata.Add("timestamp", jsheader.timestamp);
            body.Add(sbodydata);
        }
    }
   
    oJson.Add("body",body);
    
    char pub_topic[256] = {0};
    snprintf(pub_topic, 128, CFG_APP_NAME"/Broadcast/JSON/report/notification/%s/%s",CParamManager::CreateInstance().m_devModel.c_str(), CParamManager::CreateInstance().m_devDev.c_str());
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
    
    mskprintf("\n上报遥测数据结束\n");
}

void CTaskManager::MqttPackNotificationData_1(std::string guid,std::string model)
{
    mskprintf("\n上报一次遥测数据\n");
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n"); 
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("datatype", "0");    //遥测数据

    neb::CJsonObject body;   

    // 打印变量信息
    std::map<std::string, float>::iterator iter;
    for (iter = CParamManager::CreateInstance().m_dev_Analyzer_value[guid].begin(); iter != CParamManager::CreateInstance().m_dev_Analyzer_value[guid].end(); iter++)
    { 
        //跳过初始值
        float fvlaue = iter->second;
        if(fvlaue > 9998 || fvlaue <-9998)
        {
            continue;
        }   

 		std::string name = iter->first;
		 if (name.compare("ImbNgV") == 0 || name.compare("ImbNgA") == 0 || name.compare("ImbNgLoad") == 0 ||name.compare("LoadRate") == 0  )
        {
            neb::CJsonObject sbodydata;
            sbodydata.Add("name", iter->first.c_str());
            sbodydata.Add("val", std::to_string(100*iter->second));
            sbodydata.Add("quality", "0");
            sbodydata.Add("secret", "1");
            sbodydata.Add("timestamp", jsheader.timestamp);
            body.Add(sbodydata);
        }
        else
        {
            neb::CJsonObject sbodydata;
            sbodydata.Add("name", iter->first.c_str());
            sbodydata.Add("val", std::to_string(iter->second));
            sbodydata.Add("quality", "0");
            sbodydata.Add("secret", "1");
            sbodydata.Add("timestamp", jsheader.timestamp);
            body.Add(sbodydata);
        }
    }
   
    oJson.Add("body",body);
    
    char pub_topic[256] = {0};
    snprintf(pub_topic, 128, CFG_APP_NAME"/Broadcast/JSON/report/notification/%s/%s",model.c_str(), guid.c_str());
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
    
    mskprintf("\n上报遥测数据结束\n");
}
//推送一次 数据变化上报 遥信数据
void CTaskManager::MqttPackNotificationData_2(std::string name)
{
    mskprintf("\n上报一次遥信数据  %s\n",name.c_str());
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }


    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("datatype", "1");    //遥信数据 
    oJson.AddEmptySubArray("body");
    
    neb::CJsonObject body;    
    body.Add("name",name);
    body.Add("id","");
    body.Add("val",std::to_string(CParamManager::CreateInstance().m_pdAnalyzer_value_YX[name]));
    body.Add("unit","");
    body.Add("quality","");
    body.Add("timestamp",jsheader.timestamp);
    oJson["body"].Add(body);

    
    
    char pub_topic[256] = {0};
    snprintf(pub_topic, 128, CFG_APP_NAME"/Broadcast/JSON/report/notification/%s/%s",CParamManager::CreateInstance().m_devModel.c_str(), CParamManager::CreateInstance().m_devDev.c_str());
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
    MqttPackSetRealTimeData_yx(name);
    mskprintf("\n上报遥信数据结束\n");   
}

void CTaskManager::MqttPackNotificationData_2(std::string name,std::string guid)
{
    mskprintf("\n上报一次遥信数据  %s\n",name.c_str());
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }


    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);  
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("datatype", "1");    //遥信数据 
    oJson.AddEmptySubArray("body");
    
    neb::CJsonObject body;    
    body.Add("name",name);
    body.Add("id","");
    body.Add("val",std::to_string(CParamManager::CreateInstance().m_pdAnalyzer_value_YX[name]));
    body.Add("unit","");
    body.Add("quality","");
    body.Add("timestamp",jsheader.timestamp);
    oJson["body"].Add(body);

    std::string model = CParamManager::CreateInstance().m_dev_model[guid];
    
    char pub_topic[256] = {0};
    snprintf(pub_topic, 128, CFG_APP_NAME"/Broadcast/JSON/report/notification/%s/%s",model.c_str(), guid.c_str());
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
    MqttPackSetRealTimeData_yx(name);
    mskprintf("\n上报遥信数据结束\n");  
}
//极值推送
void CTaskManager::MqttPackNotificationData_4()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("datatype", "3");    //极值数据

    neb::CJsonObject bodyMax;   
    bodyMax.Add("type","0");//极大值    
    neb::CJsonObject dataMax; 
    JsonPackNameData(dataMax,"PhV_phsA","PhVMaxDay_phsA",CParamManager::CreateInstance().m_PhVMaxDay_phsA_TIME.c_str());
    JsonPackNameData(dataMax,"PhV_phsB","PhVMaxDay_phsB",CParamManager::CreateInstance().m_PhVMaxDay_phsB_TIME.c_str());
    JsonPackNameData(dataMax,"PhV_phsC","PhVMaxDay_phsC",CParamManager::CreateInstance().m_PhVMaxDay_phsC_TIME.c_str());
/*     JsonPackNameData(dataMax,"PhV_phsA","PhVMaxMon_phsA",CParamManager::CreateInstance().m_PhVMaxMon_phsA_TIME.c_str());
    JsonPackNameData(dataMax,"PhV_phsB","PhVMaxMon_phsB",CParamManager::CreateInstance().m_PhVMaxMon_phsB_TIME.c_str());
    JsonPackNameData(dataMax,"PhV_phsC","PhVMaxMon_phsC",CParamManager::CreateInstance().m_PhVMaxMon_phsC_TIME.c_str()); */
    bodyMax.Add("data",dataMax);

    neb::CJsonObject bodyMin;   
    bodyMin.Add("type","1");//极小值
    neb::CJsonObject dataMin; 
    JsonPackNameData(dataMin,"PhV_phsA","PhVMinDay_phsA",CParamManager::CreateInstance().m_PhVMinDay_phsA_TIME.c_str());
    JsonPackNameData(dataMin,"PhV_phsB","PhVMinDay_phsB",CParamManager::CreateInstance().m_PhVMinDay_phsB_TIME.c_str());
    JsonPackNameData(dataMin,"PhV_phsC","PhVMinDay_phsC",CParamManager::CreateInstance().m_PhVMinDay_phsC_TIME.c_str());
/*     JsonPackNameData(dataMin,"PhV_phsA","PhVMinMon_phsA",CParamManager::CreateInstance().m_PhVMinMon_phsA_TIME.c_str());
    JsonPackNameData(dataMin,"PhV_phsB","PhVMinMon_phsB",CParamManager::CreateInstance().m_PhVMinMon_phsB_TIME.c_str());
    JsonPackNameData(dataMin,"PhV_phsC","PhVMinMon_phsC",CParamManager::CreateInstance().m_PhVMinMon_phsC_TIME.c_str()); */
    bodyMin.Add("data",dataMin);
    neb::CJsonObject body;
    body.Add(bodyMax); 
    body.Add(bodyMin);  
    oJson.Add("body",body);    


    char pub_topic[256] = {0}; 
    snprintf(pub_topic, 128, CFG_APP_NAME"/Broadcast/JSON/report/notification/%s/%s",CParamManager::CreateInstance().m_devModel.c_str(), CParamManager::CreateInstance().m_devDev.c_str());
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

//日月合格率
void CTaskManager::MqttPackNotificationData_5()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("datatype", "4");    //日月合格率推送数据

    neb::CJsonObject bodyDay;   
    bodyDay.Add("type","0");//日合格率值
    bodyDay.Add("reporttime",jsheader.timestamp);//
    neb::CJsonObject dataDay; 
    JsonPackNameData(dataDay,"PhVPass_phsA","PhVPassDay_phsA");
    JsonPackNameData(dataDay,"PhVPass_phsB","PhVPassDay_phsB");
    JsonPackNameData(dataDay,"PhVPass_phsC","PhVPassDay_phsC");
    bodyDay.Add("data",dataDay);

    neb::CJsonObject bodyMon;   
    bodyMon.Add("type","1");//月合格率
    bodyDay.Add("reporttime",jsheader.timestamp);//

    neb::CJsonObject dataMon; 
    JsonPackNameData(dataMon,"PhVPass_phsA","PhVPassMon_phsA");
    JsonPackNameData(dataMon,"PhVPass_phsB","PhVPassMon_phsB");
    JsonPackNameData(dataMon,"PhVPass_phsC","PhVPassMon_phsC");
    bodyMon.Add("data",dataMon);
    

    neb::CJsonObject body;
    body.Add(bodyDay); 
    body.Add(bodyMon);  
    oJson.Add("body",body);    


    char pub_topic[256] = {0};
    snprintf(pub_topic, 128, CFG_APP_NAME"/Broadcast/JSON/report/notification/%s/%s",CParamManager::CreateInstance().m_devModel.c_str(), CParamManager::CreateInstance().m_devDev.c_str());
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }

}

//日月越限推送
void CTaskManager::MqttPackNotificationData_6()
{
     mqtt_header_s jsheader = {0}; 
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("datatype", "5");    //日月越限推送

    neb::CJsonObject bodyDay;   
    bodyDay.Add("type","0");//日越限推送
    bodyDay.Add("reporttime",jsheader.timestamp);//

    neb::CJsonObject upperDataDay;   
    JsonPackNameData(upperDataDay,"PTOV_Rate_phsA","PTOV_Rate_Day_phsA"); 
    JsonPackNameData(upperDataDay,"PTOV_Rate_phsB","PTOV_Rate_Day_phsB");
    JsonPackNameData(upperDataDay,"PTOV_Rate_phsC","PTOV_Rate_Day_phsC");
    bodyDay.Add("upperData",upperDataDay);

    neb::CJsonObject lowerDataDay;   
    JsonPackNameData(lowerDataDay,"PTUV_Rate_phsA","PTUV_Rate_Day_phsA");
    JsonPackNameData(lowerDataDay,"PTUV_Rate_phsB","PTUV_Rate_Day_phsB");
    JsonPackNameData(lowerDataDay,"PTUV_Rate_phsC","PTUV_Rate_Day_phsC");
    bodyDay.Add("lowerData",lowerDataDay);


    
    neb::CJsonObject bodyMon;   
    bodyMon.Add("type","1");//月越限推送
    bodyMon.Add("reporttime",jsheader.timestamp); 
    neb::CJsonObject upperDataMon;   
    JsonPackNameData(upperDataMon,"PTOV_Rate_phsA","PTOV_Rate_Mon_phsA");
    JsonPackNameData(upperDataMon,"PTOV_Rate_phsB","PTOV_Rate_Mon_phsB");
    JsonPackNameData(upperDataMon,"PTOV_Rate_phsC","PTOV_Rate_Mon_phsC");
    bodyMon.Add("upperData",upperDataMon);

    neb::CJsonObject lowerDataMon;   
    JsonPackNameData(lowerDataMon,"PTUV_Rate_phsA","PTUV_Rate_Mon_phsA");
    JsonPackNameData(lowerDataMon,"PTUV_Rate_phsB","PTUV_Rate_Mon_phsB");
    JsonPackNameData(lowerDataMon,"PTUV_Rate_phsC","PTUV_Rate_Mon_phsC");
    bodyMon.Add("lowerData",lowerDataMon);
  
    neb::CJsonObject body;
    body.Add(bodyDay); 
    body.Add(bodyMon);  
    oJson.Add("body",body);    
    
    

    char pub_topic[256] = {0};
    snprintf(pub_topic, 128, CFG_APP_NAME"/Broadcast/JSON/report/notification/%s/%s",CParamManager::CreateInstance().m_devModel.c_str(), CParamManager::CreateInstance().m_devDev.c_str());
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

//故障报告推送
void CTaskManager::MqttPackNotificationData_7()
{
}

bool CTaskManager::JsonPackNameData(neb::CJsonObject &obj, std::string name, std::string pdname, const char *timestamp)
{
    // 跳过初始值
    float fvlaue = CParamManager::CreateInstance().m_pdAnalyzer_value[pdname];
    if (fvlaue > 9998 || fvlaue < -9998)
    {
        return false;
    }

    neb::CJsonObject sondata;
    sondata.Add("name",name);  
    sondata.Add("id","1");
    sondata.Add("val",std::to_string(CParamManager::CreateInstance().m_pdAnalyzer_value[pdname])); 
    sondata.Add("timestamp", timestamp);
    obj.Add(sondata);
    return true;
}

bool CTaskManager::JsonPackNameData(neb::CJsonObject &obj, std::string name, std::string pdname)
{    
    neb::CJsonObject sondata;
    sondata.Add("name",name);  
    sondata.Add("id","1");
    sondata.Add("val",std::to_string(CParamManager::CreateInstance().m_pdAnalyzer_value[pdname]));     
    obj.Add(sondata);
    return true;
}

void CTaskManager::UnpackEsn(neb::CJsonObject obj)
{
    // bool bRet = true;
    neb::CJsonObject obj_body;
    if (!obj.Get("body", obj_body))
    {
        return;
    }

    if (obj_body.Get("serialNumber", m_esn))
    {
        mskprintf("serialNumber = %s.\n\r", m_esn.c_str());
        n_ops = OPS_INIT_OK;
    }
}

void CTaskManager::MqttPackOnline(CDataObj *obj)
{
    if (obj == NULL)
    {
        return;
    }

    if (m_esn.empty())
    {
        return;
    }

    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;

    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);

    neb::CJsonObject oBodyJson;
    oBodyJson.Add("state", "online");
    // obj->m_startTime
    C256String eventTime;
    STimeInfo st = ii_get_current_time();

    // eventTime.Format("%04d-%02d-%02dT%02d:%02d:%02dZ",
    //     obj->m_startTime.nYear,
    //     obj->m_startTime.nMonth,
    //     obj->m_startTime.nDay,
    //     obj->m_startTime.nHour,
    //     obj->m_startTime.nMinute,
    //     obj->m_startTime.nSecond);

    // 测试
    eventTime.Format("%04d-%02d-%02dT%02d:%02d:%02dZ",
                     st.nYear,
                     st.nMonth,
                     st.nDay,
                     st.nHour,
                     st.nMinute,
                     st.nSecond);

    oBodyJson.Add("event-time", eventTime.ToString()); // 第一次收到交采数据的
    oJson.Add("body", oBodyJson);

    ////mskprintf("ReadModelFile deviceType:%s.\n", obj->m_deviceType.c_str());
    // mskprintf("ReadModelFile manufacturerId:%s.\n", obj->m_manufacturerId.c_str());
    // mskprintf("ReadModelFile protocolType:%s.\n", obj->m_protocolType.c_str());
    // mskprintf("ReadModelFile manufacturerName:%s.\n", obj->m_manufacturerName.c_str());
    // mskprintf("ReadModelFile model:%s.\n", obj->m_model.c_str());
    // mskprintf("ReadModelFile topic:%s.\n", obj->m_topic.c_str());
    // mskprintf("ReadModelFile m_guid:%s.\n", obj->m_guid.guid.c_str());
    // mskprintf("ReadModelFile dev:%s.\n", obj->m_guid.dev.c_str());

    

    C256String pub_topic;
    pub_topic.Format("%s/notify/event/gwTerminal/status/%s/%s/%s/%s/%s/%s%s",
                     CFG_APP_NAME,
                     obj->m_manufacturerId.c_str(),
                     obj->m_manufacturerName.c_str(),
                     obj->m_deviceType.c_str(),
                     obj->m_model.c_str(),
                     obj->m_protocolType.c_str(),
                     obj->m_guid.dev.c_str(),
                     m_esn.c_str());

    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
        item.msg_send_lenth = mssm.size();
        item.retained = 1;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
        // CMqttClientInterManager::CreateInstance().agent_MqttMsgPub((char*)(mssm.c_str()), (char *)pub_topic.ToChar(), 1);
    }
}

void CTaskManager::MqttPackData(CDataObj *obj)
{
    bool bRet = false;
    if (obj == NULL)
    {
        return;
    }

    if (m_esn.empty())
    {
        return;
    }

    std::list<CNameObj>::iterator it = obj->m_SendNameObjs.begin();
    for (; it != obj->m_SendNameObjs.end(); it++)
    {
        bRet = false;
        CNameObj nameObj = *it;
        std::string serviceId = nameObj.m_serviceName;

        if (serviceId.compare("discrete") == 0 && CParamManager::CreateInstance().m_yxcycle == 0)
        {
            continue;
        }

        mqtt_header_s jsheader = {0};
        int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
        if (ret != MQTT_OK)
        {
            MG_LOG_E("\nJson head fill failed.\n");
            return;
        }

        SMTimeInfo currenttime = ::ii_get_current_mtime();
        ret = snprintf(jsheader.timestamp, 32, "%04d-%02d-%02dT%02d:%02d:%02dZ",
                       currenttime.nYear,
                       currenttime.nMonth,
                       currenttime.nDay,
                       currenttime.nHour,
                       currenttime.nMinute,
                       0);

        neb::CJsonObject oJson;

        oJson.Add("token", jsheader.token);
        oJson.Add("timestamp", jsheader.timestamp);

        neb::CJsonObject oBodyJson;
        neb::CJsonObject oServicePropertiesJson;
        // neb::CJsonObject oDataPropertiesJson;

        oBodyJson.Add("serviceId", serviceId);
        std::list<std::string>::iterator itName = nameObj.m_names.begin();
        for (; itName != nameObj.m_names.end(); itName++)
        {
            std::string sname = *itName;
            std::string svalue = obj->m_RealDatas[sname];
            if (svalue.size() > 0)
            {
                oServicePropertiesJson.Add(sname, svalue);
                bRet = true;
            }
        }
        if (bRet)
        {
            oBodyJson.Add("serviceProperties", oServicePropertiesJson);
            oJson.Add("body", oBodyJson);
            C256String pub_topic;
            pub_topic.Format("%s/terminal/dataReport/%s/%s/%s/%s/%s%s",
                             CFG_APP_NAME,
                             obj->m_manufacturerId.c_str(),
                             obj->m_manufacturerName.c_str(),
                             obj->m_deviceType.c_str(),
                             obj->m_model.c_str(),
                             // obj->m_protocolType.c_str(),
                             obj->m_guid.dev.c_str(),
                             m_esn.c_str());

            std::string mssm = oJson.ToString();
            if (mssm.size() > 0 && bRet)
            {
                mqtt_data_info_s item = {0};
                memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
                memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
                item.msg_send_lenth = mssm.size();
                item.retained = 0;
                CMqttClientInterManager::CreateInstance().Push_SendItem(item);
                // CMqttClientInterManager::CreateInstance().agent_MqttMsgPub((char*)(mssm.c_str()), (char *)pub_topic.ToChar(), 0);
            }
        }

        // oServicePropertiesJson.Add("dataProperties", oDataPropertiesJson);
        ii_sleep(100);
    }
}

// void CTaskManager::MqttPackData(CDataObj* obj)
//{
//     bool bRet = false;
//     if (obj == NULL) {
//         return;
//     }
//
//     std::map<std::string, CNameObj>::iterator it = obj->m_serviceIds.begin();
//     for (; it != obj->m_serviceIds.end(); it++)
//     {
//         std::string serviceId = it->first;
//         CNameObj nameObj = it->second;
//
//         if (serviceId.compare("discrete") == 0) {
//             continue;
//         }
//
//         mqtt_header_s jsheader = { 0 };
//         int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
//         if (ret != MQTT_OK)
//         {
//             MG_LOG_E("\nJson head fill failed.\n");
//             return;
//         }
//
//         neb::CJsonObject oJson;
//
//         oJson.Add("token", jsheader.token);
//         oJson.Add("timestamp", jsheader.timestamp);
//
//         neb::CJsonObject oBodyJson;
//
//         //neb::CJsonObject oDataPropertiesJson;
//
//         oBodyJson.Add("serviceId", serviceId);
//         std::list<std::string>::iterator itName = nameObj.m_names.begin();
//         for (; itName != nameObj.m_names.end(); itName++)
//         {
//             std::string sname = *itName;
//             std::string svalue = obj->m_RealDatas[sname];
//             if (svalue.size() > 0) {
//                 oServicePropertiesJson.Add(sname, svalue);
//                 bRet = true;
//             }
//         }
//
//         oBodyJson.Add("serviceProperties", oServicePropertiesJson);
//         oJson.Add("body", oBodyJson);
//         C256String pub_topic;
//         pub_topic.Format("%s/terminal/dataReport/%s/%s/%s/%s/%s%s",
//             CFG_APP_NAME,
//             obj->m_manufacturerId.c_str(),
//             obj->m_manufacturerName.c_str(),
//             obj->m_deviceType.c_str(),
//             obj->m_model.c_str(),
//             //obj->m_protocolType.c_str(),
//             obj->m_guid.dev.c_str(),
//             m_esn.c_str());
//
//         std::string mssm = oJson.ToString();
//         if (mssm.size() > 0 && bRet) {
//             mqtt_data_info_s item = { 0 };
//             memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char*)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
//             memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
//             item.msg_send_lenth = mssm.size();
//             item.retained = 0;
//             CMqttClientInterManager::CreateInstance().Push_SendItem(item);
//             //CMqttClientInterManager::CreateInstance().agent_MqttMsgPub((char*)(mssm.c_str()), (char *)pub_topic.ToChar(), 0);
//         }
//         //oServicePropertiesJson.Add("dataProperties", oDataPropertiesJson);
//
//         ii_sleep(1000);
//     }
// }

void CTaskManager::MqttPackSpontData(CDataObj *obj)
{
    bool bRet = false;
    if (obj == NULL)
    {
        return;
    }

    std::list<std::string>::iterator it = obj->m_SpontObj.m_Spontnames.begin();
    for (; it != obj->m_SpontObj.m_Spontnames.end(); it++)
    {
        std::string serviceId = obj->m_SpontObj.m_serviceName;
        CNameObj nameObj = obj->m_SpontObj;
        mqtt_header_s jsheader = {0};
        int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
        if (ret != MQTT_OK)
        {
            MG_LOG_E("\nJson head fill failed.\n");
            return;
        }

        neb::CJsonObject oJson;

        oJson.Add("token", jsheader.token);
        oJson.Add("timestamp", jsheader.timestamp);

        neb::CJsonObject oBodyJson;
        neb::CJsonObject oServicePropertiesJson;
        // neb::CJsonObject oDataPropertiesJson;

        oBodyJson.Add("serviceId", serviceId);
        std::list<std::string>::iterator itName = nameObj.m_Spontnames.begin();
        for (; itName != nameObj.m_Spontnames.end(); itName++)
        {
            std::string sname = *itName;
            std::string svalue = obj->m_RealDatas[sname];
            if (svalue.size() > 0)
            {
                oServicePropertiesJson.Add(sname, svalue);
                bRet = true;
            }
        }
        if (bRet)
        {
            // oServicePropertiesJson.Add("dataProperties", oDataPropertiesJson);
            oBodyJson.Add("serviceProperties", oServicePropertiesJson);
            oJson.Add("body", oBodyJson);
            C256String pub_topic;
            pub_topic.Format("%s/terminal/dataReport/%s/%s/%s/%s/%s%s",
                             CFG_APP_NAME,
                             obj->m_manufacturerId.c_str(),
                             obj->m_manufacturerName.c_str(),
                             obj->m_deviceType.c_str(),
                             obj->m_model.c_str(),
                             // obj->m_protocolType.c_str(),
                             obj->m_guid.dev.c_str(),
                             m_esn.c_str());

            std::string mssm = oJson.ToString();
            if (mssm.size() > 0 && bRet)
            {
                mqtt_data_info_s item = {0};
                memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
                memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
                item.msg_send_lenth = mssm.size();
                item.retained = 0;
                CMqttClientInterManager::CreateInstance().Push_SendItem(item);
                // CMqttClientInterManager::CreateInstance().agent_MqttMsgPub((char*)(mssm.c_str()), (char *)pub_topic.ToChar(), 0);
            }
        }
        ii_sleep(1000);
    }
}

bool CTaskManager::IsFindDiscrete(CDataObj *obj, std::string name, std::string &serveiceName)
{
    bool bRet = false;
    std::string serviceDis = "discrete";
    std::string serviceDis1 = "yx";
    std::string serviceDis2 = "YX";

    std::map<std::string, CNameObj>::iterator iter = obj->m_serviceIds.find(serviceDis);
    if (iter != obj->m_serviceIds.end())
    {
        CNameObj nameObj = obj->m_serviceIds[serviceDis];
        std::list<std::string>::iterator itName = nameObj.m_names.begin();
        for (; itName != nameObj.m_names.end(); itName++)
        {
            std::string sname = *itName;
            if (sname.compare(name.c_str()) == 0)
            {
                serveiceName = serviceDis;
                return true;
            }
        }
    }
    

    std::map<std::string, CNameObj>::iterator iter1 = obj->m_serviceIds.find(serviceDis1);
    if (iter1 != obj->m_serviceIds.end())
    {
        CNameObj nameObj = obj->m_serviceIds[serviceDis1];
        std::list<std::string>::iterator itName = nameObj.m_names.begin();
        for (; itName != nameObj.m_names.end(); itName++)
        {
            std::string sname = *itName;
            if (sname.compare(name.c_str()) == 0)
            {
                serveiceName = serviceDis1;
                return true;
            }
        }
    }

    std::map<std::string, CNameObj>::iterator iter2 = obj->m_serviceIds.find(serviceDis2);
    if (iter2 != obj->m_serviceIds.end())
    {
        CNameObj nameObj = obj->m_serviceIds[serviceDis2];
        std::list<std::string>::iterator itName = nameObj.m_names.begin();
        for (; itName != nameObj.m_names.end(); itName++)
        {
            std::string sname = *itName;
            if (sname.compare(name.c_str()) == 0)
            {
                serveiceName = serviceDis2;
                return true;
            }
        }
    }

    return bRet;
}

void CTaskManager::pdAnalyzer_analysis()
{ 
    // 按分钟计算电能质量分析数据 
    SMTimeInfo tm = ii_get_current_mtime();
    pdAnalyzer_get_time_change(tm);
    static int cnt = 1;
    if(cnt == 1)
    {
        cnt++;
        m_lastTime = tm;
    }
}

int CTaskManager::pdAnalyzer_get_time_change(SMTimeInfo now_time)
{
    //计算周
    int y = m_lastTime.nYear % 100;
    int c = m_lastTime.nYear / 100;
    int m = m_lastTime.nMonth;

    if (m < 3)
    {
        m += 12;
    }
    int d = m_lastTime.nDay;
    Juint8 week = y + y / 4 + c / 4 - 2 * c + 26 * (m + 1) / 10 + d - 1;
    week = week % 7;
    m_lastWeek = week;
    mskprintf("%04d-%02d-%02d %02d:%02d:%02d:%03d week:%d\n",now_time.nYear,now_time.nMonth,now_time.nDay,now_time.nHour,now_time.nMinute,now_time.nSecond,now_time.nMSecond,m_lastWeek);
   
    char calTime[30];
    snprintf(calTime,29,"%04d-%02d-%02dT%02d:%02d:%02d.%03d+0800", now_time.nYear,now_time.nMonth,now_time.nDay,now_time.nHour,now_time.nMinute,now_time.nSecond,now_time.nMSecond);
     
    std::string str_time = calTime;

    //mskprintf("%d--%s\n",m_minute_start,str_time.c_str()); 
    
    

    CParamManager::CreateInstance().judge_m_TCalOvertime();//电荷不平衡度YX;
    CParamManager::CreateInstance().voltage_yx_analysis();//遥信事件判断。
    //CParamManager::CreateInstance().voltage_yx_analysis_ADC(); //遥信事件判断

    std::map<std::string, std::string>::iterator it = CParamManager::CreateInstance().m_dev_model.begin();
    for (; it != CParamManager::CreateInstance().m_dev_model.end(); ++it)
    {
        const std::string &devVal = it->first;
        const std::string &devmodel = it->second;
        CParamManager::CreateInstance().voltage_yx_analysis_dev(devVal);   
    } 
    
    

    

    if (m_minute_start)
    {
        // 计算越限时间
        time_t time_val = time(NULL);
        float interval = 0;
        if (m_lastsec == 0)
        {
            m_lastsec = time_val;
        }
        else
        {
            interval = time_val - m_lastsec;
            m_lastsec = time_val;
        }
        CParamManager::CreateInstance().voltage_limit_analysis(interval,str_time);
    }
    


    //mskprintf("%d--%d\n",m_lastTime.nMinute,now_time.nMinute);

    //分钟有变化 
    if(m_lastTime.nMinute != now_time.nMinute)
    {
        m_lastTime.nMinute = now_time.nMinute;
        if(m_minute_start)
        {
            if(CParamManager::CreateInstance().Unpack_pdAnalyzerData())//一分钟计算一次电能质量，周期参数        
            {
                //计算完发送一次数据
                // 1、实时数据写所有的数据进行写到数据库
                MqttPackSetRealTimeData();
                // 2、数据变化上报，104接收；2.1 遥测推送，
                MqttPackNotificationData_1();

                //MqttPack_pdAnalyzerData();      //测试用  
            } 

            std::map<std::string, std::string>::iterator it = CParamManager::CreateInstance().m_dev_model.begin();
            for (; it != CParamManager::CreateInstance().m_dev_model.end(); ++it)
            {
                const std::string &devVal = it->first;
                const std::string &devmodel = it->second;
                if(CParamManager::CreateInstance().Unpack_pdAnalyzerData(devVal))//一分钟计算一次电能质量，周期参数        
                {
                    MqttPackSetRealTimeData(devVal,devmodel);   
                    MqttPackNotificationData_1(devVal,devmodel);
                       
                }
                if(m_lastTime.nMinute % 15 == 0 )
                {
                    MqttPack_pdAnalyzerData(devVal,devmodel);
                }
                 
            } 

            
            if(m_lastTime.nMinute % 15 == 0 )//每15分钟发送一次上日上月信息，极值
            {
                MqttPack_pdAnalyzerData();
            }
            
        }         

        if(!m_minute_start && now_time.nSecond == 0)
        {
            m_minute_start = true;//作为开始计时标志  避免第一次上电多计数   
            m_ocAnalyzer_T.StartCount();        //同时开始计算可开放容量分析 
			m_ocLoadRate_T.StartCount();        //同时计算配变负载率
        }
    }

    if(m_lastTime.nDay != now_time.nDay) //日变化 
    {
        m_lastTime.nDay = now_time.nDay;  
        //初始化
        CParamManager::CreateInstance().initDayData();

        //一周结束    
        int y = m_lastTime.nYear%100;
        int c = m_lastTime.nYear/100;
        int m = m_lastTime.nMonth;
        if(m <3)
        {
            m+=12;
        }
        int d = m_lastTime.nDay;
        Juint8 week = y+y/4+c/4-2*c+26*(m+1)/10+d-1;
        week = week%7;

        mskprintf("m_lastWeek : %d ,week : %d\n",m_lastWeek, week);
        //周变化
        if(m_lastWeek != week)
        {
            m_lastWeek = week;
            CParamManager::CreateInstance().initWeekData();
        }
    }

    //mskprintf("m_lastTime.nMonth : %d ,now_time.nMonth : %d\n",m_lastTime.nMonth,now_time.nMonth);
    if(m_lastTime.nMonth != now_time.nMonth) //月变化 
    {
        m_lastTime.nMonth = now_time.nMonth;
        CParamManager::CreateInstance().initMonData();
    }

    return 0;
}

void CTaskManager::CalMachinecode(char *str, char *currentCode)
{
     int RecvBuff[4096];
    memset(RecvBuff, 0, sizeof(RecvBuff));
    int nCount = 0;

    // 将机器码字符串转换为整数数组
    for (int i = 0; i < strlen(str); i += 2)
    {
        char str2[3] = {str[i], str[i + 1], '\0'};
        int strtxt = (int)strtol(str2, NULL, 16);
        RecvBuff[nCount] = strtxt;
        nCount++;
    }

    // 根据机器码计算注册码
    int m;
    for (int i = 0; i < nCount; i++)
    {
        int n = RecvBuff[i];
        if (n == 0x10)
        {
            m = 0xFF;
            sprintf(currentCode, "%s%02X%s", currentCode, m, "FF");
        }
        else if (n % 5 == 3)
        {
            m = n + 2;
            sprintf(currentCode, "%s%02X%s", currentCode, m, "MEG");
        }
        else
        {
            m = n - 1;
            if (m < 0)
            {
                m = 0;
                sprintf(currentCode, "%s%02X%s", currentCode, m, "10");
            }
            else if (m % 3 == 1)
            {
                sprintf(currentCode, "%s%02X%s", currentCode, m, "A#");
            }
            else
            {
                sprintf(currentCode, "%s%02X%s", currentCode, m, "C$");
            }
        }
    }
}

void CTaskManager::GetFileNames(string path, vector<string> &filenames)
{
    DIR *pDir;
    struct dirent *ptr;
    if (!(pDir = opendir(path.c_str())))
        return;
    while ((ptr = readdir(pDir)) != 0)
    {
        if (strcmp(ptr->d_name, ".") != 0 && strcmp(ptr->d_name, "..") != 0)
            filenames.push_back(path + "/" + ptr->d_name);
    }
    closedir(pDir);
}

std::string CTaskManager::GetFirsAppName(std::string topic)
{
    std::string str;
    size_t index = topic.find_first_of("/", 0);
    if (index != std::string::npos)
    {
        str = topic.substr(0, index);
    }
    return str;
}

bool CTaskManager::GetStringVlaue(neb::CJsonObject obj, const char *key, C64String &str)
{
    std::string s;
    if (obj.Get(key, s))
    {
        str = s;
        return true;
    }
    else
    {
        return false;
    }
}

std::string CTaskManager::CreatMd5(std::string str)
{
    MD5_CTX md5;
    std::string out;
    unsigned char md[16] = {0};

    if (!MD5_Init(&md5))
    {
        mskprintf("MD5_Init error\n");
        return "";
    }
    if (!MD5_Update(&md5, str.c_str(), strlen(str.c_str())))
    {
        mskprintf("MD5_Update error\n");
        return "";
    }
    if (!MD5_Final(md, &md5))
    {
        mskprintf("MD5_Final error\n");
        return "";
    }
    for (int i = 0; i < 16; i++)
    {
        char outc[3] = {0};
        sprintf(outc, "%02X", md[i]);
        std::string outb = outc;
        out = out + outb;
    }
    return out;
}
CTaskManager::CTaskManager()
{
    m_Thread.SetPackageAcquireManager(this);
    m_T.SetParam(120 * 1000); // 默认120秒
    m_T.StartCount();
    m_ESN_T.SetParam(600 * 1000); // 默认600秒
    m_ESN_T.StartCount();
    m_Data_T.SetParam(30 * 1000); //
    m_Data_T.StartCount();

    mskprintf("电能质量分析m_Stat_Interval_t: %d\n",CParamManager::CreateInstance().m_Stat_Interval_t);
    if(CParamManager::CreateInstance().m_Stat_Interval_t < 1)
    {
        m_pdAnalyzer_T.SetParam(1000*1); // 电能质量分析    

    }
    else
    {
        m_pdAnalyzer_T.SetParam(1000*CParamManager::CreateInstance().m_Stat_Interval_t); // 电能质量分析
    }
    
    m_ocAnalyzer_T.SetParam(1000 *60); // 分钟为单位  可开放容量分析     
	m_ocLoadRate_T.SetParam(1000 * 10);  //10秒计算一次

    

    m_MCCBAnalyzer_T.SetParam(1000 * 60);  //开关一分钟计算一次数据
    m_METERAnalyzer_T.SetParam(1000 * 60 * 15);  //电表十五分钟计算一次数据

    m_minute_start = false;
    m_lastsec = 0;
    m_lastWeek = 0;
}

CTaskManager::~CTaskManager()
{
    std::list<CDataObj *>::iterator iter = m_dataObj.begin();
    for (; iter != m_dataObj.end(); ++iter)
    {
        CDataObj *obj = *iter;
        if (obj != NULL)
        {
            delete obj;
            obj = NULL;
        }
    }
}

//
// void CTaskManager::MqttPackData(CDataObj* obj)
//{
//    bool bRet = false;
//    if (obj == NULL) {
//        return;
//    }
//
//    std::map<std::string, CNameObj>::iterator it = obj->m_serviceIds.begin();
//    for (; it != obj->m_serviceIds.end(); it++)
//    {
//        std::string serviceId = it->first;
//        CNameObj nameObj = it->second;
//
//        if (serviceId.compare("discrete") == 0) {
//            continue;
//        }
//
//        mqtt_header_s jsheader = { 0 };
//        int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
//        if (ret != MQTT_OK)
//        {
//            MG_LOG_E("\nJson head fill failed.\n");
//            return;
//        }
//
//        neb::CJsonObject oJson;
//
//        oJson.Add("token", jsheader.token);
//        oJson.Add("timestamp", jsheader.timestamp);
//
//        neb::CJsonObject oBodyJson;
//        neb::CJsonObject oServicePropertiesJson;
//        neb::CJsonObject oDataPropertiesJson;
//
//        oBodyJson.Add("serviceId", serviceId);
//        std::list<std::string>::iterator itName = nameObj.m_names.begin();
//        for (; itName != nameObj.m_names.end(); itName++)
//        {
//            std::string sname = *itName;
//            std::string svalue = obj->m_RealDatas[sname];
//            if (svalue.size() > 0) {
//                oDataPropertiesJson.Add(sname, svalue);
//                bRet = true;
//            }
//        }
//
//        oServicePropertiesJson.Add("dataProperties", oDataPropertiesJson);
//        oBodyJson.Add("serviceProperties", oServicePropertiesJson);
//        oJson.Add("body", oBodyJson);
//        C256String pub_topic;
//        pub_topic.Format("%s/terminal/dataReport/%s/%s/%s/%s/%s/%s%s",
//            CFG_APP_NAME,
//            obj->m_manufacturerId.c_str(),
//            obj->m_manufacturerName.c_str(),
//            obj->m_deviceType.c_str(),
//            obj->m_model.c_str(),
//            obj->m_protocolType.c_str(),
//            obj->m_guid.dev.c_str(),
//            m_esn.c_str());
//
//        std::string mssm = oJson.ToString();
//        if (mssm.size() > 0 && bRet) {
//            CMqttClientInterManager::CreateInstance().agent_MqttMsgPub((char*)(mssm.c_str()), (char *)pub_topic.ToChar(), 0);
//        }
//        ii_sleep(1000);
//    }
//}
//
// void CTaskManager::MqttPackSpontData(CDataObj* obj)
//{
//    bool bRet = false;
//    if (obj == NULL) {
//        return;
//    }
//
//    std::list<std::string>::iterator it = obj->m_SpontObj.m_Spontnames.begin();
//    for (; it != obj->m_SpontObj.m_Spontnames.end(); it++)
//    {
//        std::string serviceId = obj->m_SpontObj.m_serviceName;
//        CNameObj nameObj = obj->m_SpontObj;
//        mqtt_header_s jsheader = { 0 };
//        int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
//        if (ret != MQTT_OK)
//        {
//            MG_LOG_E("\nJson head fill failed.\n");
//            return;
//        }
//
//        neb::CJsonObject oJson;
//
//        oJson.Add("token", jsheader.token);
//        oJson.Add("timestamp", jsheader.timestamp);
//
//        neb::CJsonObject oBodyJson;
//        neb::CJsonObject oServicePropertiesJson;
//        neb::CJsonObject oDataPropertiesJson;
//
//        oBodyJson.Add("serviceId", serviceId);
//        std::list<std::string>::iterator itName = nameObj.m_Spontnames.begin();
//        for (; itName != nameObj.m_Spontnames.end(); itName++)
//        {
//            std::string sname = *itName;
//            std::string svalue = obj->m_RealDatas[sname];
//            if (svalue.size() > 0) {
//                oDataPropertiesJson.Add(sname, svalue);
//                bRet = true;
//            }
//        }
//
//        oServicePropertiesJson.Add("dataProperties", oDataPropertiesJson);
//        oBodyJson.Add("serviceProperties", oServicePropertiesJson);
//        oJson.Add("body", oBodyJson);
//        C256String pub_topic;
//        pub_topic.Format("%s/terminal/dataReport/%s/%s/%s/%s/%s/%s%s",
//            CFG_APP_NAME,
//            obj->m_manufacturerId.c_str(),
//            obj->m_manufacturerName.c_str(),
//            obj->m_deviceType.c_str(),
//            obj->m_model.c_str(),
//            obj->m_protocolType.c_str(),
//            obj->m_guid.dev.c_str(),
//            m_esn.c_str());
//
//        std::string mssm = oJson.ToString();
//        if (mssm.size() > 0 && bRet) {
//            CMqttClientInterManager::CreateInstance().agent_MqttMsgPub((char*)(mssm.c_str()), (char *)pub_topic.ToChar(), 0);
//        }
//        ii_sleep(1000);
//    }
//}

CNameObj::CNameObj()
{
}

CNameObj::~CNameObj()
{
}

