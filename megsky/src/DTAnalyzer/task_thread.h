

/*=====================================================================
 * 文件：task_thread.h
 *
 * 描述：mqtt订阅和发布线程处理
 *
 * 作者：田振超			2021年9月27日10:28:12
 * 
 * 修改记录：
 =====================================================================*/


#ifndef TASK_THREAD_H
#define TASK_THREAD_H

#include "public_struct.h"
#include "pub_string.h"
#include "pub_thread.h"
#include "pub_logfile.h"
#include "queue_model.h"
#include "CJsonObject.h"
#include "transient.h"

using namespace std;

class CNameObj      // 第一次收到交采主题之后开始发送
{
public:
    CNameObj();
    virtual~CNameObj();

    //CNameObj& operator = (const CNameObj&);
    //CNameObj(const CNameObj&);
public:
    std::string              m_serviceName;
    std::list<std::string>   m_names;
    std::list<std::string>   m_Spontnames;
};


class CDataObj      // 第一次收到交采主题之后开始发送
{
public:
    CDataObj();
    virtual~CDataObj();

public:
    std::string                                     m_topic;
    std::string                                     m_deviceType;
    std::string                                     m_manufacturerId;
    std::string                                     m_manufacturerName;
    std::string                                     m_model;
    std::string                                     m_protocolType;
    guid_body_s                                     m_guid;
    std::map<std::string, CNameObj>                 m_serviceIds;
    CNameObj                                        m_SpontObj;
    std::list<CNameObj>                             m_SendNameObjs;     // 分帧发送的数据
    SMTimeInfo                                      m_startTime;
    bool                                            m_startFlag;
    std::map<std::string, std::string>              m_RealDatas;        // 全部数据name value name唯一
};

//====================================================================================
//线程
//=====================================================================================
class CTaskManager;
class CTaskThread : public CSL_thread
{
public:
    CTaskThread();
    virtual~CTaskThread();
    void SetPackageAcquireManager(CTaskManager * p);

    virtual void run(void);
    virtual std::string ThreadName(void) const;
    virtual void RecordLog(const std::string & sMsg);

private:
    CTaskManager * m_pMng;
    };

//=====================================================================================
//管理者
//=====================================================================================
class CTaskManager
{
public:
    static CTaskManager & CreateInstance();

    bool Init(void);
    void Exit(void);

    void thread_prev(void);
    void thread_func(void);
    void thread_exit(void);

    void OnRun(void);
    void Start(void);
    void Stop(void);

    bool UnpackData(mqtt_data_info_s &real_data);

    bool UnpackDevModelData(neb::CJsonObject &obj);         //解析模型信息
    bool UnpackDevParameterData(neb::CJsonObject &obj);     //解析定值信息


    bool UnpackRealData(neb::CJsonObject &obj);             //解析实时数据获取
    bool UnpackNotificationData(neb::CJsonObject &obj);     //解析数据变化上报
    bool UnPackRealData_MCCB(neb::CJsonObject &obj);        //解析端设备数据
    bool UnPackRealData_Meter(neb::CJsonObject &obj);       //解析电表数据

    bool UnpackRealData(CDataObj *dobj,neb::CJsonObject obj);
    std::string CreatMd5(std::string str);

    bool MqttPack_pdAnalyzerImb();   //计算不平衡度上传

    CTaskManager();
    ~CTaskManager();

    CTaskManager& operator = (const CTaskManager&);
    CTaskManager(const CTaskManager&);
private:

    std::string UnpackToken(neb::CJsonObject obj);                           // 获取Token值
    void MqttPackEsn();
    void MqttGetDevEsn();
    void MqttOS_REG();

    ////////////////////////
    
    /////////////////////////////////
    void MqttPackGetGuid();         //查询GUID
    void MqttPackGetModel();        //模型名称查询
    void MqttPackGetModelInfo();    //模型内容
    void MqttPackGetParameter();    //定值查询
    void MqttPackGetParameter_DTA();    //定值查询
    void MqttPackGetRealTimeData(); //获取一次数据库实时数据
    void MqttPackGetRealTimeData_DTA();  //获取实时数据

    void MqttPackGetDeviceGuid();    //获取模型下的guid

    void MqttPackGetRealTimeData_METER();  //获取电表的实时数据

    void MqttGetDevStatus();  //终端累计运行时间
    void MqttPackSetModelInfo_PQA();    //设置模型
    void MqttPackSetModelInfo_OCFc();    //设置模型
    void MqttPackSetModelInfo_MCCB();    //设置断路器模型
    void MqttPackDevRegister();     //注册Guid
    
public:
    bool MqttPack_pdAnalyzerData();   //计算电能质量分析上传
    bool MqttPack_pdAnalyzerData(std::string guid,std::string model);
    void MqttPackSetRealTimeData();      //推送一次实时数据
    void MqttPackSetRealTimeData(std::string guid,std::string model);
    void MqttPackSetRealTimeData_oc();      //推送一次实时数据
    void MqttPackNotificationData_1();   //推送一次 数据变化上报 遥测数据
    void MqttPackNotificationData_1(std::string guid,std::string model);
    void MqttPackNotificationData_1_oc();   //推送一次 数据变化上报 遥测数据
    void MqttPackSetRealTimeData_yx(std::string name); // 推送一次实时数据-遥信

    void MqttPackNotificationData_2(std::string name);   //推送一次 数据变化上报 遥信数据
    void MqttPackNotificationData_2(std::string name,std::string guid);        //遥信数据
private:
    void MqttPackNotificationData_4();   //推送一次 数据变化上报 极值数据
    void MqttPackNotificationData_5();   //推送一次 数据变化上报 日月合格率推送
    void MqttPackNotificationData_6();   //推送一次 数据变化上报 日月越限推送
    void MqttPackNotificationData_7();   //推送一次 数据变化上报 日重载、日过载推送

    bool JsonPackNameData(neb::CJsonObject &obj,std::string name,std::string pdname,const char *timestamp);
    bool JsonPackNameData(neb::CJsonObject &obj,std::string name,std::string pdname);

    // 发送数据。
    void UnpackEsn(neb::CJsonObject obj);
    void MqttPackOnline(CDataObj* obj);
    void MqttPackData(CDataObj* obj);
    void MqttPackSpontData(CDataObj * obj);
    bool IsFindDiscrete(CDataObj * obj, std::string name, std::string & serveiceName);

    void pdAnalyzer_analysis();

    int pdAnalyzer_get_time_change(SMTimeInfo now_time); 

    void CalMachinecode(char* str, char* currentCode);

    void GetFileNames(string path,vector<string>& filenames);


private:
    // 截取字符串 
    std::string GetFirsAppName(std::string topic);
    bool GetStringVlaue(neb::CJsonObject obj,const char* key,C64String &str);
public:
    std::list<CDataObj*>                     m_dataObj;
private:
    typedef enum
    {
        OPS_NULL,
        OPS_CHECK,
        OPS_OS_SYSTEM,
        OPS_INIT,
        OPS_INIT_OK,
        OPS_REG,
        OPS_REG_OK,
        OPS_IDLE,
    }machine_ops;
    machine_ops     n_ops;
    Juint32                                 m_second; //上次的秒
    CEPTime                                 m_baseTime; //基准时间

    time_t                                  m_lastsec;      //上次计算的秒数
    SMTimeInfo                              m_lastTime;     //上次计算时间
    SMTimeInfo                              m_lastTime_oc;     //上次计算时间-可开放容量
    Juint8                                  m_lastWeek;     //上次计算时间-周
    int                                     m_dDay;         //相隔的天数
    int                                     m_dmin;         //相隔的分钟数

    bool                                    m_minute_start;        //是否是整分钟数
    CIIMutex	                   		 	m_cs;               //  备用
    CTaskThread           		            m_Thread;           //  线程     
    std::string                             m_esn;
    CTimerCnt								m_T;				// 计时器 备用
    CTimerCnt								m_Data_T;				// 计时器 备用
    CTimerCnt								m_ESN_T;		
    CTimerCnt								m_pdAnalyzer_T;	    //定时器，1分钟上送一次数据	
    CTimerCnt								m_ocAnalyzer_T;	    //定时器，1分钟上送一次数据		
	CTimerCnt								m_ocLoadRate_T;	    //定时器，1秒计算一次。配变负载率	
    CTimerCnt                               m_MCCBAnalyzer_T;   //定时器，开关一分钟上报一次数据
    CTimerCnt                               m_METERAnalyzer_T;  //定时器，电表十五分钟分钟上报一次数据


    friend class CTaskThread;

};

#endif
