#include <openssl/md5.h>
#include "mqtt_pub_inter.h"
#include "task_thread.h"
#include "param_json.h"

const double eps = 1e-8;
#define Equ(a, b) ((fabs((a) - (b))) < (eps))
#define More(a, b) (((a) - (b)) > (eps))

CFileObj::CFileObj(void)
{
}

CFileObj::~CFileObj()
{
} 

CParamThread::CParamThread(void)
    : m_pMng(NULL)
{
}

CParamThread::~CParamThread()
{
}

void CParamThread::SetPackageAcquireManager(CParamManager *p)
{
    m_pMng = p;
}

void CParamThread::run(void)
{
    m_pMng->thread_prev();
    while (CSL_thread::IsEnableRun())
    {
        m_pMng->thread_func();
    }
    m_pMng->thread_exit();
}



std::string CParamThread::ThreadName(void) const
{
    return std::string("MQTT 内部Broker线程");
}

void CParamThread::RecordLog(const std::string &sMsg)
{
}

//***************************************************************
// 报文获取管理者
//***************************************************************
CParamManager &CParamManager::CreateInstance()
{
    static CParamManager Mng;
    return Mng;
}

bool CParamManager::Init(void)
{
    initADCData();
    m_FileObjs.clear();
    if (!ReadJsonFile())
    {
        return false;
    }

    mskprintf(":::2\n");
    ReadJsonFile_preData();//读初始数据

    WriteJsonFile_preData();
    
    return true;
}

void CParamManager::Exit(void)
{

    m_Thread.close();
    m_Thread.wait_for_end();
}

void CParamManager::thread_prev(void)
{
}

void CParamManager::thread_func(void)
{
    OnRun();
}

void CParamManager::thread_exit(void)
{
}
void CParamManager::OnRun(void)
{
    mskprintf("OnRun\n");
    /* // 负荷不平衡度
    if (m_T.CalOvertime())    
    {        
        mskprintf("m_T 超时\n");
        m_pdAnalyzer_value_YX["ImbNgLoad_Alm"]=1;
        CTaskManager::CreateInstance().MqttPackNotificationData_2("ImbNgLoad_Alm");
        m_T.StopCount();
    } */

    ii_sleep(100);    
}

void CParamManager::Start(void)
{
}

void CParamManager::Stop(void)
{
}

bool CParamManager::ReadJsonFile()
{
    CIIFile iiFile(DEVICE_INFO_FILEPATH_ABS);
    int filesize = iiFile.GetLength();
    mskprintf("filesize == (%d).\n", filesize);
    if (filesize <= 0)
    {
        return false;
    }
    char dataBuf[filesize] = {0};

    if (iiFile.Open("r+")) // file exist
    {
        Jboolean bRead = iiFile.Read(dataBuf, filesize);
        if (!bRead)
        {
            IEC_LOG_RECORD(eErrType, "file read" DEVICE_INFO_FILEPATH_ABS "failed.");
            return false;
        }
    }
    iiFile.Close();
    
    mskprintf("m_databuffer == (%s).\n", dataBuf);
    neb::CJsonObject oJson;
    if (!oJson.Parse(dataBuf))
    {
        IEC_LOG_RECORD(eErrType, "json_loadb real_data.msg_send failed.");
        return false;
    }

    neb::CJsonObject obj_body;
    if (!oJson.Get("body", obj_body))
    {
        IEC_LOG_RECORD(eErrType, " ReadJsonFile body = NULL.");
        return false;
    }

    if (!obj_body.IsArray())
    {
        IEC_LOG_RECORD(eErrType, "ReadJsonFile body is not Array.");
        return false;
    }

    int asize = obj_body.GetArraySize();
    for (int i = 0; i < asize; i++)
    {
        neb::CJsonObject obj_data;
        if (!obj_body.Get(i, obj_data))
        {
            mskprintf("ReadJsonFile body data get error i = %d.\n", i);
            return false;
        }

        std::string info;
        if (!obj_data.Get("info", info))
        {
            IEC_LOG_RECORD(eErrType, "file:" DEVICE_INFO_FILEPATH_ABS "read body info failed.");
            return false;
        }

        if (info == "acMeter_guid")//交采设备，用于向数据中心获取guid
        {
            m_obj_body_dev = obj_data;
            if (!obj_data.Get("model", m_devModel))
            {
                IEC_LOG_RECORD(eErrType, "file:" DEVICE_INFO_FILEPATH_ABS "read body model failed.");
                return false;
            }

            if (!obj_data.Get("port", m_devPort))
            {
                IEC_LOG_RECORD(eErrType, "file:" DEVICE_INFO_FILEPATH_ABS "read body port failed.");
            }

            if (!obj_data.Get("addr", m_devAddr))
            {
                IEC_LOG_RECORD(eErrType, "file:" DEVICE_INFO_FILEPATH_ABS "read body addr failed.");
            }
            
            if (!obj_data.Get("desc", m_devDesc))
            {
                IEC_LOG_RECORD(eErrType, "file:" DEVICE_INFO_FILEPATH_ABS "read body desc failed.");
            }
            obj_data.Get("manuID", m_devmanuID);
            obj_data.Get("manuName", m_devmanuName);
            obj_data.Get("ProType", m_devProType);
            obj_data.Get("deviceType", m_devdeviceType);
            obj_data.Get("isReport", m_devisReport);
            obj_data.Get("nodeID", m_devnodeID);
            obj_data.Get("productID", m_devproductID);
        }
        else if(info == "model_info")//添加查询模型的列表
        {
            std::string model_list;
            if (obj_data.Get("model", model_list))
            {
                std::string token;
                for (size_t i = 0, start = 0; i <= model_list.size(); ++i)
                {
                    if (i == model_list.size() || model_list[i] == ',')
                    {
                        if (i > start)
                        {
                            token = model_list.substr(start, i - start);
                            size_t l = 0, r = token.size();
                            while (l < r && (token[l] == ' ' || token[l] == '\t')) ++l;
                            while (r > l && (token[r - 1] == ' ' || token[r - 1] == '\t')) --r;
                            if (r > l)
                            {
                                std::string name = token.substr(l, r - l);
                                m_models.push_back(name) ; 
                                mskprintf("model_info add: %s\n", name.c_str());
                            }
                        }
                        start = i + 1;
                    }
                }
            }
            else
            {
                IEC_LOG_RECORD(eErrType, "file:" DEVICE_INFO_FILEPATH_ABS " read model_info.model failed.");
            }
        }
        else if(info == "acMeter_data")//交采采集数据
        {
            m_obj_body_adc = obj_data;
            obj_data.Get("PhV_phsA", m_ADC_value["PhV_phsA"]);
            obj_data.Get("PhV_phsB", m_ADC_value["PhV_phsB"]);
            obj_data.Get("PhV_phsC", m_ADC_value["PhV_phsC"]);
            obj_data.Get("SeqV_c0",  m_ADC_value["SeqV_c0"]);
            obj_data.Get("SeqV_c1",  m_ADC_value["SeqV_c1"]);
            obj_data.Get("SeqV_c2",  m_ADC_value["SeqV_c2"]);
            obj_data.Get("A_phsA",   m_ADC_value["A_phsA"]);
            obj_data.Get("A_phsB",   m_ADC_value["A_phsB"]);
            obj_data.Get("A_phsC",   m_ADC_value["A_phsC"]);
            obj_data.Get("SeqA_c0",  m_ADC_value["SeqA_c0"]);
            obj_data.Get("SeqA_c1",  m_ADC_value["SeqA_c1"]);
            obj_data.Get("SeqA_c2",  m_ADC_value["SeqA_c2"]);
            obj_data.Get("Hz",       m_ADC_value["Hz"]);
            obj_data.Get("PhPF_phsA",m_ADC_value["PhPF_phsA"]);
            obj_data.Get("PhPF_phsB",m_ADC_value["PhPF_phsB"]);
            obj_data.Get("PhPF_phsC",m_ADC_value["PhPF_phsC"]);
            obj_data.Get("TotPF",    m_ADC_value["TotPF"]);
        }
        else if(info == "param")//定值数据  
        {
            m_obj_body_para = obj_data;
            obj_data.Get("PowOff_Enable",m_Param_value["PowOff_Enable"]);            
            obj_data.Get("PowerOff_V_Lim", m_Param_value["PowerOff_V_Lim"]);
            obj_data.Get("PowerOff_Dly", m_Param_value["PowerOff_Dly"]);
            obj_data.Get("PowOn_Enable",m_Param_value["PowOn_Enable"]);
            obj_data.Get("PowerOn_V_Lim", m_Param_value["PowerOn_V_Lim"]);
            obj_data.Get("PowerOn_Dly", m_Param_value["PowerOn_Dly"]);
            obj_data.Get("PTUA_Enable",     m_Param_value["PTUA_Enable"]);
            obj_data.Get("PTUA_Dly", m_Param_value["PTUA_Dly"]);
            obj_data.Get("PTUA_Loss_Dly", m_Param_value["PTUA_Loss_Dly"]);
            obj_data.Get("PTUV_Enable", m_Param_value["PTUV_Enable"]);
            obj_data.Get("PTUV_Lim", m_Param_value["PTUV_Lim"]);
            obj_data.Get("PTUV_Dly", m_Param_value["PTUV_Dly"]);
            obj_data.Get("PTOV_Enable", m_Param_value["PTOV_Enable"]);
            obj_data.Get("PTOV_Lim", m_Param_value["PTOV_Lim"]);
            obj_data.Get("PTOV_Dly", m_Param_value["PTOV_Dly"]);
            obj_data.Get("Load_Enable", m_Param_value["Load_Enable"]);
            obj_data.Get("Load_Lim", m_Param_value["Load_Lim"]);
            obj_data.Get("Load_Dly", m_Param_value["Load_Dly"]);
            obj_data.Get("PTOC_Hvld_Enable", m_Param_value["PTOC_Hvld_Enable"]);
            obj_data.Get("PTOC_Hvld_Lim", m_Param_value["PTOC_Hvld_Lim"]);
            obj_data.Get("PTOC_Hvld_Dly", m_Param_value["PTOC_Hvld_Dly"]);
            obj_data.Get("PTOC_Ovld_Enable", m_Param_value["PTOC_Ovld_Enable"]);
            obj_data.Get("PTOC_Ovld_Lim", m_Param_value["PTOC_Ovld_Lim"]);
            obj_data.Get("PTOC_Ovld_Dly", m_Param_value["PTOC_Ovld_Dly"]);
            obj_data.Get("Seqc0OV_Enable", m_Param_value["Seqc0OV_Enable"]);
            obj_data.Get("Seqc0OV_Lim", m_Param_value["Seqc0OV_Lim"]);
            obj_data.Get("Seqc0OV_Dly", m_Param_value["Seqc0OV_Dly"]);
            obj_data.Get("Seqc0OC_Enable", m_Param_value["Seqc0OC_Enable"]);
            obj_data.Get("Seqc0OC_Lim", m_Param_value["Seqc0OC_Lim"]);
            obj_data.Get("Seqc0OC_Dly", m_Param_value["Seqc0OC_Dly"]);
            obj_data.Get("ResA_Enable", m_Param_value["ResA_Enable"]);
            obj_data.Get("ResA_Lim", m_Param_value["ResA_Lim"]);
            obj_data.Get("ResA_Dly", m_Param_value["ResA_Dly"]);
            obj_data.Get("PTUPF_Enable", m_Param_value["PTUPF_Enable"]);
            obj_data.Get("PTUPF_Lim", m_Param_value["PTUPF_Lim"]);
            obj_data.Get("PTUPF_Dly", m_Param_value["PTUPF_Dly"]);
            obj_data.Get("ImbA_Enable", m_Param_value["ImbA_Enable"]); 
            obj_data.Get("Imb_db", m_Param_value["Imb_db"]);
            obj_data.Get("ImbA_Lim", m_Param_value["ImbA_Lim"]);
            obj_data.Get("ImbNgA_phsN_Lim", m_Param_value["ImbNgA_phsN_Lim"]);
            obj_data.Get("ImbA_Dly", m_Param_value["ImbA_Dly"]);
            obj_data.Get("PhvImb_strVal", m_Param_value["PhvImb_strVal"]);
            obj_data.Get("LoadImb_strVal", m_Param_value["LoadImb_strVal"]);
            obj_data.Get("ARtg", m_Param_value["ARtg"]);
            obj_data.Get("ARtgSnd", m_Param_value["ARtgSnd"]);            
            obj_data.Get("zeroLineCTPrimaryRating", m_Param_value["zeroLineCTPrimaryRating"]);
            obj_data.Get("zeroLineCTSecondaryRating", m_Param_value["zeroLineCTSecondaryRating"]); 
            obj_data.Get("RtgA", m_Param_value["RtgA"]);
            obj_data.Get("DeviceLoad", m_Param_value["DeviceLoad"]);
            obj_data.Get("std_voltage", m_Param_value["std_voltage"]);
            obj_data.Get("std_Current", m_Param_value["std_Current"]);
            obj_data.Get("std_Hz", m_Param_value["std_Hz"]);
            obj_data.Get("Mqtt_Send_t", m_Mqtt_Send_t);
            obj_data.Get("Stat_Interval_t", m_Stat_Interval_t);
            obj_data.Get("LoadRate_CalcCyc", m_Param_value["LoadRate_CalcCyc"]);
            obj_data.Get("ResLoad_CalcCyc",  m_Param_value["ResLoad_CalcCyc"]);
            obj_data.Get("VRtg",    m_Param_value["VRtg"]);
            obj_data.Get("VRtgSnd", m_Param_value["VRtgSnd"]); 
            obj_data.Get("inverse_offset_threshold_fs", m_Param_value["inverse_offset_threshold_fs"]);     
            obj_data.Get("A_Zero_Drift", m_Param_value["A_Zero_Drift"]);
            obj_data.Get("PTUV_Open_V_Lim", m_Param_value["PTUV_Open_V_Lim"]);
            obj_data.Get("PTUV_Open_Dly", m_Param_value["PTUV_Open_Dly"]);
            obj_data.Get("DIP_Enable", m_Param_value["DIP_Enable"]);
            obj_data.Get("DIP_strVal_Lim", m_Param_value["DIP_strVal_Lim"]);
            obj_data.Get("DIP_Dly", m_Param_value["DIP_Dly"]);
            obj_data.Get("SWL_Enable", m_Param_value["SWL_Enable"]);
            obj_data.Get("SWL_strVal_Lim", m_Param_value["SWL_strVal_Lim"]);
            obj_data.Get("SWL_Dly", m_Param_value["SWL_Dly"]);
            obj_data.Get("INTR_Enable", m_Param_value["INTR_Enable"]);
            obj_data.Get("INTR_strVal_Lim", m_Param_value["INTR_strVal_Lim"]);
            obj_data.Get("INTR_Dly", m_Param_value["INTR_Dly"]);
            obj_data.Get("PTUV_lim_setVal", m_Param_value["PTUV_lim_setVal"]);
            obj_data.Get("PTOV_lim_setVal", m_Param_value["PTOV_lim_setVal"]);
            obj_data.Get("PTUF_Enable",m_Param_value["PTUF_Enable"]);
            obj_data.Get("PTUF_Lim",m_Param_value["PTUF_Lim"]);
            obj_data.Get("PTUF_Dly",m_Param_value["PTUF_Dly"]);
            obj_data.Get("PTOF_Lim",m_Param_value["PTOF_Lim"]);
            obj_data.Get("PTOF_Dly",m_Param_value["PTOF_Dly"]);
            
            obj_data.Get("ThdPhV_Op_V_Enable",m_Param_value["ThdPhV_Op_V_Enable"]);
            obj_data.Get("ThdPhV_Op_V_Lim",m_Param_value["ThdPhV_Op_V_Lim"]);
            obj_data.Get("ThdPhV_Op_V_Dly",m_Param_value["ThdPhV_Op_V_Dly"]);
            obj_data.Get("ThdA_Op_A_Enable",m_Param_value["ThdA_Op_A_Enable"]);
            obj_data.Get("ThdA_Op_A_Lim",m_Param_value["ThdA_Op_A_Lim"]);
            obj_data.Get("ThdA_Op_A_Dly",m_Param_value["ThdA_Op_A_Dly"]);
            obj_data.Get("PT_Type",m_Param_value["PT_Type"]); //变压器类型，1代表DYN11，2代表YYN0
            obj_data.Get("PQALogic_Enable",m_Param_value["PQALogic_Enable"]); //电能质量治理策略逻辑投退
            

            // mskprintf("PowerOn_V_Lim:%f.\n",m_Param_value["PowerOn_V_Lim"]);
            // mskprintf("A_Zero_Drift:%f.\n",m_Param_value["A_Zero_Drift"]);
            // mskprintf("PTOV_lim_setVal:%f.\n", m_Param_value["PTOV_lim_setVal"]);
            // mskprintf("PTUV_lim_setVal:%f.\n", m_Param_value["PTUV_lim_setVal"]);

            

        }
        else if (info == "pqAnaly_model")//可开放容量分析，用于设置模型
        { 
            m_obj_body_model = obj_data;

            neb::CJsonObject info_data; 
            
            obj_data.Get("body",info_data);

            int count = info_data.GetArraySize();

            for (int j = 0; j < count; j++)
            {
                neb::CJsonObject obj_value;
                if (!info_data.Get(j, obj_value))
                {
                    mskprintf("UnpackDevModelData body data get error j = %d.\n", j);
                    continue;
                }

                //mskprintf("%s\n",obj_value.ToString().c_str());
                model_item_s item;
                obj_value.Get("name", item.name);
                obj_value.Get("type", item.type);
                obj_value.Get("unit", item.unit);
                obj_value.Get("deadzone", item.deadzone);
                obj_value.Get("ratio", item.ratio);
                obj_value.Get("isReport", item.isReport);
                obj_value.Get("userdefine", item.userdefine);
                               
                m_vmodel.push_back(item); 
            }

            obj_data.Get("model", m_devModel);
            obj_data.Get("port", m_devPort);
            obj_data.Get("addr", m_devAddr);
            obj_data.Get("desc", m_devDesc);
        }
        else if (info == "MCCB_param")
        {
            
        }
        else if (info == "MCCB_model")  //端设备模型
        {
            m_obj_body_model = obj_data;  

            neb::CJsonObject info_data; 
            
            obj_data.Get("body",info_data);

            int count = info_data.GetArraySize();

            for (int j = 0; j < count; j++)
            {
                neb::CJsonObject obj_value;
                if (!info_data.Get(j, obj_value))
                {
                    mskprintf("UnpackDevModelData body data get error j = %d.\n", j);
                    continue;
                }

                //mskprintf("%s\n",obj_value.ToString().c_str());
                model_item_s item;
                obj_value.Get("name", item.name);
                obj_value.Get("type", item.type);
                obj_value.Get("unit", item.unit);
                obj_value.Get("deadzone", item.deadzone);
                obj_value.Get("ratio", item.ratio);
                obj_value.Get("isReport", item.isReport);
                obj_value.Get("userdefine", item.userdefine);
                               
                m_vmodel.push_back(item);
            }
            // obj_data.Get("model", m_devModel);
            // obj_data.Get("port", m_devPort);
            // obj_data.Get("addr", m_devAddr);
            // obj_data.Get("desc", m_devDesc);
        }
        else if (info == "pqAnaly_model")//电表模型
        {
            m_obj_body_model = obj_data;

            neb::CJsonObject info_data; 
            
            obj_data.Get("body",info_data);

            int count = info_data.GetArraySize();

            for (int j = 0; j < count; j++)
            {
                neb::CJsonObject obj_value;
                if (!info_data.Get(j, obj_value))
                {
                    mskprintf("UnpackDevModelData body data get error j = %d.\n", j);
                    continue;
                }

                //mskprintf("%s\n",obj_value.ToString().c_str());
                model_item_s item;
                obj_value.Get("name", item.name);
                obj_value.Get("type", item.type);
                obj_value.Get("unit", item.unit);
                obj_value.Get("deadzone", item.deadzone);
                obj_value.Get("ratio", item.ratio);
                obj_value.Get("isReport", item.isReport);
                obj_value.Get("userdefine", item.userdefine);
                               
                m_vmodel.push_back(item);
            }

            obj_data.Get("model", m_devModel);
            obj_data.Get("port", m_devPort);
            obj_data.Get("addr", m_devAddr);
            obj_data.Get("desc", m_devDesc);
        }
    }
    

    // mskprintf("m_devModel:%s.\n", m_devModel.c_str());
    // mskprintf("m_devPort:%s.\n", m_devPort.c_str());
    // mskprintf("m_devAddr:%s.\n", m_devAddr.c_str());
    // mskprintf("m_devDesc:%s.\n", m_devDesc.c_str());
    // mskprintf("ImbA_Lim:%f.\n", m_Param_value["ImbA_Lim"]);
    // mskprintf("ARtgSnd:%f.\n", m_Param_value["ARtgSnd"]);

    m_databuffer = oJson.ToString();
    return true;
}

bool CParamManager::ReadJsonFile_preData()
{
    std::map<std::string,float>::iterator it = m_pdAnalyzer_value.begin();        
    for(;it != m_pdAnalyzer_value.end();++it)
    {
        std::string name = it->first;
        float value = it->second;
        //mskprintf("@@NAME: %s, value: %f\n",name.c_str(),value);
    }


    CIIFile iiFile(PRE_DATA_INFO_FILEPATH_ABS);
    int filesize = iiFile.GetLength();
    mskprintf("filesize == (%d).\n", filesize);
    if (filesize <= 0 ) 
    {
        return false;
    }
    char dataBuf[filesize] = {0};

    if (iiFile.Open("r+")) // file exist
    {
        Jboolean bRead = iiFile.Read(dataBuf, filesize);
        if (!bRead)
        {
            IEC_LOG_RECORD(eErrType, "file read" PRE_DATA_INFO_FILEPATH_ABS "failed.");
            return false;
        }
    }
    iiFile.Close();

    //mskprintf("m_databuffer == (%s).\n", dataBuf);
    neb::CJsonObject oJson;
    if (!oJson.Parse(dataBuf))
    {
        IEC_LOG_RECORD(eErrType, "json_loadb real_data.msg_send failed.");
        return false;
    }
    
    //查看时间
    string date_YMD;
    oJson.Get("date",date_YMD);

    SMTimeInfo tm = ii_get_current_mtime();
    char time_buff[10];
    snprintf(time_buff, 10, "%02d%02d%02d", tm.nYear % 100, tm.nMonth, tm.nDay);
    string preDate = time_buff;

    mskprintf("date:(%s).  (%s)\n", preDate.c_str(),date_YMD.c_str());

    if(preDate != date_YMD)
    {
        return false;
    }

    neb::CJsonObject oJson_pdAnalyzer;
    if (!oJson.Get("oJson_pdAnalyzer", oJson_pdAnalyzer))
    {
        IEC_LOG_RECORD(eErrType, " ReadJsonFile body = NULL.");
        return false;
    }

    it = m_pdAnalyzer_value.begin();

    for(;it != m_pdAnalyzer_value.end();++it)
    {
        string name = it->first;
        float value; 
        oJson_pdAnalyzer.Get(name,value);
        //mskprintf("%s,%f\n",name.c_str(),value);
        m_pdAnalyzer_value[name] = value;
        //mskprintf("----%s,%f\n",name.c_str(),m_pdAnalyzer_value[name]);
    }
    
    neb::CJsonObject oJson_pdAnalyzer_param;
    if (!oJson.Get("pdParam", oJson_pdAnalyzer_param))
    {
        IEC_LOG_RECORD(eErrType, " ReadJsonFile body = NULL.");
        return false;
    }
    
    oJson_pdAnalyzer_param.Get("m_Tol_PhV_Day_phsA",m_Tol_PhV_Day_phsA);
    oJson_pdAnalyzer_param.Get("m_Count_PhV_Day_phsA",m_Count_PhV_Day_phsA);
    oJson_pdAnalyzer_param.Get("m_Tol_PhV_Day_phsB",m_Tol_PhV_Day_phsB);
    oJson_pdAnalyzer_param.Get("m_Count_PhV_Day_phsB",m_Count_PhV_Day_phsB);
    oJson_pdAnalyzer_param.Get("m_Tol_PhV_Day_phsC",m_Tol_PhV_Day_phsC);
    oJson_pdAnalyzer_param.Get("m_Count_PhV_Day_phsC",m_Count_PhV_Day_phsC);

    oJson_pdAnalyzer_param.Get("m_Tol_PhVOfs_Day_phsA",m_Tol_PhVOfs_Day_phsA);
    oJson_pdAnalyzer_param.Get("m_Count_PhVOfs_Day_phsA",m_Count_PhVOfs_Day_phsA);
    oJson_pdAnalyzer_param.Get("m_Tol_PhVOfs_Day_phsB",m_Tol_PhVOfs_Day_phsB);
    oJson_pdAnalyzer_param.Get("m_Count_PhVOfs_Day_phsB",m_Count_PhVOfs_Day_phsB);
    oJson_pdAnalyzer_param.Get("m_Tol_PhVOfs_Day_phsC",m_Tol_PhVOfs_Day_phsC);
    oJson_pdAnalyzer_param.Get("m_Count_PhVOfs_Day_phsC",m_Count_PhVOfs_Day_phsC);

    oJson_pdAnalyzer_param.Get("m_Tol_HzOfs_Day_phsA",m_Tol_HzOfs_Day_phsA);
    oJson_pdAnalyzer_param.Get("m_Count_HzOfs_Day_phsA",m_Count_HzOfs_Day_phsA);
    oJson_pdAnalyzer_param.Get("m_Tol_HzOfs_Day_phsB",m_Tol_HzOfs_Day_phsB);
    oJson_pdAnalyzer_param.Get("m_Count_HzOfs_Day_phsB",m_Count_HzOfs_Day_phsB);
    oJson_pdAnalyzer_param.Get("m_Tol_HzOfs_Day_phsC",m_Tol_HzOfs_Day_phsC);
    oJson_pdAnalyzer_param.Get("m_Count_HzOfs_Day_phsC",m_Count_HzOfs_Day_phsC);


    oJson_pdAnalyzer_param.Get("m_Tol_PhVOfs_Week_phsA",m_Tol_PhVOfs_Week_phsA);
    oJson_pdAnalyzer_param.Get("m_Count_PhVOfs_Week_phsA",m_Count_PhVOfs_Week_phsA);
    oJson_pdAnalyzer_param.Get("m_Tol_PhVOfs_Week_phsB",m_Tol_PhVOfs_Week_phsB);
    oJson_pdAnalyzer_param.Get("m_Count_PhVOfs_Week_phsB",m_Count_PhVOfs_Week_phsB);
    oJson_pdAnalyzer_param.Get("m_Tol_PhVOfs_Week_phsC",m_Tol_PhVOfs_Week_phsC);
    oJson_pdAnalyzer_param.Get("m_Count_PhVOfs_Week_phsC",m_Count_PhVOfs_Week_phsC);

    oJson_pdAnalyzer_param.Get("m_Tol_HzOfs_Week_phsA",m_Tol_HzOfs_Week_phsA);
    oJson_pdAnalyzer_param.Get("m_Count_HzOfs_Week_phsA",m_Count_HzOfs_Week_phsA);
    oJson_pdAnalyzer_param.Get("m_Tol_HzOfs_Week_phsB",m_Tol_HzOfs_Week_phsB);
    oJson_pdAnalyzer_param.Get("m_Count_HzOfs_Week_phsB",m_Count_HzOfs_Week_phsB);
    oJson_pdAnalyzer_param.Get("m_Tol_HzOfs_Week_phsC",m_Tol_HzOfs_Week_phsC);
    oJson_pdAnalyzer_param.Get("m_Count_HzOfs_Week_phsC",m_Count_HzOfs_Week_phsC);

    oJson_pdAnalyzer_param.Get("m_Tol_PhV_Mon_phsA",m_Tol_PhV_Mon_phsA);
    oJson_pdAnalyzer_param.Get("m_Count_PhV_Mon_phsA",m_Count_PhV_Mon_phsA);
    oJson_pdAnalyzer_param.Get("m_Tol_PhV_Mon_phsB",m_Tol_PhV_Mon_phsB);
    oJson_pdAnalyzer_param.Get("m_Count_PhV_Mon_phsB",m_Count_PhV_Mon_phsB);
    oJson_pdAnalyzer_param.Get("m_Tol_PhV_Mon_phsC",m_Tol_PhV_Mon_phsC);
    oJson_pdAnalyzer_param.Get("m_Count_PhV_Mon_phsC",m_Count_PhV_Mon_phsC);

    oJson_pdAnalyzer_param.Get("m_Tol_PhVOfs_Mon_phsA",m_Tol_PhVOfs_Mon_phsA);
    oJson_pdAnalyzer_param.Get("m_Count_PhVOfs_Mon_phsA",m_Count_PhVOfs_Mon_phsA);
    oJson_pdAnalyzer_param.Get("m_Tol_PhVOfs_Mon_phsB",m_Tol_PhVOfs_Mon_phsB);
    oJson_pdAnalyzer_param.Get("m_Count_PhVOfs_Mon_phsB",m_Count_PhVOfs_Mon_phsB);
    oJson_pdAnalyzer_param.Get("m_Tol_PhVOfs_Mon_phsC",m_Tol_PhVOfs_Mon_phsC);
    oJson_pdAnalyzer_param.Get("m_Count_PhVOfs_Mon_phsC",m_Count_PhVOfs_Mon_phsC);

    oJson_pdAnalyzer_param.Get("m_Tol_HzOfs_Mon_phsA",m_Tol_HzOfs_Mon_phsA);
    oJson_pdAnalyzer_param.Get("m_Count_HzOfs_Mon_phsA",m_Count_HzOfs_Mon_phsA);
    oJson_pdAnalyzer_param.Get("m_Tol_HzOfs_Mon_phsB",m_Tol_HzOfs_Mon_phsB);
    oJson_pdAnalyzer_param.Get("m_Count_HzOfs_Mon_phsB",m_Count_HzOfs_Mon_phsB);
    oJson_pdAnalyzer_param.Get("m_Tol_HzOfs_Mon_phsC",m_Tol_HzOfs_Mon_phsC);
    oJson_pdAnalyzer_param.Get("m_Count_HzOfs_Mon_phsC",m_Count_HzOfs_Mon_phsC);

	neb::CJsonObject oJson_ocAnalyzer_param;
    if (!oJson.Get("ocParam", oJson_ocAnalyzer_param))
    {
        IEC_LOG_RECORD(eErrType, " ReadJsonFile ocbody = NULL.");
        return false;
    }

    oJson_ocAnalyzer_param.Get("m_LoadRate",m_LoadRate);//配变负载率
    oJson_ocAnalyzer_param.Get("m_LoadRate_phsA",m_LoadRate_phsA);       //配变负载率A
    oJson_ocAnalyzer_param.Get("m_LoadRate_phsB",m_LoadRate_phsB);       //配变负载率B
    oJson_ocAnalyzer_param.Get("m_LoadRate_phsC",m_LoadRate_phsC);      //配变负载率C

    oJson_ocAnalyzer_param.Get("m_MaxLoadRate",m_MaxLoadRate);//周期内配变负载率
    oJson_ocAnalyzer_param.Get("m_MaxLoadRate_phsA",m_MaxLoadRate_phsA);//周期内配变负载率A
    oJson_ocAnalyzer_param.Get("m_MaxLoadRate_phsB",m_MaxLoadRate_phsB);;    //周期内配变负载率B
    oJson_ocAnalyzer_param.Get("m_MaxLoadRate_phsC",m_MaxLoadRate_phsC);;    //周期内配变负载率C

    oJson_ocAnalyzer_param.Get("m_MaxLoad",m_MaxLoad);  //周期内最大负荷
    oJson_ocAnalyzer_param.Get("m_MaxLoad_phsA",m_MaxLoad_phsA);  //周期内最大负荷a
    oJson_ocAnalyzer_param.Get("m_MaxLoad_phsB",m_MaxLoad_phsB);   //周期内最大负荷b
    oJson_ocAnalyzer_param.Get("m_MaxLoad_phsC",m_MaxLoad_phsC);    //周期内最大负荷c

    oJson_ocAnalyzer_param.Get("m_TotResLoad",m_TotResLoad);    //可开放容量
    oJson_ocAnalyzer_param.Get("m_ResLoad_phsA",m_ResLoad_phsA);  //可开放容量A  //m_ResLoad_phsA  m_ocAnalyzer_phsA
    oJson_ocAnalyzer_param.Get("m_ResLoad_phsB",m_ResLoad_phsB); //可开放容量B
    oJson_ocAnalyzer_param.Get("m_ResLoad_phsC",m_ResLoad_phsC); //可开放容量C 
	
	return false;
}

bool CParamManager::WriteJsonFile_preData()
{   
    std::map<std::string,float>::iterator it = m_pdAnalyzer_value.begin();        
    for(;it != m_pdAnalyzer_value.end();++it)
    {
        std::string name = it->first;
        float value = it->second;
        //mskprintf("NAME: %s, value: %f\n",name.c_str(),value);
    }


    mskprintf("写数据文件, size: %d\n\n\n",m_pdAnalyzer_value.size());
    SMTimeInfo tm = ii_get_current_mtime();
    char time_buff[10];
    snprintf(time_buff, 10, "%02d%02d%02d", tm.nYear % 100, tm.nMonth, tm.nDay);

    neb::CJsonObject oJson;
    oJson.Add("desc","PreData");
    oJson.Add("date",time_buff);    
    neb::CJsonObject oJson_pdAnalyzer_value;

    it = m_pdAnalyzer_value.begin();        
    for(;it != m_pdAnalyzer_value.end();++it)
    {
        std::string name = it->first;
        float value = it->second;
        oJson_pdAnalyzer_value.Add(name,value);
    }
    //mskprintf("oJson_pdAnalyzer_value:    %s\n\n\n", oJson_pdAnalyzer_value.ToString().c_str());
    oJson.Add("oJson_pdAnalyzer",oJson_pdAnalyzer_value);
/*     neb::CJsonObject obody;
    obody.Add(oJson_pdAnalyzer_value);
    oJson.Add("body",obody); */

 
    neb::CJsonObject oJson_pdAnalyzer_param;
    oJson_pdAnalyzer_param.Add("m_Tol_PhV_Day_phsA",m_Tol_PhV_Day_phsA);
    oJson_pdAnalyzer_param.Add("m_Count_PhV_Day_phsA",m_Count_PhV_Day_phsA);
    oJson_pdAnalyzer_param.Add("m_Tol_PhV_Day_phsB",m_Tol_PhV_Day_phsB);
    oJson_pdAnalyzer_param.Add("m_Count_PhV_Day_phsB",m_Count_PhV_Day_phsB);
    oJson_pdAnalyzer_param.Add("m_Tol_PhV_Day_phsC",m_Tol_PhV_Day_phsC);
    oJson_pdAnalyzer_param.Add("m_Count_PhV_Day_phsC",m_Count_PhV_Day_phsC);

    oJson_pdAnalyzer_param.Add("m_Tol_PhVOfs_Day_phsA",m_Tol_PhVOfs_Day_phsA);
    oJson_pdAnalyzer_param.Add("m_Count_PhVOfs_Day_phsA",m_Count_PhVOfs_Day_phsA);
    oJson_pdAnalyzer_param.Add("m_Tol_PhVOfs_Day_phsB",m_Tol_PhVOfs_Day_phsB);
    oJson_pdAnalyzer_param.Add("m_Count_PhVOfs_Day_phsB",m_Count_PhVOfs_Day_phsB);
    oJson_pdAnalyzer_param.Add("m_Tol_PhVOfs_Day_phsC",m_Tol_PhVOfs_Day_phsC);
    oJson_pdAnalyzer_param.Add("m_Count_PhVOfs_Day_phsC",m_Count_PhVOfs_Day_phsC);

    oJson_pdAnalyzer_param.Add("m_Tol_HzOfs_Day_phsA",m_Tol_HzOfs_Day_phsA);
    oJson_pdAnalyzer_param.Add("m_Count_HzOfs_Day_phsA",m_Count_HzOfs_Day_phsA);
    oJson_pdAnalyzer_param.Add("m_Tol_HzOfs_Day_phsB",m_Tol_HzOfs_Day_phsB);
    oJson_pdAnalyzer_param.Add("m_Count_HzOfs_Day_phsB",m_Count_HzOfs_Day_phsB);
    oJson_pdAnalyzer_param.Add("m_Tol_HzOfs_Day_phsC",m_Tol_HzOfs_Day_phsC);
    oJson_pdAnalyzer_param.Add("m_Count_HzOfs_Day_phsC",m_Count_HzOfs_Day_phsC);


    oJson_pdAnalyzer_param.Add("m_Tol_PhVOfs_Week_phsA",m_Tol_PhVOfs_Week_phsA);
    oJson_pdAnalyzer_param.Add("m_Count_PhVOfs_Week_phsA",m_Count_PhVOfs_Week_phsA);
    oJson_pdAnalyzer_param.Add("m_Tol_PhVOfs_Week_phsB",m_Tol_PhVOfs_Week_phsB);
    oJson_pdAnalyzer_param.Add("m_Count_PhVOfs_Week_phsB",m_Count_PhVOfs_Week_phsB);
    oJson_pdAnalyzer_param.Add("m_Tol_PhVOfs_Week_phsC",m_Tol_PhVOfs_Week_phsC);
    oJson_pdAnalyzer_param.Add("m_Count_PhVOfs_Week_phsC",m_Count_PhVOfs_Week_phsC);

    oJson_pdAnalyzer_param.Add("m_Tol_HzOfs_Week_phsA",m_Tol_HzOfs_Week_phsA);
    oJson_pdAnalyzer_param.Add("m_Count_HzOfs_Week_phsA",m_Count_HzOfs_Week_phsA);
    oJson_pdAnalyzer_param.Add("m_Tol_HzOfs_Week_phsB",m_Tol_HzOfs_Week_phsB);
    oJson_pdAnalyzer_param.Add("m_Count_HzOfs_Week_phsB",m_Count_HzOfs_Week_phsB);
    oJson_pdAnalyzer_param.Add("m_Tol_HzOfs_Week_phsC",m_Tol_HzOfs_Week_phsC);
    oJson_pdAnalyzer_param.Add("m_Count_HzOfs_Week_phsC",m_Count_HzOfs_Week_phsC);

    oJson_pdAnalyzer_param.Add("m_Tol_PhV_Mon_phsA",m_Tol_PhV_Mon_phsA);
    oJson_pdAnalyzer_param.Add("m_Count_PhV_Mon_phsA",m_Count_PhV_Mon_phsA);
    oJson_pdAnalyzer_param.Add("m_Tol_PhV_Mon_phsB",m_Tol_PhV_Mon_phsB);
    oJson_pdAnalyzer_param.Add("m_Count_PhV_Mon_phsB",m_Count_PhV_Mon_phsB);
    oJson_pdAnalyzer_param.Add("m_Tol_PhV_Mon_phsC",m_Tol_PhV_Mon_phsC);
    oJson_pdAnalyzer_param.Add("m_Count_PhV_Mon_phsC",m_Count_PhV_Mon_phsC);

    oJson_pdAnalyzer_param.Add("m_Tol_PhVOfs_Mon_phsA",m_Tol_PhVOfs_Mon_phsA);
    oJson_pdAnalyzer_param.Add("m_Count_PhVOfs_Mon_phsA",m_Count_PhVOfs_Mon_phsA);
    oJson_pdAnalyzer_param.Add("m_Tol_PhVOfs_Mon_phsB",m_Tol_PhVOfs_Mon_phsB);
    oJson_pdAnalyzer_param.Add("m_Count_PhVOfs_Mon_phsB",m_Count_PhVOfs_Mon_phsB);
    oJson_pdAnalyzer_param.Add("m_Tol_PhVOfs_Mon_phsC",m_Tol_PhVOfs_Mon_phsC);
    oJson_pdAnalyzer_param.Add("m_Count_PhVOfs_Mon_phsC",m_Count_PhVOfs_Mon_phsC);

    oJson_pdAnalyzer_param.Add("m_Tol_HzOfs_Mon_phsA",m_Tol_HzOfs_Mon_phsA);
    oJson_pdAnalyzer_param.Add("m_Count_HzOfs_Mon_phsA",m_Count_HzOfs_Mon_phsA);
    oJson_pdAnalyzer_param.Add("m_Tol_HzOfs_Mon_phsB",m_Tol_HzOfs_Mon_phsB);
    oJson_pdAnalyzer_param.Add("m_Count_HzOfs_Mon_phsB",m_Count_HzOfs_Mon_phsB);
    oJson_pdAnalyzer_param.Add("m_Tol_HzOfs_Mon_phsC",m_Tol_HzOfs_Mon_phsC);
    oJson_pdAnalyzer_param.Add("m_Count_HzOfs_Mon_phsC",m_Count_HzOfs_Mon_phsC); 


/*     neb::CJsonObject obodyParam;
    obodyParam.Add(oJson_pdAnalyzer_param);    
    oJson.Add("pdParam",obodyParam); */

    oJson.Add("pdParam",oJson_pdAnalyzer_param);

	neb::CJsonObject oJson_ocAnalyzer_param;
    oJson_ocAnalyzer_param.Add("m_LoadRate",m_LoadRate);//配变负载率
    oJson_ocAnalyzer_param.Add("m_LoadRate_phsA",m_LoadRate_phsA);       //配变负载率A
    oJson_ocAnalyzer_param.Add("m_LoadRate_phsB",m_LoadRate_phsB);       //配变负载率B
    oJson_ocAnalyzer_param.Add("m_LoadRate_phsC",m_LoadRate_phsC);      //配变负载率C

    oJson_ocAnalyzer_param.Add("m_MaxLoadRate",m_MaxLoadRate);//周期内配变负载率
    oJson_ocAnalyzer_param.Add("m_MaxLoadRate_phsA",m_MaxLoadRate_phsA);//周期内配变负载率A
    oJson_ocAnalyzer_param.Add("m_MaxLoadRate_phsB",m_MaxLoadRate_phsB);;    //周期内配变负载率B
    oJson_ocAnalyzer_param.Add("m_MaxLoadRate_phsC",m_MaxLoadRate_phsC);;    //周期内配变负载率C

    oJson_ocAnalyzer_param.Add("m_MaxLoad",m_MaxLoad);  //周期内最大负荷
    oJson_ocAnalyzer_param.Add("m_MaxLoad_phsA",m_MaxLoad_phsA);  //周期内最大负荷a
    oJson_ocAnalyzer_param.Add("m_MaxLoad_phsB",m_MaxLoad_phsB);   //周期内最大负荷b
    oJson_ocAnalyzer_param.Add("m_MaxLoad_phsC",m_MaxLoad_phsC);    //周期内最大负荷c

    oJson_ocAnalyzer_param.Add("m_TotResLoad",m_TotResLoad);    //可开放容量
    oJson_ocAnalyzer_param.Add("m_ResLoad_phsA",m_ResLoad_phsA);  //可开放容量A  //m_ResLoad_phsA  m_ocAnalyzer_phsA
    oJson_ocAnalyzer_param.Add("m_ResLoad_phsB",m_ResLoad_phsB); //可开放容量B
    oJson_ocAnalyzer_param.Add("m_ResLoad_phsC",m_ResLoad_phsC); //可开放容量C

    oJson.Add("ocParam",oJson_ocAnalyzer_param);




    m_databuffer = oJson.ToString();

    CIIFile iiFile(PRE_DATA_INFO_FILEPATH_ABS);
    // 打印写文件内容
    //mskprintf("write:    %s\n\n\n", m_databuffer.c_str());

    int filesize = m_databuffer.size();
    char dataBuf[filesize] = {0};
    m_databuffer.copy(dataBuf, m_databuffer.size(), 0);

    if (iiFile.Open("w+")) // file exist
    {
        if (-1 == iiFile.Write(dataBuf, m_databuffer.size()))
        {
            // 写文件失败，bak改回
            IEC_LOG_RECORD(eErrType, "更新数据文件失败.");
            iiFile.Close();
            return false;
        }
        else
        {
            // 删除bak文件
            iiFile.Close();
            //IEC_LOG_RECORD(eRunType, "更新数据文件成功.");
            return true;
        }
    }
    return false;    
}

bool CParamManager::ReadModelFile()
{
    CTaskManager::CreateInstance().m_dataObj.clear();
    std::list<CFileObj>::iterator iter = m_FileObjs.begin();
    for (; iter != m_FileObjs.end(); ++iter)
    {
        CFileObj obj = *iter;
        std::string filename = IEC_CFG_PATH + obj.m_file;
        CIIFile iiFile;
        iiFile.SetFileName(filename.c_str());
        int filesize = iiFile.GetLength();
        mskprintf("filesize == (%d).\n", filesize);
        if (filesize == 0)
        {
            IEC_LOG_RECORD(eErrType, "file read %s filesize = %d.", filename.c_str(), filesize);
            return false;
        }
        Jchar dataBuf[filesize] = {0};

        if (iiFile.Open("r+")) // file exist
        {
            Jboolean bRead = iiFile.Read(dataBuf, filesize);
            if (!bRead)
            {
                IEC_LOG_RECORD(eErrType, "file read %s failed.", filename.c_str());
                return false;
            }
        }
        iiFile.Close();
        mskprintf("dataBuf == (%s).\n", dataBuf);
        neb::CJsonObject oJson;
        if (!oJson.Parse(dataBuf))
        {
            IEC_LOG_RECORD(eErrType, "json_loadb real_data.msg_send failed.");
            return false;
        }

        std::list<guid_body_s>::iterator it = obj.m_devs.begin();
        for (; it != obj.m_devs.end(); ++it)
        {
            guid_body_s guid_obj = *it;
            CDataObj *dataObj = NULL;
            dataObj = new CDataObj();
            oJson.Get("deviceType", dataObj->m_deviceType);
            oJson.Get("manufacturerId", dataObj->m_manufacturerId);
            oJson.Get("manufacturerName", dataObj->m_manufacturerName);
            oJson.Get("protocolType", dataObj->m_protocolType);
            oJson.Get("model", dataObj->m_model);

            dataObj->m_topic = obj.m_Topic + guid_obj.guid;
            dataObj->m_guid = guid_obj;
            mskprintf("ReadModelFile deviceType:%s.\n", dataObj->m_deviceType.c_str());
            mskprintf("ReadModelFile manufacturerId:%s.\n", dataObj->m_manufacturerId.c_str());
            mskprintf("ReadModelFile protocolType:%s.\n", dataObj->m_protocolType.c_str());
            mskprintf("ReadModelFile manufacturerName:%s.\n", dataObj->m_manufacturerName.c_str());
            mskprintf("ReadModelFile model:%s.\n", dataObj->m_model.c_str());
            mskprintf("ReadModelFile topic:%s.\n", dataObj->m_topic.c_str());
            mskprintf("ReadModelFile m_guid:%s.\n", guid_obj.guid.c_str());
            mskprintf("ReadModelFile dev:%s.\n", guid_obj.dev.c_str());

            neb::CJsonObject obj_services;
            if (!oJson.Get("services", obj_services))
            {
                IEC_LOG_RECORD(eErrType, "file: %s read services object failed.", obj.m_file.c_str());
                return false;
            }

            if (!obj_services.IsArray())
            {
                IEC_LOG_RECORD(eErrType, "file: %s read services object  object failed. not array.", obj.m_file.c_str());
                return false;
            }

            int servicesSize = obj_services.GetArraySize();
            for (int i = 0; i < servicesSize; i++)
            {
                std::string serviceId;
                neb::CJsonObject service_obj;
                if (obj_services.Get(i, service_obj))
                {
                    service_obj.Get("serviceId", serviceId);
                    mskprintf("ReadModelFile serviceId:%s.\n", serviceId.c_str());
                    neb::CJsonObject obj_properties;
                    if (!service_obj.Get("properties", obj_properties))
                    {
                        IEC_LOG_RECORD(eErrType, "file: %s read properties object failed.", obj.m_file.c_str());
                        return false;
                    }

                    if (!obj_properties.IsArray())
                    {
                        IEC_LOG_RECORD(eErrType, "file: %s read properties object  object failed. not array.", obj.m_file.c_str());
                        return false;
                    }

                    int propertieSize = obj_properties.GetArraySize();
                    CNameObj name_obj;
                    CNameObj Sendname_obj;
                    name_obj.m_names.clear();
                    Juint32 j = 0;
                    for (int i = 0; i < propertieSize; i++)
                    {
                        std::string name;
                        neb::CJsonObject propertie_obj;
                        if (obj_properties.Get(i, propertie_obj))
                        {
                            propertie_obj.Get("name", name);
                            mskprintf("ReadModelFile name:%s.\n", name.c_str());
                            name_obj.m_names.push_back(name);
                            Sendname_obj.m_names.push_back(name);
                            if (j > m_sendNum)
                            {
                                Sendname_obj.m_serviceName = serviceId;
                                dataObj->m_SendNameObjs.push_back(Sendname_obj);
                                Sendname_obj.m_names.clear();
                                j = 0;
                            }
                            else if (i == (propertieSize - 1))
                            {
                                Sendname_obj.m_serviceName = serviceId;
                                dataObj->m_SendNameObjs.push_back(Sendname_obj);
                                Sendname_obj.m_names.clear();
                            }
                        }
                        j++;
                    }
                    name_obj.m_serviceName = serviceId;
                    dataObj->m_serviceIds[serviceId] = name_obj;
                }
            }
            CTaskManager::CreateInstance().m_dataObj.push_back(dataObj);
        }
    }

    return true;
}

std::string CParamManager::GetFirsAppName(std::string topic)
{
    std::string str;

    size_t index = topic.find_first_of("/", 0);
    if (index != std::string::npos)
    {
        str = topic.substr(0, index);
    }
    return str;
}

std::string CParamManager::CreatMd5(std::string str)
{
    MD5_CTX md5;
    std::string out;
    unsigned char md[16] = {0};

    if (!MD5_Init(&md5))
    {
        mskprintf("MD5_Init error\n");
        return "";
    }
    if (!MD5_Update(&md5, str.c_str(), strlen(str.c_str())))
    {
        mskprintf("MD5_Update error\n");
        return "";
    }
    if (!MD5_Final(md, &md5))
    {
        mskprintf("MD5_Final error\n");
        return "";
    }
    for (int i = 0; i < 16; i++)
    {
        char outc[3] = {0};
        sprintf(outc, "%02X", md[i]);
        std::string outb = outc;
        out = out + outb;
    }
    return out;
}


int CParamManager::SetConstParam(std::string paramname, float paramval)
{   
    CIIAutoMutex mutex(&m_cs);
    //定值进行保存

    if( paramname.compare("PowOff_Enable")   == 0 || paramname.compare("PowerOff_V_Lim") == 0 || paramname.compare("PowerOff_Dly") == 0       //失压停电
        || paramname.compare("PowOn_Enable") == 0 || paramname.compare("PowerOn_V_Lim") == 0  || paramname.compare("PowerOn_Dly") == 0        //有压来电
        || paramname.compare("PTUA_Enable")  == 0 || paramname.compare("A_Zero_Drift") == 0   || paramname.compare("PTUA_Dly") == 0 || paramname.compare("PTUA_Loss_Dly") == 0   //失流-断流
        || paramname.compare("PTUV_Enable")  == 0 || paramname.compare("PTUV_Lim") == 0       || paramname.compare("PTUV_Dly") == 0  //低压
        || paramname.compare("PTOV_Enable")  == 0 || paramname.compare("PTOV_Lim") == 0       || paramname.compare("PTOV_Dly") == 0  //过压
        || paramname.compare("Load_Enable")  == 0 || paramname.compare("Load_Lim") == 0       || paramname.compare("Load_Dly") == 0  //负荷越限        
        || paramname.compare("PTOC_Hvld_Enable")  == 0  || paramname.compare("PTOC_Hvld_Lim") == 0||paramname.compare("PTOC_Hvld_Dly") == 0 //重载
        || paramname.compare("PTOC_Ovld_Enable")  == 0  || paramname.compare("PTOC_Ovld_Lim") == 0||paramname.compare("PTOC_Ovld_Dly") == 0 //过载
        || paramname.compare("Seqc0OV_Enable") == 0 || paramname.compare("Seqc0OV_Lim")  == 0 || paramname.compare("Seqc0OV_Dly")  == 0     //0序过压
        || paramname.compare("Seqc0OC_Enable") == 0 || paramname.compare("Seqc0OC_Lim")  == 0 || paramname.compare("Seqc0OC_Dly")  == 0     //0序过流
        || paramname.compare("ResA_Enable") == 0 || paramname.compare("ResA_Lim")  == 0 || paramname.compare("ResA_Dly")  == 0     //剩余电流
        || paramname.compare("PTUPF_Enable") == 0 || paramname.compare("PTUPF_Lim")  == 0 || paramname.compare("PTUPF_Dly")  == 0     //功率因数
        || paramname.compare("ImbA_Enable") == 0  ||paramname.compare("Imb_db") == 0 || paramname.compare("ImbA_Lim")  == 0  //负荷不平衡-相电流-零线
        || paramname.compare("ImbNgA_phsN_Lim")  == 0||paramname.compare("ImbA_Dly")  == 0                                   //负荷不平衡-相电流-零线
        || paramname.compare("Load") == 0   //配变容量
        || paramname.compare("DeviceLoad") == 0
        || paramname.compare("RtgA") == 0   //配电变压器额定电流 
        || paramname.compare("VRtg") == 0  || paramname.compare("VRtgSnd") == 0  || paramname.compare("ARtg") == 0  || paramname.compare("ARtgSnd") == 0     //pt ct
        || paramname.compare("ARtg_Seqc0") == 0 ||paramname.compare("ARtgSnd_Seqc0") == 0  //零线CT额定
        || paramname.compare("LoadRate_CalcCyc") == 0   //配变负载率计算周期
		|| paramname.compare("ResLoad_CalcCyc") == 0    //可开放容量计算周期
        || paramname.compare("PTUV_Open_V_Lim")  == 0|| paramname.compare("PTUV_Open_V_Lim") == 0      //电压不平衡 ， 电荷不平衡
        || paramname.compare("PTUV_Open_Dly")  == 0|| paramname.compare("PTUV_Open_Dly") == 0 ||  paramname.compare("PhvImb_strVal") == 0   //电压不平衡 ， 电荷不平衡
        //暂降DipStrVal_Enable  DipStrVal_Lim  DipStrVal_Dly 暂升SwlStrVal_Enable 
        || paramname.compare("DIP_Enable")  == 0|| paramname.compare("DIP_strVal_Lim") == 0 ||  paramname.compare("DIP_Dly") == 0//电压暂降
        || paramname.compare("SWL_Enable")  == 0|| paramname.compare("SWL_strVal_Lim") == 0 ||  paramname.compare("SWL_Dly") == 0//电压暂升
        || paramname.compare("INTR_Enable")  == 0|| paramname.compare("INTR_strVal_Lim") == 0 ||  paramname.compare("INTR_Dly") == 0//电压暂升
        || paramname.compare("PTOV_lim_setVal")==0||paramname.compare("PTUV_lim_setVal")==0
        || paramname.compare("ThdPhV_Op_V_Enable") == 0 || paramname.compare("ThdPhV_Op_V_Lim") == 0 || paramname.compare("ThdPhV_Op_V_Dly") == 0//电压谐波越限
        || paramname.compare("ThdA_Op_A_Enable") == 0 || paramname.compare("ThdA_Op_A_Lim") == 0 || paramname.compare("ThdA_Op_A_Dly") == 0 //电流谐波越限 
        || paramname.compare("PTOF_Enable") == 0 || paramname.compare("PTOF_Lim") == 0 || paramname.compare("PTOF_Dly") == 0  //过频
        || paramname.compare("PTUF_Enable") == 0 || paramname.compare("PTUF_Lim") == 0 || paramname.compare("PTUF_Dly") == 0  //欠频
        || paramname.compare("PTUC_Unld_Enable") == 0 || paramname.compare("PTUC_Unld_Lim") == 0 || paramname.compare("PTUC_Unld_Dly") == 0 //空载
        || paramname.compare("PTUC_Lvld_Enable") == 0 || paramname.compare("PTUC_Lvld_Lim") == 0 || paramname.compare("PTUC_Lvld_Dly") == 0 //轻载
        || paramname.compare("PTOC_mild_Ovld_Enable") == 0 || paramname.compare("PTOC_mild_Ovld_Lim") == 0 || paramname.compare("PTOC_mild_Ovld_Dly") == 0 //轻度过载
        || paramname.compare("PTOC_middle_Ovld_Enable") == 0 || paramname.compare("PTOC_middle_Ovld_Lim") == 0 || paramname.compare("PTOC_middle_Ovld_Dly") == 0 //中度过载
        || paramname.compare("PTOC_severe_Ovld_Enable") == 0 || paramname.compare("PTOC_severe_Ovld_Lim") == 0 || paramname.compare("PTOC_severe_Ovld_Dly") == 0 //重度过载
        || paramname.compare("PTOC_abnormal_Ovld_Enable") == 0 || paramname.compare("PTOC_abnormal_Ovld_Lim") == 0 || paramname.compare("PTOC_abnormal_Ovld_Dly") == 0 //异常过载
    )	 
    {
        m_obj_body_para.Replace(paramname,paramval);
        m_Param_value[paramname] = paramval; 
       // mskprintf("set param name:%s val:%f  \n",paramname.c_str(),paramval);
        IEC_LOG_RECORD(eErrType, "set param name:%s val:%f  \n",paramname.c_str(),paramval);
    }

    if(paramname.compare("ImbA_Dly") == 0)
    {
        mskprintf("&&&&&  ImbA_Dly:%f\n",m_Param_value["ImbA_Dly"]);
        m_T.SetParam(1000*m_Param_value["ImbA_Dly"]);
		m_T_Imb_YX.SetParam(1000*m_Param_value["ImbA_Dly"]);
        m_T_ZImb_YX.SetParam(1000*m_Param_value["ImbA_Dly"]);
        m_T_Imb_mild_YX.SetParam(1000*m_Param_value["ImbA_Dly"]);
        m_T_Imb_middle_YX.SetParam(1000*m_Param_value["ImbA_Dly"]);
        m_T_Imb_severe_YX.SetParam(1000*m_Param_value["ImbA_Dly"]);
	}
    if(paramname.compare("ImbA_Enable") == 0)
    {
        mskprintf("&&&&&  ImbA_Enable:%d\n",m_Param_value["ImbA_Enable"]);
    }

    return 1;    
}

int CParamManager::SetAdcData(std::string paramname, float paramval)
{
    CIIAutoMutex mutex(&m_cs);
    //交采更新
    //m_obj_body_adc.Replace(paramname,paramval);
    m_ADC_value[paramname] = paramval; 

    return 1;
}

// 计算电能质量分析
bool CParamManager::Unpack_pdAnalyzerData()
{
    CIIAutoMutex mutex(&m_cs);

    mskprintf("1分钟，计算电能质量分析\n");
    mskprintf("交采原始数据\n");

    std::map<std::string,float>::iterator it = m_pdAnalyzer_value.begin();        
    for(;it != m_pdAnalyzer_value.end();++it)
    {
        std::string name = it->first;
        float value = it->second;
        //mskprintf("@@NAME: %s, value: %f\n",name.c_str(),value);
    }


    // 打印变量信息
    std::map<std::string, float>::iterator iter;
    for (iter = CParamManager::CreateInstance().m_ADC_value.begin(); iter != CParamManager::CreateInstance().m_ADC_value.end(); iter++)
    {
        mskprintf("name =  %s , value = %f\n", iter->first.c_str(), iter->second);
    } 

    //时间更新
    m_voltage_time.PhV_Tm_Day_phsA++;   //当日检测时间
    m_voltage_time.PhV_Tm_Day_phsB++;   //当日检测时间
    m_voltage_time.PhV_Tm_Day_phsC++;   //当日检测时间
    m_voltage_time.PhV_Tm_Mon_phsA++;   //当月检测时间
    m_voltage_time.PhV_Tm_Mon_phsB++;   //当月检测时间
    m_voltage_time.PhV_Tm_Mon_phsC++;   //当月检测时间 

    Unpack_pdAnalyzerImb();//不平衡度

    // A电压超上限时间
    if(m_voltage_time.PTOV_Tm_phsA_S >= SEC_PER_MIN)
    {
        m_voltage_time.PTOV_Tm_Day_phsA++;
        m_voltage_time.PTOV_Tm_Mon_phsA++;
    } 
    else if(m_voltage_time.PTUV_Tm_phsA_S >= SEC_PER_MIN)
    {
        m_voltage_time.PTUV_Tm_Day_phsA++;
        m_voltage_time.PTUV_Tm_Mon_phsA++;
    }
    else
    {
        m_voltage_time.PASS_Tm_Day_phsA++;
        m_voltage_time.PASS_Tm_Mon_phsA++;
    }

    //mskprintf("PTOV_Tm_Day_phsA(%f),PTUV_Tm_Day_phsA(%f),PASS_Tm_Day_phsA(%f),PhV_Tm_Day_phsA(%f)\n",
    //m_voltage_time.PTOV_Tm_Day_phsA,m_voltage_time.PTUV_Tm_Day_phsA,m_voltage_time.PASS_Tm_Day_phsA,m_voltage_time.PhV_Tm_Day_phsA);

    m_voltage_time.PTOV_Tm_phsA_S = 0;
    m_voltage_time.PTUV_Tm_phsA_S = 0;
    m_voltage_time.PASS_Tm_phsA_S = 0;


    // B电压超上限时间
    if(m_voltage_time.PTOV_Tm_phsB_S >= SEC_PER_MIN)
    {
        m_voltage_time.PTOV_Tm_Day_phsB++;
        m_voltage_time.PTOV_Tm_Mon_phsB++;
    } 
    else if(m_voltage_time.PTUV_Tm_phsB_S >= SEC_PER_MIN)
    {
        m_voltage_time.PTUV_Tm_Day_phsB++;
        m_voltage_time.PTUV_Tm_Mon_phsB++;
    }
    else
    {
        m_voltage_time.PASS_Tm_Day_phsB++;
        m_voltage_time.PASS_Tm_Mon_phsB++;
    }
    m_voltage_time.PTOV_Tm_phsB_S = 0;
    m_voltage_time.PTUV_Tm_phsB_S = 0;
    m_voltage_time.PASS_Tm_phsB_S = 0;

    // C电压超上限时间
    if(m_voltage_time.PTOV_Tm_phsC_S >= SEC_PER_MIN)
    {
        m_voltage_time.PTOV_Tm_Day_phsC++;
        m_voltage_time.PTOV_Tm_Mon_phsC++;
    } 
    else if(m_voltage_time.PTUV_Tm_phsC_S >= SEC_PER_MIN)
    {
        m_voltage_time.PTUV_Tm_Day_phsC++;
        m_voltage_time.PTUV_Tm_Mon_phsC++;
    }
    else
    {
        m_voltage_time.PASS_Tm_Day_phsC++;
        m_voltage_time.PASS_Tm_Mon_phsC++;
    }
    m_voltage_time.PTOV_Tm_phsC_S = 0;
    m_voltage_time.PTUV_Tm_phsC_S = 0;
    m_voltage_time.PASS_Tm_phsC_S = 0;

    //电压越上限日累计时间
    m_pdAnalyzer_value["PTOV_Tm_Day_phsA"] = m_voltage_time.PTOV_Tm_Day_phsA;
    m_pdAnalyzer_value["PTUV_Tm_Day_phsA"] = m_voltage_time.PTUV_Tm_Day_phsA;
    m_pdAnalyzer_value["PTOV_Tm_Day_phsB"] = m_voltage_time.PTOV_Tm_Day_phsB;
    m_pdAnalyzer_value["PTUV_Tm_Day_phsB"] = m_voltage_time.PTUV_Tm_Day_phsB;
    m_pdAnalyzer_value["PTOV_Tm_Day_phsC"] = m_voltage_time.PTOV_Tm_Day_phsC;
    m_pdAnalyzer_value["PTUV_Tm_Day_phsC"] = m_voltage_time.PTUV_Tm_Day_phsC;
    mskprintf("PTOV_Tm_Day_phsA:%f PTUV_Tm_Day_phsA:%f\n",m_pdAnalyzer_value["PTOV_Tm_Day_phsA"],m_pdAnalyzer_value["PTUV_Tm_Day_phsA"]);
    mskprintf("PTOV_Tm_Day_phsB:%f PTUV_Tm_Day_phsB:%f\n",m_pdAnalyzer_value["PTOV_Tm_Day_phsB"],m_pdAnalyzer_value["PTUV_Tm_Day_phsB"]);
    mskprintf("PTOV_Tm_Day_phsC:%f PTUV_Tm_Day_phsC:%f\n",m_pdAnalyzer_value["PTOV_Tm_Day_phsC"],m_pdAnalyzer_value["PTUV_Tm_Day_phsC"]);
    //电压越下限日累计时间
    m_pdAnalyzer_value["PTOV_Tm_Mon_phsA"] = m_voltage_time.PTOV_Tm_Mon_phsA;
    m_pdAnalyzer_value["PTUV_Tm_Mon_phsA"] = m_voltage_time.PTUV_Tm_Mon_phsA;
    m_pdAnalyzer_value["PTOV_Tm_Mon_phsB"] = m_voltage_time.PTOV_Tm_Mon_phsB;
    m_pdAnalyzer_value["PTUV_Tm_Mon_phsB"] = m_voltage_time.PTUV_Tm_Mon_phsB;
    m_pdAnalyzer_value["PTOV_Tm_Mon_phsC"] = m_voltage_time.PTOV_Tm_Mon_phsC;
    m_pdAnalyzer_value["PTUV_Tm_Mon_phsC"] = m_voltage_time.PTUV_Tm_Mon_phsC;
    mskprintf("PTOV_Tm_Mon_phsA:%f PTUV_Tm_Mon_phsA:%f\n",m_pdAnalyzer_value["PTOV_Tm_Mon_phsA"],m_pdAnalyzer_value["PTUV_Tm_Mon_phsA"]);
    mskprintf("PTOV_Tm_Mon_phsB:%f PTUV_Tm_Mon_phsB:%f\n",m_pdAnalyzer_value["PTOV_Tm_Mon_phsB"],m_pdAnalyzer_value["PTUV_Tm_Mon_phsB"]);
    mskprintf("PTOV_Tm_Mon_phsC:%f PTUV_Tm_Mon_phsC:%f\n",m_pdAnalyzer_value["PTOV_Tm_Mon_phsC"],m_pdAnalyzer_value["PTUV_Tm_Mon_phsC"]);

    //计算合格率A
    if((m_voltage_time.PhV_Tm_Day_phsA > 0) && (m_voltage_time.PhV_Tm_Mon_phsA > 0))//计算合格率 
    {
        m_pdAnalyzer_value["PTOV_Rate_Day_phsA"] = 100.0 * m_voltage_time.PTOV_Tm_Day_phsA / m_voltage_time.PhV_Tm_Day_phsA;
        m_pdAnalyzer_value["PTOV_Rate_Mon_phsA"] = 100.0 * m_voltage_time.PTOV_Tm_Mon_phsA / m_voltage_time.PhV_Tm_Mon_phsA ;
        m_pdAnalyzer_value["PTUV_Rate_Day_phsA"] = 100.0 * m_voltage_time.PTUV_Tm_Day_phsA / m_voltage_time.PhV_Tm_Day_phsA ;
        m_pdAnalyzer_value["PTUV_Rate_Mon_phsA"] = 100.0 * m_voltage_time.PTUV_Tm_Mon_phsA / m_voltage_time.PhV_Tm_Mon_phsA ;
        m_pdAnalyzer_value["PhVPassDay_phsA"] = 100.0 * m_voltage_time.PASS_Tm_Day_phsA / m_voltage_time.PhV_Tm_Day_phsA ;
        m_pdAnalyzer_value["PhVPassMon_phsA"] = 100.0 * m_voltage_time.PASS_Tm_Mon_phsA / m_voltage_time.PhV_Tm_Mon_phsA ;

        mskprintf("m_pdAnalyzer_value[PhVPassDay_phsA] = %f\n",m_pdAnalyzer_value["PhVPassDay_phsA"]);
        mskprintf("m_pdAnalyzer_value[PhVPassMon_phsA] = %f\n",m_pdAnalyzer_value["PhVPassMon_phsA"]);


    }
    //计算合格率B
    if((m_voltage_time.PhV_Tm_Day_phsB > 0) && (m_voltage_time.PhV_Tm_Mon_phsB > 0))//计算合格率 
    {
        m_pdAnalyzer_value["PTOV_Rate_Day_phsB"] = 100.0 * m_voltage_time.PTOV_Tm_Day_phsB / m_voltage_time.PhV_Tm_Day_phsB ;
        m_pdAnalyzer_value["PTOV_Rate_Mon_phsB"] = 100.0 * m_voltage_time.PTOV_Tm_Mon_phsB / m_voltage_time.PhV_Tm_Mon_phsB ;
        m_pdAnalyzer_value["PTUV_Rate_Day_phsB"] = 100.0 * m_voltage_time.PTUV_Tm_Day_phsB / m_voltage_time.PhV_Tm_Day_phsB ;
        m_pdAnalyzer_value["PTUV_Rate_Mon_phsB"] = 100.0 * m_voltage_time.PTUV_Tm_Mon_phsB / m_voltage_time.PhV_Tm_Mon_phsB ;
        m_pdAnalyzer_value["PhVPassDay_phsB"] = 100.0 * m_voltage_time.PASS_Tm_Day_phsB / m_voltage_time.PhV_Tm_Day_phsB ;
        m_pdAnalyzer_value["PhVPassMon_phsB"] = 100.0 * m_voltage_time.PASS_Tm_Mon_phsB / m_voltage_time.PhV_Tm_Mon_phsB ;
        mskprintf("m_pdAnalyzer_value[PhVPassDay_phsB] = %f\n",m_pdAnalyzer_value["PhVPassDay_phsB"]);
        mskprintf("m_pdAnalyzer_value[PhVPassMon_phsB] = %f\n",m_pdAnalyzer_value["PhVPassMon_phsB"]);
    }
    //计算合格率C
    if((m_voltage_time.PhV_Tm_Day_phsC > 0) && (m_voltage_time.PhV_Tm_Mon_phsC > 0))//计算合格率 
    {
        m_pdAnalyzer_value["PTOV_Rate_Day_phsC"] = 100.0 * m_voltage_time.PTOV_Tm_Day_phsC / m_voltage_time.PhV_Tm_Day_phsC ;
        m_pdAnalyzer_value["PTOV_Rate_Mon_phsC"] = 100.0 * m_voltage_time.PTOV_Tm_Mon_phsC / m_voltage_time.PhV_Tm_Mon_phsC ;
        m_pdAnalyzer_value["PTUV_Rate_Day_phsC"] = 100.0 * m_voltage_time.PTUV_Tm_Day_phsC / m_voltage_time.PhV_Tm_Day_phsC ;
        m_pdAnalyzer_value["PTUV_Rate_Mon_phsC"] = 100.0 * m_voltage_time.PTUV_Tm_Mon_phsC / m_voltage_time.PhV_Tm_Mon_phsC ;
        m_pdAnalyzer_value["PhVPassDay_phsC"] = 100.0 * m_voltage_time.PASS_Tm_Day_phsC / m_voltage_time.PhV_Tm_Day_phsC ;
        m_pdAnalyzer_value["PhVPassMon_phsC"] = 100.0 * m_voltage_time.PASS_Tm_Mon_phsC / m_voltage_time.PhV_Tm_Mon_phsC ;
        mskprintf("m_pdAnalyzer_value[PhVPassDay_phsC] = %f\n",m_pdAnalyzer_value["PhVPassDay_phsC"]);
        mskprintf("m_pdAnalyzer_value[PhVPassMon_phsC] = %f\n",m_pdAnalyzer_value["PhVPassMon_phsC"]);
    }
    
    

/*     //实时配变负载率
    float LoadRate_time = m_ADC_value["TotVA"] / m_Param_value["DeviceLoad"];
    m_pdAnalyzer_value["LoadRate_sum"] += LoadRate_time;
    m_pdAnalyzer_value["LoadRate_num"] += 1;
    //配变负载率
    m_pdAnalyzer_value["LoadRate"] = m_pdAnalyzer_value["LoadRate_sum"]/m_pdAnalyzer_value["LoadRate_num"];

    // 周期内最大负荷LoadMax
    if (m_pdAnalyzer_value["LoadMax"] < m_pdAnalyzer_value["LoadRate"])
    {
        m_pdAnalyzer_value["LoadMax"] = m_pdAnalyzer_value["LoadRate"];
    }
 */
    //电压偏差
    m_pdAnalyzer_value["PhVOfs_phsA"]  =  100* (m_ADC_value["PhV_phsA"] - m_Param_value["std_voltage"]) / m_Param_value["std_voltage"];
    m_pdAnalyzer_value["PhVOfs_phsB"]  =  100* (m_ADC_value["PhV_phsB"] - m_Param_value["std_voltage"]) / m_Param_value["std_voltage"];
    m_pdAnalyzer_value["PhVOfs_phsC"]  =  100* (m_ADC_value["PhV_phsC"] - m_Param_value["std_voltage"]) / m_Param_value["std_voltage"];

    //mskprintf("%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%   m_pdAnalyzer_value[PhVOfs_phsB] = %f,(m_ADC_value[PhV_phsB]=(%f), .std_voltage(%f)\n",
    //m_pdAnalyzer_value["PhVOfs_phsB"],m_ADC_value["PhV_phsB"] , .std_voltage);

    //日电压偏差极值A
    if(m_pdAnalyzer_value["PhVOfsMaxDay_phsA"] < m_pdAnalyzer_value["PhVOfs_phsA"])
    {
        m_pdAnalyzer_value["PhVOfsMaxDay_phsA"] = m_pdAnalyzer_value["PhVOfs_phsA"];
    }
    if(m_pdAnalyzer_value["PhVOfsMinDay_phsA"] > m_pdAnalyzer_value["PhVOfs_phsA"])
    {
        m_pdAnalyzer_value["PhVOfsMinDay_phsA"] = m_pdAnalyzer_value["PhVOfs_phsA"];
    }
    //mskprintf("%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%   m_pdAnalyzer_value[PhVOfsMaxDay_phsA] = %f,m_pdAnalyzer_value[PhVOfsMinDay_phsA] = %f\n",
    //m_pdAnalyzer_value["PhVOfsMaxDay_phsA"],m_pdAnalyzer_value["PhVOfsMinDay_phsA"]);


    //日电压偏平均值A
    m_Tol_PhVOfs_Day_phsA += m_pdAnalyzer_value["PhVOfs_phsA"];
    m_Count_PhVOfs_Day_phsA++;
    m_pdAnalyzer_value["PhVOfsAvDay_phsA"] = m_Tol_PhVOfs_Day_phsA/m_Count_PhVOfs_Day_phsA;

    //日电压偏差极值B
    if(m_pdAnalyzer_value["PhVOfsMaxDay_phsB"] < m_pdAnalyzer_value["PhVOfs_phsB"])
    {
        m_pdAnalyzer_value["PhVOfsMaxDay_phsB"] = m_pdAnalyzer_value["PhVOfs_phsB"];
    }
    if(m_pdAnalyzer_value["PhVOfsMinDay_phsB"] > m_pdAnalyzer_value["PhVOfs_phsB"])
    {
        m_pdAnalyzer_value["PhVOfsMinDay_phsB"] = m_pdAnalyzer_value["PhVOfs_phsB"];
    }
    //日电压偏平均值B
    m_Tol_PhVOfs_Day_phsB += m_pdAnalyzer_value["PhVOfs_phsB"];
    m_Count_PhVOfs_Day_phsB++;
    m_pdAnalyzer_value["PhVOfsAvDay_phsB"] = m_Tol_PhVOfs_Day_phsB/m_Count_PhVOfs_Day_phsB;

    //日电压偏差极值C
    if(m_pdAnalyzer_value["PhVOfsMaxDay_phsC"] < m_pdAnalyzer_value["PhVOfs_phsC"])
    {
        m_pdAnalyzer_value["PhVOfsMaxDay_phsC"] = m_pdAnalyzer_value["PhVOfs_phsC"];
    }
    if(m_pdAnalyzer_value["PhVOfsMinDay_phsC"] > m_pdAnalyzer_value["PhVOfs_phsC"])
    {
        m_pdAnalyzer_value["PhVOfsMinDay_phsC"] = m_pdAnalyzer_value["PhVOfs_phsC"];
    }
    //日电压偏平均值C
    m_Tol_PhVOfs_Day_phsC += m_pdAnalyzer_value["PhVOfs_phsC"];
    m_Count_PhVOfs_Day_phsC++;
    m_pdAnalyzer_value["PhVOfsAvDay_phsC"] = m_Tol_PhVOfs_Day_phsC/m_Count_PhVOfs_Day_phsC;

    //周电压偏差极值
    //周电压偏差极值A
    if(m_pdAnalyzer_value["PhVOfsMaxWeek_phsA"] < m_pdAnalyzer_value["PhVOfs_phsA"])
    {
        m_pdAnalyzer_value["PhVOfsMaxWeek_phsA"] = m_pdAnalyzer_value["PhVOfs_phsA"];
    }
    if(m_pdAnalyzer_value["PhVOfsMinWeek_phsA"] > m_pdAnalyzer_value["PhVOfs_phsA"])
    {
        m_pdAnalyzer_value["PhVOfsMinWeek_phsA"] = m_pdAnalyzer_value["PhVOfs_phsA"];
    }
    //周电压偏平均值A
    m_Tol_PhVOfs_Week_phsA += m_pdAnalyzer_value["PhVOfs_phsA"];
    m_Count_PhVOfs_Week_phsA++;
    m_pdAnalyzer_value["PhVOfsAvWeek_phsA"] = m_Tol_PhVOfs_Week_phsA/m_Count_PhVOfs_Week_phsA;

    //周电压偏差极值B
    if(m_pdAnalyzer_value["PhVOfsMaxWeek_phsB"] < m_pdAnalyzer_value["PhVOfs_phsB"])
    {
        m_pdAnalyzer_value["PhVOfsMaxWeek_phsB"] = m_pdAnalyzer_value["PhVOfs_phsB"];
    }
    if(m_pdAnalyzer_value["PhVOfsMinWeek_phsB"] > m_pdAnalyzer_value["PhVOfs_phsB"])
    {
        m_pdAnalyzer_value["PhVOfsMinWeek_phsB"] = m_pdAnalyzer_value["PhVOfs_phsB"];
    }
    //周电压偏平均值B
    m_Tol_PhVOfs_Week_phsB += m_pdAnalyzer_value["PhVOfs_phsB"];
    m_Count_PhVOfs_Week_phsB++;
    m_pdAnalyzer_value["PhVOfsAvWeek_phsB"] = m_Tol_PhVOfs_Week_phsB/m_Count_PhVOfs_Week_phsB;

    //周电压偏差极值C
    if(m_pdAnalyzer_value["PhVOfsMaxWeek_phsC"] < m_pdAnalyzer_value["PhVOfs_phsC"])
    {
        m_pdAnalyzer_value["PhVOfsMaxWeek_phsC"] = m_pdAnalyzer_value["PhVOfs_phsC"];
    }
    if(m_pdAnalyzer_value["PhVOfsMinWeek_phsC"] > m_pdAnalyzer_value["PhVOfs_phsC"])
    {
        m_pdAnalyzer_value["PhVOfsMinWeek_phsC"] = m_pdAnalyzer_value["PhVOfs_phsC"];
    }
    //周电压偏平均值C
    m_Tol_PhVOfs_Week_phsC += m_pdAnalyzer_value["PhVOfs_phsC"];
    m_Count_PhVOfs_Week_phsC++;
    m_pdAnalyzer_value["PhVOfsAvWeek_phsC"] = m_Tol_PhVOfs_Week_phsC/m_Count_PhVOfs_Week_phsC;

    //月电压偏差极值
    //月电压偏差极值A
    if(m_pdAnalyzer_value["PhVOfsMaxMon_phsA"] < m_pdAnalyzer_value["PhVOfs_phsA"])
    {
        m_pdAnalyzer_value["PhVOfsMaxMon_phsA"] = m_pdAnalyzer_value["PhVOfs_phsA"];
    }
    if(m_pdAnalyzer_value["PhVOfsMinMon_phsA"] > m_pdAnalyzer_value["PhVOfs_phsA"])
    {
        m_pdAnalyzer_value["PhVOfsMinMon_phsA"] = m_pdAnalyzer_value["PhVOfs_phsA"];
    }
    //Mon电压偏平均值A
    m_Tol_PhVOfs_Mon_phsA += m_pdAnalyzer_value["PhVOfs_phsA"];
    m_Count_PhVOfs_Mon_phsA++;
    m_pdAnalyzer_value["PhVOfsAvMon_phsA"] = m_Tol_PhVOfs_Mon_phsA/m_Count_PhVOfs_Mon_phsA;

    //Mon电压偏差极值B
    if(m_pdAnalyzer_value["PhVOfsMaxMon_phsB"] < m_pdAnalyzer_value["PhVOfs_phsB"])
    {
        m_pdAnalyzer_value["PhVOfsMaxMon_phsB"] = m_pdAnalyzer_value["PhVOfs_phsB"];
    }
    if(m_pdAnalyzer_value["PhVOfsMinMon_phsB"] > m_pdAnalyzer_value["PhVOfs_phsB"])
    {
        m_pdAnalyzer_value["PhVOfsMinMon_phsB"] = m_pdAnalyzer_value["PhVOfs_phsB"];
    }
    //Mon电压偏平均值B
    m_Tol_PhVOfs_Mon_phsB += m_pdAnalyzer_value["PhVOfs_phsB"];
    m_Count_PhVOfs_Mon_phsB++;
    m_pdAnalyzer_value["PhVOfsAvMon_phsB"] = m_Tol_PhVOfs_Mon_phsB/m_Count_PhVOfs_Mon_phsB;

    //Mon电压偏差极值C
    if(m_pdAnalyzer_value["PhVOfsMaxMon_phsC"] < m_pdAnalyzer_value["PhVOfs_phsC"])
    {
        m_pdAnalyzer_value["PhVOfsMaxMon_phsC"] = m_pdAnalyzer_value["PhVOfs_phsC"];
    }
    if(m_pdAnalyzer_value["PhVOfsMinMon_phsC"] > m_pdAnalyzer_value["PhVOfs_phsC"])
    {
        m_pdAnalyzer_value["PhVOfsMinMon_phsC"] = m_pdAnalyzer_value["PhVOfs_phsC"];
    }
    //Mon电压偏平均值C
    m_Tol_PhVOfs_Mon_phsC += m_pdAnalyzer_value["PhVOfs_phsC"];
    m_Count_PhVOfs_Mon_phsC++;
    m_pdAnalyzer_value["PhVOfsAvMon_phsC"] = m_Tol_PhVOfs_Mon_phsC/m_Count_PhVOfs_Mon_phsC;

    //频率偏差
    m_pdAnalyzer_value["HzOfs_phsA"]  =  100.0* (m_ADC_value["Hz_phsA"] - m_Param_value["std_Hz"]) / m_Param_value["std_Hz"];
    m_pdAnalyzer_value["HzOfs_phsB"]  =  100.0* (m_ADC_value["Hz_phsB"] - m_Param_value["std_Hz"]) / m_Param_value["std_Hz"];
    m_pdAnalyzer_value["HzOfs_phsC"]  =  100.0* (m_ADC_value["Hz_phsC"] - m_Param_value["std_Hz"]) / m_Param_value["std_Hz"];
    m_pdAnalyzer_value["TotHzOfs"]    =  100.0* (m_ADC_value["Hz"]      - m_Param_value["std_Hz"]) / m_Param_value["std_Hz"];
    
    //日频率偏差极值 A    
    if(m_pdAnalyzer_value["HzOfsMaxDay_phsA"] < m_pdAnalyzer_value["HzOfs_phsA"])
    {
        m_pdAnalyzer_value["HzOfsMaxDay_phsA"] = m_pdAnalyzer_value["HzOfs_phsA"];
    }
    if(m_pdAnalyzer_value["HzOfsMinDay_phsA"] > m_pdAnalyzer_value["HzOfs_phsA"])
    {
        m_pdAnalyzer_value["HzOfsMinDay_phsA"] = m_pdAnalyzer_value["HzOfs_phsA"];
    }
    //日频率偏平均值A
    m_Tol_HzOfs_Day_phsA += m_pdAnalyzer_value["HzOfs_phsA"];
    m_Count_HzOfs_Day_phsA++;
    m_pdAnalyzer_value["HzOfsAvDay_phsA"] = m_Tol_HzOfs_Day_phsA/m_Count_HzOfs_Day_phsA;

    //日频率偏差极值 B    
    if(m_pdAnalyzer_value["HzOfsMaxDay_phsB"] < m_pdAnalyzer_value["HzOfs_phsB"])
    {
        m_pdAnalyzer_value["HzOfsMaxDay_phsB"] = m_pdAnalyzer_value["HzOfs_phsB"];
    }
    if(m_pdAnalyzer_value["HzOfsMinDay_phsB"] > m_pdAnalyzer_value["HzOfs_phsB"])
    {
        m_pdAnalyzer_value["HzOfsMinDay_phsB"] = m_pdAnalyzer_value["HzOfs_phsB"];
    }
    //日频率偏平均值B
    m_Tol_HzOfs_Day_phsB += m_pdAnalyzer_value["HzOfs_phsB"];
    m_Count_HzOfs_Day_phsB++;
    m_pdAnalyzer_value["HzOfsAvDay_phsB"] = m_Tol_HzOfs_Day_phsB/m_Count_HzOfs_Day_phsB;

    //日频率偏差极值 C    
    if(m_pdAnalyzer_value["HzOfsMaxDay_phsC"] < m_pdAnalyzer_value["HzOfs_phsC"])
    {
        m_pdAnalyzer_value["HzOfsMaxDay_phsC"] = m_pdAnalyzer_value["HzOfs_phsC"];
    }
    if(m_pdAnalyzer_value["HzOfsMinDay_phsC"] > m_pdAnalyzer_value["HzOfs_phsC"])
    {
        m_pdAnalyzer_value["HzOfsMinDay_phsC"] = m_pdAnalyzer_value["HzOfs_phsC"];
    }
    //日频率偏平均值C
    m_Tol_HzOfs_Day_phsC += m_pdAnalyzer_value["HzOfs_phsC"];
    m_Count_HzOfs_Day_phsC++;
    m_pdAnalyzer_value["HzOfsAvDay_phsC"] = m_Tol_HzOfs_Day_phsC/m_Count_HzOfs_Day_phsC;


    //周频率偏差极值
    if(m_pdAnalyzer_value["HzOfsMaxWeek_phsA"] < m_pdAnalyzer_value["HzOfs_phsA"])
    {
        m_pdAnalyzer_value["HzOfsMaxWeek_phsA"] = m_pdAnalyzer_value["HzOfs_phsA"];
    }
    if(m_pdAnalyzer_value["HzOfsMinWeek_phsA"] > m_pdAnalyzer_value["HzOfs_phsA"])
    {
        m_pdAnalyzer_value["HzOfsMinWeek_phsA"] = m_pdAnalyzer_value["HzOfs_phsA"];
    }
    //Week频率偏平均值A
    m_Tol_HzOfs_Week_phsA += m_pdAnalyzer_value["HzOfs_phsA"];
    m_Count_HzOfs_Week_phsA++;
    m_pdAnalyzer_value["HzOfsAvWeek_phsA"] = m_Tol_HzOfs_Week_phsA/m_Count_HzOfs_Week_phsA;

    //Week频率偏差极值 B    
    if(m_pdAnalyzer_value["HzOfsMaxWeek_phsB"] < m_pdAnalyzer_value["HzOfs_phsB"])
    {
        m_pdAnalyzer_value["HzOfsMaxWeek_phsB"] = m_pdAnalyzer_value["HzOfs_phsB"];
    }
    if(m_pdAnalyzer_value["HzOfsMinWeek_phsB"] > m_pdAnalyzer_value["HzOfs_phsB"])
    {
        m_pdAnalyzer_value["HzOfsMinWeek_phsB"] = m_pdAnalyzer_value["HzOfs_phsB"];
    }
    //Week频率偏平均值B
    m_Tol_HzOfs_Week_phsB += m_pdAnalyzer_value["HzOfs_phsB"];
    m_Count_HzOfs_Week_phsB++;
    m_pdAnalyzer_value["HzOfsAvWeek_phsB"] = m_Tol_HzOfs_Week_phsB/m_Count_HzOfs_Week_phsB;

    //Week频率偏差极值 C    
    if(m_pdAnalyzer_value["HzOfsMaxWeek_phsC"] < m_pdAnalyzer_value["HzOfs_phsC"])
    {
        m_pdAnalyzer_value["HzOfsMaxWeek_phsC"] = m_pdAnalyzer_value["HzOfs_phsC"];
    }
    if(m_pdAnalyzer_value["HzOfsMinWeek_phsC"] > m_pdAnalyzer_value["HzOfs_phsC"])
    {
        m_pdAnalyzer_value["HzOfsMinWeek_phsC"] = m_pdAnalyzer_value["HzOfs_phsC"];
    }
    //Week频率偏平均值C
    m_Tol_HzOfs_Week_phsC += m_pdAnalyzer_value["HzOfs_phsC"];
    m_Count_HzOfs_Week_phsC++; 
    m_pdAnalyzer_value["HzOfsAvWeek_phsC"] = m_Tol_HzOfs_Week_phsC/m_Count_HzOfs_Week_phsC;


    //月频率偏差极值
    if(m_pdAnalyzer_value["HzOfsMaxMon_phsA"] < m_pdAnalyzer_value["HzOfs_phsA"])
    {
        m_pdAnalyzer_value["HzOfsMaxMon_phsA"] = m_pdAnalyzer_value["HzOfs_phsA"];
    }
    if(m_pdAnalyzer_value["HzOfsMinMon_phsA"] > m_pdAnalyzer_value["HzOfs_phsA"])
    {
        m_pdAnalyzer_value["HzOfsMinMon_phsA"] = m_pdAnalyzer_value["HzOfs_phsA"];
    }
    //Mon频率偏平均值A
    m_Tol_HzOfs_Mon_phsA += m_pdAnalyzer_value["HzOfs_phsA"];
    m_Count_HzOfs_Mon_phsA++;
    m_pdAnalyzer_value["HzOfsAvMon_phsA"] = m_Tol_HzOfs_Mon_phsA/m_Count_HzOfs_Mon_phsA;

    //Mon频率偏差极值 B    
    if(m_pdAnalyzer_value["HzOfsMaxMon_phsB"] < m_pdAnalyzer_value["HzOfs_phsB"])
    {
        m_pdAnalyzer_value["HzOfsMaxMon_phsB"] = m_pdAnalyzer_value["HzOfs_phsB"];
    }
    if(m_pdAnalyzer_value["HzOfsMinMon_phsB"] > m_pdAnalyzer_value["HzOfs_phsB"])
    {
        m_pdAnalyzer_value["HzOfsMinMon_phsB"] = m_pdAnalyzer_value["HzOfs_phsB"];
    }
    //Mon频率偏平均值B
    m_Tol_HzOfs_Mon_phsB += m_pdAnalyzer_value["HzOfs_phsB"];
    m_Count_HzOfs_Mon_phsB++;
    m_pdAnalyzer_value["HzOfsAvMon_phsB"] = m_Tol_HzOfs_Mon_phsB/m_Count_HzOfs_Mon_phsB;

    //Mon频率偏差极值 C    
    if(m_pdAnalyzer_value["HzOfsMaxMon_phsC"] < m_pdAnalyzer_value["HzOfs_phsC"])
    {
        m_pdAnalyzer_value["HzOfsMaxMon_phsC"] = m_pdAnalyzer_value["HzOfs_phsC"];
    }
    if(m_pdAnalyzer_value["HzOfsMinMon_phsC"] > m_pdAnalyzer_value["HzOfs_phsC"])
    {
        m_pdAnalyzer_value["HzOfsMinMon_phsC"] = m_pdAnalyzer_value["HzOfs_phsC"];
    }
    //Mon频率偏平均值C
    m_Tol_HzOfs_Mon_phsC += m_pdAnalyzer_value["HzOfs_phsC"];
    m_Count_HzOfs_Mon_phsC++;
    m_pdAnalyzer_value["HzOfsAvMon_phsC"] = m_Tol_HzOfs_Mon_phsC/m_Count_HzOfs_Mon_phsC;



    //三相电流不平衡累计时间 
    if(m_T_Imb_YX.IsStart() || m_T_Imb_YX.CalOvertime())
    {
        m_voltage_time.ImbNgA_Tm_Day ++; 
        m_voltage_time.ImbNgA_Tm_Mon ++;  
    }

    //三相电压不平衡累计时间
    //待定，如果测试不过改为计时的
    /////////////////////////////////////////////
    if(m_pdAnalyzer_value_YX["ImbNgV_Alm"] == 1)
    {
        m_voltage_time.ImbNgV_Tm_Day ++;  
        m_voltage_time.ImbNgV_Tm_Mon ++; 
    }
    ///////////////////////////////////////////////////
    m_pdAnalyzer_value["ImbNgA_Num_Day"] = m_voltage_time.ImbNgA_Num_Day;
    m_pdAnalyzer_value["ImbNgA_Num_Day"] = m_voltage_time.ImbNgA_Num_Day;
    m_pdAnalyzer_value["ImbNgA_Tm_Day"] = m_voltage_time.ImbNgA_Tm_Day;
    m_pdAnalyzer_value["ImbNgA_Tm_Mon"] = m_voltage_time.ImbNgA_Tm_Mon;
    m_pdAnalyzer_value["ImbNgV_Num_Day"] = m_voltage_time.ImbNgV_Num_Day;
    m_pdAnalyzer_value["ImbNgV_Num_Mon"] = m_voltage_time.ImbNgV_Num_Mon;
    m_pdAnalyzer_value["ImbNgV_Tm_Day"] = m_voltage_time.ImbNgV_Tm_Day;
    m_pdAnalyzer_value["ImbNgV_Tm_Mon"] = m_voltage_time.ImbNgV_Tm_Mon;

    //配变日累计时长
    

    //空载告警警时长存在，报警时长累加，告警时长复归，日空载时长等于上次告警时长

    if(m_voltage_time.PTUC_Unld_Alm_Tm_s > 0) 
    {
        m_voltage_time.PTUC_Unld_Tm_Alm = m_voltage_time.PTUC_Unld_Alm_Tm_s/60.0 ;
    }
    else if (m_voltage_time.PTUC_Unld_Alm_Tm_s == 0 )
    {
        m_voltage_time.PTUC_Unld_Tm_Day += m_voltage_time.PTUC_Unld_Tm_Alm;
        m_voltage_time.PTUC_Unld_Tm_Alm = 0;
    }

    if(m_voltage_time.PTUC_lvld_Alm_Tm_s > 0) 
    {
        m_voltage_time.PTUC_lvld_Tm_Alm = m_voltage_time.PTUC_lvld_Alm_Tm_s/60.0 ;
    }
    else if (m_voltage_time.PTUC_lvld_Alm_Tm_s == 0 )
    {
        m_voltage_time.PTUC_lvld_Tm_Day += m_voltage_time.PTUC_lvld_Tm_Alm;
        m_voltage_time.PTUC_lvld_Tm_Alm = 0;
    }

    if(m_voltage_time.PTOC_Hvld_Alm_Tm_s > 0)
    {
        m_voltage_time.PTOC_Hvld_Tm_Alm = m_voltage_time.PTOC_Hvld_Alm_Tm_s/60.0;
    }
    else if(m_voltage_time.PTOC_Hvld_Alm_Tm_s == 0)
    {
        m_voltage_time.PTOC_Hvld_Tm_Day += m_voltage_time.PTOC_Hvld_Tm_Alm;
        m_voltage_time.PTOC_Hvld_Tm_Alm = 0;
    }
    //反向重载累计时长
    if(m_voltage_time.Re_PTOC_abnormal_Ovld_Alm_Tm_s >0)
    {
        m_voltage_time.Re_PTOC_Hvld_Tm_Alm = m_voltage_time.Re_PTOC_Hvld_Alm_Tm_s / 60.0;
    }
    else if(m_voltage_time.Re_PTOC_Hvld_Alm_Tm_s == 0)
    {
        m_voltage_time.Re_PTOC_Hvld_Tm_Day += m_voltage_time.Re_PTOC_Hvld_Tm_Alm;
        m_voltage_time.Re_PTOC_Hvld_Tm_Alm = 0;
    }

    if(m_LoadRate <= m_Param_value["PTUC_Unld_Lim"])
    {
        m_voltage_time.PTUC_Unld_Tm_Alm ++;
        m_voltage_time.PTUC_Unld_Tm_Day ++;
    }
    else
    {
        m_voltage_time.PTUC_Unld_Tm_Alm = 0;
    }

    if(m_LoadRate > m_Param_value["PTUC_Unld_Lim"]  && m_LoadRate <= m_Param_value["PTUC_Lvld_Lim"])
    {
        m_voltage_time.PTUC_lvld_Tm_Alm ++;
        m_voltage_time.PTUC_lvld_Tm_Day ++;
    }
    else 
    {
        m_voltage_time.PTUC_lvld_Tm_Alm =0;
    }

    if(m_LoadRate > m_Param_value["PTUC_Lvld_Lim"]  && m_LoadRate <= m_Param_value["PTOC_Hvld_Lim"])
    {
        m_voltage_time.PTOC_Hvld_Tm_Alm ++;
        m_voltage_time.PTOC_Hvld_Tm_Day ++;
    }
    else 
    {
        m_voltage_time.PTOC_Hvld_Tm_Alm =0;
    }
    
    m_pdAnalyzer_value["PTUC_Unld_Tm_Alm"] = m_voltage_time.PTUC_Unld_Tm_Alm;
    m_pdAnalyzer_value["PTUC_Unld_Tm_Day"] = m_voltage_time.PTUC_Unld_Tm_Day;
    m_pdAnalyzer_value["PTUC_lvld_Tm_Alm"] = m_voltage_time.PTUC_lvld_Tm_Alm;
    m_pdAnalyzer_value["PTUC_lvld_Tm_Day"] = m_voltage_time.PTUC_lvld_Tm_Day;
    m_pdAnalyzer_value["PTUC_Unld_Tm_Alm"] = m_voltage_time.PTUC_Unld_Tm_Alm;
    
    


    //将最新数据写入文件
    WriteJsonFile_preData();
    return true;
}

bool CParamManager::Unpack_pdAnalyzerData(std::string guid)
{
    CIIAutoMutex mutex(&m_cs);

    mskprintf("1分钟，计算电能质量分析\n");
    mskprintf(guid.c_str());
    mskprintf("交采原始数据\n");

    std::map<std::string,float>::iterator it = m_dev_Analyzer_value[guid].begin();        
    for(;it != m_dev_Analyzer_value[guid].end();++it)
    {
        std::string name = it->first;  
        float value = it->second;
        //mskprintf("@@NAME: %s, value: %f\n",name.c_str(),value);
    }


    // 打印变量信息
    std::map<std::string, float>::iterator iter;
    for (iter = CParamManager::CreateInstance().m_ADC_value.begin(); iter != CParamManager::CreateInstance().m_ADC_value.end(); iter++)
    {
        mskprintf("name =  %s , value = %f\n", iter->first.c_str(), iter->second);
    } 

    //时间更新
    m_dev_time[guid].PhV_Tm_Day_phsA++;   //当日检测时间
    m_dev_time[guid].PhV_Tm_Day_phsB++;   //当日检测时间
    m_dev_time[guid].PhV_Tm_Day_phsC++;   //当日检测时间
    m_dev_time[guid].PhV_Tm_Mon_phsA++;   //当月检测时间
    m_dev_time[guid].PhV_Tm_Mon_phsB++;   //当月检测时间
    m_dev_time[guid].PhV_Tm_Mon_phsC++;   //当月检测时间 

    Unpack_pdAnalyzerImb();//不平衡度

    // A电压超上限时间
    if(m_dev_time[guid].PTOV_Tm_phsA_S >= SEC_PER_MIN)
    {
        m_dev_time[guid].PTOV_Tm_Day_phsA++;
        m_dev_time[guid].PTOV_Tm_Mon_phsA++;
    } 
    else if(m_dev_time[guid].PTUV_Tm_phsA_S >= SEC_PER_MIN)
    {
        m_dev_time[guid].PTUV_Tm_Day_phsA++;
        m_dev_time[guid].PTUV_Tm_Mon_phsA++;
    }
    else
    {
        m_dev_time[guid].PASS_Tm_Day_phsA++;
        m_dev_time[guid].PASS_Tm_Mon_phsA++;
    }

    //mskprintf("PTOV_Tm_Day_phsA(%f),PTUV_Tm_Day_phsA(%f),PASS_Tm_Day_phsA(%f),PhV_Tm_Day_phsA(%f)\n",
    //m_dev_time[guid].PTOV_Tm_Day_phsA,m_dev_time[guid].PTUV_Tm_Day_phsA,m_dev_time[guid].PASS_Tm_Day_phsA,m_dev_time[guid].PhV_Tm_Day_phsA);

    m_dev_time[guid].PTOV_Tm_phsA_S = 0;
    m_dev_time[guid].PTUV_Tm_phsA_S = 0;
    m_dev_time[guid].PASS_Tm_phsA_S = 0;




    // B电压超上限时间
    if(m_dev_time[guid].PTOV_Tm_phsB_S >= SEC_PER_MIN)
    {
        m_dev_time[guid].PTOV_Tm_Day_phsB++;
        m_dev_time[guid].PTOV_Tm_Mon_phsB++;
    } 
    else if(m_dev_time[guid].PTUV_Tm_phsB_S >= SEC_PER_MIN)
    {
        m_dev_time[guid].PTUV_Tm_Day_phsB++;
        m_dev_time[guid].PTUV_Tm_Mon_phsB++;
    }
    else
    {
        m_dev_time[guid].PASS_Tm_Day_phsB++;
        m_dev_time[guid].PASS_Tm_Mon_phsB++;
    }
    m_dev_time[guid].PTOV_Tm_phsB_S = 0;
    m_dev_time[guid].PTUV_Tm_phsB_S = 0;
    m_dev_time[guid].PASS_Tm_phsB_S = 0;

    // C电压超上限时间
    if(m_dev_time[guid].PTOV_Tm_phsC_S >= SEC_PER_MIN)
    {
        m_dev_time[guid].PTOV_Tm_Day_phsC++;
        m_dev_time[guid].PTOV_Tm_Mon_phsC++;
    } 
    else if(m_dev_time[guid].PTUV_Tm_phsC_S >= SEC_PER_MIN)
    {
        m_dev_time[guid].PTUV_Tm_Day_phsC++;
        m_dev_time[guid].PTUV_Tm_Mon_phsC++;
    }
    else
    {
        m_dev_time[guid].PASS_Tm_Day_phsC++;
        m_dev_time[guid].PASS_Tm_Mon_phsC++;
    }
    m_dev_time[guid].PTOV_Tm_phsC_S = 0;
    m_dev_time[guid].PTUV_Tm_phsC_S = 0;
    m_dev_time[guid].PASS_Tm_phsC_S = 0;

    //电压越上限日累计时间
    m_dev_Analyzer_value[guid]["PTOV_Tm_Day_phsA"] = m_dev_time[guid].PTOV_Tm_Day_phsA;
    m_dev_Analyzer_value[guid]["PTUV_Tm_Day_phsA"] = m_dev_time[guid].PTUV_Tm_Day_phsA;
    m_dev_Analyzer_value[guid]["PTOV_Tm_Day_phsB"] = m_dev_time[guid].PTOV_Tm_Day_phsB;
    m_dev_Analyzer_value[guid]["PTUV_Tm_Day_phsB"] = m_dev_time[guid].PTUV_Tm_Day_phsB;
    m_dev_Analyzer_value[guid]["PTOV_Tm_Day_phsC"] = m_dev_time[guid].PTOV_Tm_Day_phsC;
    m_dev_Analyzer_value[guid]["PTUV_Tm_Day_phsC"] = m_dev_time[guid].PTUV_Tm_Day_phsC;
    mskprintf("PTOV_Tm_Day_phsA:%f PTUV_Tm_Day_phsA:%f\n",m_dev_Analyzer_value[guid]["PTOV_Tm_Day_phsA"],m_dev_Analyzer_value[guid]["PTUV_Tm_Day_phsA"]);
    mskprintf("PTOV_Tm_Day_phsB:%f PTUV_Tm_Day_phsB:%f\n",m_dev_Analyzer_value[guid]["PTOV_Tm_Day_phsB"],m_dev_Analyzer_value[guid]["PTUV_Tm_Day_phsB"]);
    mskprintf("PTOV_Tm_Day_phsC:%f PTUV_Tm_Day_phsC:%f\n",m_dev_Analyzer_value[guid]["PTOV_Tm_Day_phsC"],m_dev_Analyzer_value[guid]["PTUV_Tm_Day_phsC"]);
    //电压越下限日累计时间
    m_dev_Analyzer_value[guid]["PTOV_Tm_Mon_phsA"] = m_dev_time[guid].PTOV_Tm_Mon_phsA;
    m_dev_Analyzer_value[guid]["PTUV_Tm_Mon_phsA"] = m_dev_time[guid].PTUV_Tm_Mon_phsA;
    m_dev_Analyzer_value[guid]["PTOV_Tm_Mon_phsB"] = m_dev_time[guid].PTOV_Tm_Mon_phsB;
    m_dev_Analyzer_value[guid]["PTUV_Tm_Mon_phsB"] = m_dev_time[guid].PTUV_Tm_Mon_phsB;
    m_dev_Analyzer_value[guid]["PTOV_Tm_Mon_phsC"] = m_dev_time[guid].PTOV_Tm_Mon_phsC;
    m_dev_Analyzer_value[guid]["PTUV_Tm_Mon_phsC"] = m_dev_time[guid].PTUV_Tm_Mon_phsC;
    mskprintf("PTOV_Tm_Mon_phsA:%f PTUV_Tm_Mon_phsA:%f\n",m_dev_Analyzer_value[guid]["PTOV_Tm_Mon_phsA"],m_dev_Analyzer_value[guid]["PTUV_Tm_Mon_phsA"]);
    mskprintf("PTOV_Tm_Mon_phsB:%f PTUV_Tm_Mon_phsB:%f\n",m_dev_Analyzer_value[guid]["PTOV_Tm_Mon_phsB"],m_dev_Analyzer_value[guid]["PTUV_Tm_Mon_phsB"]);
    mskprintf("PTOV_Tm_Mon_phsC:%f PTUV_Tm_Mon_phsC:%f\n",m_dev_Analyzer_value[guid]["PTOV_Tm_Mon_phsC"],m_dev_Analyzer_value[guid]["PTUV_Tm_Mon_phsC"]);

    //计算合格率A
    if((m_dev_time[guid].PhV_Tm_Day_phsA > 0) && (m_dev_time[guid].PhV_Tm_Mon_phsA > 0))//计算合格率 
    {
        m_dev_Analyzer_value[guid]["PTOV_Rate_Day_phsA"] = 100.0 * m_dev_time[guid].PTOV_Tm_Day_phsA / m_dev_time[guid].PhV_Tm_Day_phsA;
        m_dev_Analyzer_value[guid]["PTOV_Rate_Mon_phsA"] = 100.0 * m_dev_time[guid].PTOV_Tm_Mon_phsA / m_dev_time[guid].PhV_Tm_Mon_phsA ;
        m_dev_Analyzer_value[guid]["PTUV_Rate_Day_phsA"] = 100.0 * m_dev_time[guid].PTUV_Tm_Day_phsA / m_dev_time[guid].PhV_Tm_Day_phsA ;
        m_dev_Analyzer_value[guid]["PTUV_Rate_Mon_phsA"] = 100.0 * m_dev_time[guid].PTUV_Tm_Mon_phsA / m_dev_time[guid].PhV_Tm_Mon_phsA ;
        m_dev_Analyzer_value[guid]["PhVPassDay_phsA"] = 100.0 * m_dev_time[guid].PASS_Tm_Day_phsA / m_dev_time[guid].PhV_Tm_Day_phsA ;
        m_dev_Analyzer_value[guid]["PhVPassMon_phsA"] = 100.0 * m_dev_time[guid].PASS_Tm_Mon_phsA / m_dev_time[guid].PhV_Tm_Mon_phsA ;

        mskprintf("m_dev_Analyzer_value[guid][PhVPassDay_phsA] = %f\n",m_dev_Analyzer_value[guid]["PhVPassDay_phsA"]);
        mskprintf("m_dev_Analyzer_value[guid][PhVPassMon_phsA] = %f\n",m_dev_Analyzer_value[guid]["PhVPassMon_phsA"]);
    }
    //计算合格率B
    if((m_dev_time[guid].PhV_Tm_Day_phsB > 0) && (m_dev_time[guid].PhV_Tm_Mon_phsB > 0))//计算合格率 
    {
        m_dev_Analyzer_value[guid]["PTOV_Rate_Day_phsB"] = 100.0 * m_dev_time[guid].PTOV_Tm_Day_phsB / m_dev_time[guid].PhV_Tm_Day_phsB ;
        m_dev_Analyzer_value[guid]["PTOV_Rate_Mon_phsB"] = 100.0 * m_dev_time[guid].PTOV_Tm_Mon_phsB / m_dev_time[guid].PhV_Tm_Mon_phsB ;
        m_dev_Analyzer_value[guid]["PTUV_Rate_Day_phsB"] = 100.0 * m_dev_time[guid].PTUV_Tm_Day_phsB / m_dev_time[guid].PhV_Tm_Day_phsB ;
        m_dev_Analyzer_value[guid]["PTUV_Rate_Mon_phsB"] = 100.0 * m_dev_time[guid].PTUV_Tm_Mon_phsB / m_dev_time[guid].PhV_Tm_Mon_phsB ;
        m_dev_Analyzer_value[guid]["PhVPassDay_phsB"] = 100.0 * m_dev_time[guid].PASS_Tm_Day_phsB / m_dev_time[guid].PhV_Tm_Day_phsB ;
        m_dev_Analyzer_value[guid]["PhVPassMon_phsB"] = 100.0 * m_dev_time[guid].PASS_Tm_Mon_phsB / m_dev_time[guid].PhV_Tm_Mon_phsB ;
        mskprintf("m_dev_Analyzer_value[guid][PhVPassDay_phsB] = %f\n",m_dev_Analyzer_value[guid]["PhVPassDay_phsB"]);
        mskprintf("m_dev_Analyzer_value[guid][PhVPassMon_phsB] = %f\n",m_dev_Analyzer_value[guid]["PhVPassMon_phsB"]);
    }
    //计算合格率C
    if((m_dev_time[guid].PhV_Tm_Day_phsC > 0) && (m_dev_time[guid].PhV_Tm_Mon_phsC > 0))//计算合格率 
    {
        m_dev_Analyzer_value[guid]["PTOV_Rate_Day_phsC"] = 100.0 * m_dev_time[guid].PTOV_Tm_Day_phsC / m_dev_time[guid].PhV_Tm_Day_phsC ;
        m_dev_Analyzer_value[guid]["PTOV_Rate_Mon_phsC"] = 100.0 * m_dev_time[guid].PTOV_Tm_Mon_phsC / m_dev_time[guid].PhV_Tm_Mon_phsC ;
        m_dev_Analyzer_value[guid]["PTUV_Rate_Day_phsC"] = 100.0 * m_dev_time[guid].PTUV_Tm_Day_phsC / m_dev_time[guid].PhV_Tm_Day_phsC ;
        m_dev_Analyzer_value[guid]["PTUV_Rate_Mon_phsC"] = 100.0 * m_dev_time[guid].PTUV_Tm_Mon_phsC / m_dev_time[guid].PhV_Tm_Mon_phsC ;
        m_dev_Analyzer_value[guid]["PhVPassDay_phsC"] = 100.0 * m_dev_time[guid].PASS_Tm_Day_phsC / m_dev_time[guid].PhV_Tm_Day_phsC ;
        m_dev_Analyzer_value[guid]["PhVPassMon_phsC"] = 100.0 * m_dev_time[guid].PASS_Tm_Mon_phsC / m_dev_time[guid].PhV_Tm_Mon_phsC ;
        mskprintf("m_dev_Analyzer_value[guid][PhVPassDay_phsC] = %f\n",m_dev_Analyzer_value[guid]["PhVPassDay_phsC"]);
        mskprintf("m_dev_Analyzer_value[guid][PhVPassMon_phsC] = %f\n",m_dev_Analyzer_value[guid]["PhVPassMon_phsC"]);
    }
    
    
    //电压偏差
    m_dev_Analyzer_value[guid]["PhVOfs_phsA"]  =  100* (m_ADC_value["PhV_phsA"] - m_Param_value["std_voltage"]) / m_Param_value["std_voltage"];
    m_dev_Analyzer_value[guid]["PhVOfs_phsB"]  =  100* (m_ADC_value["PhV_phsB"] - m_Param_value["std_voltage"]) / m_Param_value["std_voltage"];
    m_dev_Analyzer_value[guid]["PhVOfs_phsC"]  =  100* (m_ADC_value["PhV_phsC"] - m_Param_value["std_voltage"]) / m_Param_value["std_voltage"];

    //mskprintf("%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%   m_dev_Analyzer_value[guid][PhVOfs_phsB] = %f,(m_ADC_value[PhV_phsB]=(%f), .std_voltage(%f)\n",
    //m_dev_Analyzer_value[guid]["PhVOfs_phsB"],m_ADC_value["PhV_phsB"] , .std_voltage);

    //日电压偏差极值A
    if(m_dev_Analyzer_value[guid]["PhVOfsMaxDay_phsA"] < m_dev_Analyzer_value[guid]["PhVOfs_phsA"])
    {
        m_dev_Analyzer_value[guid]["PhVOfsMaxDay_phsA"] = m_dev_Analyzer_value[guid]["PhVOfs_phsA"];
    }
    if(m_dev_Analyzer_value[guid]["PhVOfsMinDay_phsA"] > m_dev_Analyzer_value[guid]["PhVOfs_phsA"])
    {
        m_dev_Analyzer_value[guid]["PhVOfsMinDay_phsA"] = m_dev_Analyzer_value[guid]["PhVOfs_phsA"];
    }
    //mskprintf("%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%   m_dev_Analyzer_value[guid][PhVOfsMaxDay_phsA] = %f,m_dev_Analyzer_value[guid][PhVOfsMinDay_phsA] = %f\n",
    //m_dev_Analyzer_value[guid]["PhVOfsMaxDay_phsA"],m_dev_Analyzer_value[guid]["PhVOfsMinDay_phsA"]);


    //日电压偏平均值A
    m_Tol_PhVOfs_Day_phsA += m_dev_Analyzer_value[guid]["PhVOfs_phsA"];
    m_Count_PhVOfs_Day_phsA++;
    m_dev_Analyzer_value[guid]["PhVOfsAvDay_phsA"] = m_Tol_PhVOfs_Day_phsA/m_Count_PhVOfs_Day_phsA;

    //日电压偏差极值B
    if(m_dev_Analyzer_value[guid]["PhVOfsMaxDay_phsB"] < m_dev_Analyzer_value[guid]["PhVOfs_phsB"])
    {
        m_dev_Analyzer_value[guid]["PhVOfsMaxDay_phsB"] = m_dev_Analyzer_value[guid]["PhVOfs_phsB"];
    }
    if(m_dev_Analyzer_value[guid]["PhVOfsMinDay_phsB"] > m_dev_Analyzer_value[guid]["PhVOfs_phsB"])
    {
        m_dev_Analyzer_value[guid]["PhVOfsMinDay_phsB"] = m_dev_Analyzer_value[guid]["PhVOfs_phsB"];
    }
    //日电压偏平均值B
    m_Tol_PhVOfs_Day_phsB += m_dev_Analyzer_value[guid]["PhVOfs_phsB"];
    m_Count_PhVOfs_Day_phsB++;
    m_dev_Analyzer_value[guid]["PhVOfsAvDay_phsB"] = m_Tol_PhVOfs_Day_phsB/m_Count_PhVOfs_Day_phsB;

    //日电压偏差极值C
    if(m_dev_Analyzer_value[guid]["PhVOfsMaxDay_phsC"] < m_dev_Analyzer_value[guid]["PhVOfs_phsC"])
    {
        m_dev_Analyzer_value[guid]["PhVOfsMaxDay_phsC"] = m_dev_Analyzer_value[guid]["PhVOfs_phsC"];
    }
    if(m_dev_Analyzer_value[guid]["PhVOfsMinDay_phsC"] > m_dev_Analyzer_value[guid]["PhVOfs_phsC"])
    {
        m_dev_Analyzer_value[guid]["PhVOfsMinDay_phsC"] = m_dev_Analyzer_value[guid]["PhVOfs_phsC"];
    }
    //日电压偏平均值C
    m_Tol_PhVOfs_Day_phsC += m_dev_Analyzer_value[guid]["PhVOfs_phsC"];
    m_Count_PhVOfs_Day_phsC++;
    m_dev_Analyzer_value[guid]["PhVOfsAvDay_phsC"] = m_Tol_PhVOfs_Day_phsC/m_Count_PhVOfs_Day_phsC;

    //周电压偏差极值
    //周电压偏差极值A
    if(m_dev_Analyzer_value[guid]["PhVOfsMaxWeek_phsA"] < m_dev_Analyzer_value[guid]["PhVOfs_phsA"])
    {
        m_dev_Analyzer_value[guid]["PhVOfsMaxWeek_phsA"] = m_dev_Analyzer_value[guid]["PhVOfs_phsA"];
    }
    if(m_dev_Analyzer_value[guid]["PhVOfsMinWeek_phsA"] > m_dev_Analyzer_value[guid]["PhVOfs_phsA"])
    {
        m_dev_Analyzer_value[guid]["PhVOfsMinWeek_phsA"] = m_dev_Analyzer_value[guid]["PhVOfs_phsA"];
    }
    //周电压偏平均值A
    m_Tol_PhVOfs_Week_phsA += m_dev_Analyzer_value[guid]["PhVOfs_phsA"];
    m_Count_PhVOfs_Week_phsA++;
    m_dev_Analyzer_value[guid]["PhVOfsAvWeek_phsA"] = m_Tol_PhVOfs_Week_phsA/m_Count_PhVOfs_Week_phsA;

    //周电压偏差极值B
    if(m_dev_Analyzer_value[guid]["PhVOfsMaxWeek_phsB"] < m_dev_Analyzer_value[guid]["PhVOfs_phsB"])
    {
        m_dev_Analyzer_value[guid]["PhVOfsMaxWeek_phsB"] = m_dev_Analyzer_value[guid]["PhVOfs_phsB"];
    }
    if(m_dev_Analyzer_value[guid]["PhVOfsMinWeek_phsB"] > m_dev_Analyzer_value[guid]["PhVOfs_phsB"])
    {
        m_dev_Analyzer_value[guid]["PhVOfsMinWeek_phsB"] = m_dev_Analyzer_value[guid]["PhVOfs_phsB"];
    }
    //周电压偏平均值B
    m_Tol_PhVOfs_Week_phsB += m_dev_Analyzer_value[guid]["PhVOfs_phsB"];
    m_Count_PhVOfs_Week_phsB++;
    m_dev_Analyzer_value[guid]["PhVOfsAvWeek_phsB"] = m_Tol_PhVOfs_Week_phsB/m_Count_PhVOfs_Week_phsB;

    //周电压偏差极值C
    if(m_dev_Analyzer_value[guid]["PhVOfsMaxWeek_phsC"] < m_dev_Analyzer_value[guid]["PhVOfs_phsC"])
    {
        m_dev_Analyzer_value[guid]["PhVOfsMaxWeek_phsC"] = m_dev_Analyzer_value[guid]["PhVOfs_phsC"];
    }
    if(m_dev_Analyzer_value[guid]["PhVOfsMinWeek_phsC"] > m_dev_Analyzer_value[guid]["PhVOfs_phsC"])
    {
        m_dev_Analyzer_value[guid]["PhVOfsMinWeek_phsC"] = m_dev_Analyzer_value[guid]["PhVOfs_phsC"];
    }
    //周电压偏平均值C
    m_Tol_PhVOfs_Week_phsC += m_dev_Analyzer_value[guid]["PhVOfs_phsC"];
    m_Count_PhVOfs_Week_phsC++;
    m_dev_Analyzer_value[guid]["PhVOfsAvWeek_phsC"] = m_Tol_PhVOfs_Week_phsC/m_Count_PhVOfs_Week_phsC;

    //月电压偏差极值
    //月电压偏差极值A
    if(m_dev_Analyzer_value[guid]["PhVOfsMaxMon_phsA"] < m_dev_Analyzer_value[guid]["PhVOfs_phsA"])
    {
        m_dev_Analyzer_value[guid]["PhVOfsMaxMon_phsA"] = m_dev_Analyzer_value[guid]["PhVOfs_phsA"];
    }
    if(m_dev_Analyzer_value[guid]["PhVOfsMinMon_phsA"] > m_dev_Analyzer_value[guid]["PhVOfs_phsA"])
    {
        m_dev_Analyzer_value[guid]["PhVOfsMinMon_phsA"] = m_dev_Analyzer_value[guid]["PhVOfs_phsA"];
    }
    //Mon电压偏平均值A
    m_Tol_PhVOfs_Mon_phsA += m_dev_Analyzer_value[guid]["PhVOfs_phsA"];
    m_Count_PhVOfs_Mon_phsA++;
    m_dev_Analyzer_value[guid]["PhVOfsAvMon_phsA"] = m_Tol_PhVOfs_Mon_phsA/m_Count_PhVOfs_Mon_phsA;

    //Mon电压偏差极值B
    if(m_dev_Analyzer_value[guid]["PhVOfsMaxMon_phsB"] < m_dev_Analyzer_value[guid]["PhVOfs_phsB"])
    {
        m_dev_Analyzer_value[guid]["PhVOfsMaxMon_phsB"] = m_dev_Analyzer_value[guid]["PhVOfs_phsB"];
    }
    if(m_dev_Analyzer_value[guid]["PhVOfsMinMon_phsB"] > m_dev_Analyzer_value[guid]["PhVOfs_phsB"])
    {
        m_dev_Analyzer_value[guid]["PhVOfsMinMon_phsB"] = m_dev_Analyzer_value[guid]["PhVOfs_phsB"];
    }
    //Mon电压偏平均值B
    m_Tol_PhVOfs_Mon_phsB += m_dev_Analyzer_value[guid]["PhVOfs_phsB"];
    m_Count_PhVOfs_Mon_phsB++;
    m_dev_Analyzer_value[guid]["PhVOfsAvMon_phsB"] = m_Tol_PhVOfs_Mon_phsB/m_Count_PhVOfs_Mon_phsB;

    //Mon电压偏差极值C
    if(m_dev_Analyzer_value[guid]["PhVOfsMaxMon_phsC"] < m_dev_Analyzer_value[guid]["PhVOfs_phsC"])
    {
        m_dev_Analyzer_value[guid]["PhVOfsMaxMon_phsC"] = m_dev_Analyzer_value[guid]["PhVOfs_phsC"];
    }
    if(m_dev_Analyzer_value[guid]["PhVOfsMinMon_phsC"] > m_dev_Analyzer_value[guid]["PhVOfs_phsC"])
    {
        m_dev_Analyzer_value[guid]["PhVOfsMinMon_phsC"] = m_dev_Analyzer_value[guid]["PhVOfs_phsC"];
    }
    //Mon电压偏平均值C
    m_Tol_PhVOfs_Mon_phsC += m_dev_Analyzer_value[guid]["PhVOfs_phsC"];
    m_Count_PhVOfs_Mon_phsC++;
    m_dev_Analyzer_value[guid]["PhVOfsAvMon_phsC"] = m_Tol_PhVOfs_Mon_phsC/m_Count_PhVOfs_Mon_phsC;

    //频率偏差
    m_dev_Analyzer_value[guid]["HzOfs_phsA"]  =  100.0* (m_ADC_value["Hz_phsA"] - m_Param_value["std_Hz"]) / m_Param_value["std_Hz"];
    m_dev_Analyzer_value[guid]["HzOfs_phsB"]  =  100.0* (m_ADC_value["Hz_phsB"] - m_Param_value["std_Hz"]) / m_Param_value["std_Hz"];
    m_dev_Analyzer_value[guid]["HzOfs_phsC"]  =  100.0* (m_ADC_value["Hz_phsC"] - m_Param_value["std_Hz"]) / m_Param_value["std_Hz"];
    m_dev_Analyzer_value[guid]["TotHzOfs"]    =  100.0* (m_ADC_value["Hz"]      - m_Param_value["std_Hz"]) / m_Param_value["std_Hz"];
    
    //日频率偏差极值 A    
    if(m_dev_Analyzer_value[guid]["HzOfsMaxDay_phsA"] < m_dev_Analyzer_value[guid]["HzOfs_phsA"])
    {
        m_dev_Analyzer_value[guid]["HzOfsMaxDay_phsA"] = m_dev_Analyzer_value[guid]["HzOfs_phsA"];
    }
    if(m_dev_Analyzer_value[guid]["HzOfsMinDay_phsA"] > m_dev_Analyzer_value[guid]["HzOfs_phsA"])
    {
        m_dev_Analyzer_value[guid]["HzOfsMinDay_phsA"] = m_dev_Analyzer_value[guid]["HzOfs_phsA"];
    }
    //日频率偏平均值A
    m_Tol_HzOfs_Day_phsA += m_dev_Analyzer_value[guid]["HzOfs_phsA"];
    m_Count_HzOfs_Day_phsA++;
    m_dev_Analyzer_value[guid]["HzOfsAvDay_phsA"] = m_Tol_HzOfs_Day_phsA/m_Count_HzOfs_Day_phsA;

    //日频率偏差极值 B    
    if(m_dev_Analyzer_value[guid]["HzOfsMaxDay_phsB"] < m_dev_Analyzer_value[guid]["HzOfs_phsB"])
    {
        m_dev_Analyzer_value[guid]["HzOfsMaxDay_phsB"] = m_dev_Analyzer_value[guid]["HzOfs_phsB"];
    }
    if(m_dev_Analyzer_value[guid]["HzOfsMinDay_phsB"] > m_dev_Analyzer_value[guid]["HzOfs_phsB"])
    {
        m_dev_Analyzer_value[guid]["HzOfsMinDay_phsB"] = m_dev_Analyzer_value[guid]["HzOfs_phsB"];
    }
    //日频率偏平均值B
    m_Tol_HzOfs_Day_phsB += m_dev_Analyzer_value[guid]["HzOfs_phsB"];
    m_Count_HzOfs_Day_phsB++;
    m_dev_Analyzer_value[guid]["HzOfsAvDay_phsB"] = m_Tol_HzOfs_Day_phsB/m_Count_HzOfs_Day_phsB;

    //日频率偏差极值 C    
    if(m_dev_Analyzer_value[guid]["HzOfsMaxDay_phsC"] < m_dev_Analyzer_value[guid]["HzOfs_phsC"])
    {
        m_dev_Analyzer_value[guid]["HzOfsMaxDay_phsC"] = m_dev_Analyzer_value[guid]["HzOfs_phsC"];
    }
    if(m_dev_Analyzer_value[guid]["HzOfsMinDay_phsC"] > m_dev_Analyzer_value[guid]["HzOfs_phsC"])
    {
        m_dev_Analyzer_value[guid]["HzOfsMinDay_phsC"] = m_dev_Analyzer_value[guid]["HzOfs_phsC"];
    }
    //日频率偏平均值C
    m_Tol_HzOfs_Day_phsC += m_dev_Analyzer_value[guid]["HzOfs_phsC"];
    m_Count_HzOfs_Day_phsC++;
    m_dev_Analyzer_value[guid]["HzOfsAvDay_phsC"] = m_Tol_HzOfs_Day_phsC/m_Count_HzOfs_Day_phsC;


    //周频率偏差极值
    if(m_dev_Analyzer_value[guid]["HzOfsMaxWeek_phsA"] < m_dev_Analyzer_value[guid]["HzOfs_phsA"])
    {
        m_dev_Analyzer_value[guid]["HzOfsMaxWeek_phsA"] = m_dev_Analyzer_value[guid]["HzOfs_phsA"];
    }
    if(m_dev_Analyzer_value[guid]["HzOfsMinWeek_phsA"] > m_dev_Analyzer_value[guid]["HzOfs_phsA"])
    {
        m_dev_Analyzer_value[guid]["HzOfsMinWeek_phsA"] = m_dev_Analyzer_value[guid]["HzOfs_phsA"];
    }
    //Week频率偏平均值A
    m_Tol_HzOfs_Week_phsA += m_dev_Analyzer_value[guid]["HzOfs_phsA"];
    m_Count_HzOfs_Week_phsA++;
    m_dev_Analyzer_value[guid]["HzOfsAvWeek_phsA"] = m_Tol_HzOfs_Week_phsA/m_Count_HzOfs_Week_phsA;

    //Week频率偏差极值 B    
    if(m_dev_Analyzer_value[guid]["HzOfsMaxWeek_phsB"] < m_dev_Analyzer_value[guid]["HzOfs_phsB"])
    {
        m_dev_Analyzer_value[guid]["HzOfsMaxWeek_phsB"] = m_dev_Analyzer_value[guid]["HzOfs_phsB"];
    }
    if(m_dev_Analyzer_value[guid]["HzOfsMinWeek_phsB"] > m_dev_Analyzer_value[guid]["HzOfs_phsB"])
    {
        m_dev_Analyzer_value[guid]["HzOfsMinWeek_phsB"] = m_dev_Analyzer_value[guid]["HzOfs_phsB"];
    }
    //Week频率偏平均值B
    m_Tol_HzOfs_Week_phsB += m_dev_Analyzer_value[guid]["HzOfs_phsB"];
    m_Count_HzOfs_Week_phsB++;
    m_dev_Analyzer_value[guid]["HzOfsAvWeek_phsB"] = m_Tol_HzOfs_Week_phsB/m_Count_HzOfs_Week_phsB;

    //Week频率偏差极值 C    
    if(m_dev_Analyzer_value[guid]["HzOfsMaxWeek_phsC"] < m_dev_Analyzer_value[guid]["HzOfs_phsC"])
    {
        m_dev_Analyzer_value[guid]["HzOfsMaxWeek_phsC"] = m_dev_Analyzer_value[guid]["HzOfs_phsC"];
    }
    if(m_dev_Analyzer_value[guid]["HzOfsMinWeek_phsC"] > m_dev_Analyzer_value[guid]["HzOfs_phsC"])
    {
        m_dev_Analyzer_value[guid]["HzOfsMinWeek_phsC"] = m_dev_Analyzer_value[guid]["HzOfs_phsC"];
    }
    //Week频率偏平均值C
    m_Tol_HzOfs_Week_phsC += m_dev_Analyzer_value[guid]["HzOfs_phsC"];
    m_Count_HzOfs_Week_phsC++;
    m_dev_Analyzer_value[guid]["HzOfsAvWeek_phsC"] = m_Tol_HzOfs_Week_phsC/m_Count_HzOfs_Week_phsC;


    //月频率偏差极值
    if(m_dev_Analyzer_value[guid]["HzOfsMaxMon_phsA"] < m_dev_Analyzer_value[guid]["HzOfs_phsA"])
    {
        m_dev_Analyzer_value[guid]["HzOfsMaxMon_phsA"] = m_dev_Analyzer_value[guid]["HzOfs_phsA"];
    }
    if(m_dev_Analyzer_value[guid]["HzOfsMinMon_phsA"] > m_dev_Analyzer_value[guid]["HzOfs_phsA"])
    {
        m_dev_Analyzer_value[guid]["HzOfsMinMon_phsA"] = m_dev_Analyzer_value[guid]["HzOfs_phsA"];
    }
    //Mon频率偏平均值A
    m_Tol_HzOfs_Mon_phsA += m_dev_Analyzer_value[guid]["HzOfs_phsA"];
    m_Count_HzOfs_Mon_phsA++;
    m_dev_Analyzer_value[guid]["HzOfsAvMon_phsA"] = m_Tol_HzOfs_Mon_phsA/m_Count_HzOfs_Mon_phsA;

    //Mon频率偏差极值 B    
    if(m_dev_Analyzer_value[guid]["HzOfsMaxMon_phsB"] < m_dev_Analyzer_value[guid]["HzOfs_phsB"])
    {
        m_dev_Analyzer_value[guid]["HzOfsMaxMon_phsB"] = m_dev_Analyzer_value[guid]["HzOfs_phsB"];
    }
    if(m_dev_Analyzer_value[guid]["HzOfsMinMon_phsB"] > m_dev_Analyzer_value[guid]["HzOfs_phsB"])
    {
        m_dev_Analyzer_value[guid]["HzOfsMinMon_phsB"] = m_dev_Analyzer_value[guid]["HzOfs_phsB"];
    }
    //Mon频率偏平均值B
    m_Tol_HzOfs_Mon_phsB += m_dev_Analyzer_value[guid]["HzOfs_phsB"];
    m_Count_HzOfs_Mon_phsB++;
    m_dev_Analyzer_value[guid]["HzOfsAvMon_phsB"] = m_Tol_HzOfs_Mon_phsB/m_Count_HzOfs_Mon_phsB;

    //Mon频率偏差极值 C    
    if(m_dev_Analyzer_value[guid]["HzOfsMaxMon_phsC"] < m_dev_Analyzer_value[guid]["HzOfs_phsC"])
    {
        m_dev_Analyzer_value[guid]["HzOfsMaxMon_phsC"] = m_dev_Analyzer_value[guid]["HzOfs_phsC"];
    }
    if(m_dev_Analyzer_value[guid]["HzOfsMinMon_phsC"] > m_dev_Analyzer_value[guid]["HzOfs_phsC"])
    {
        m_dev_Analyzer_value[guid]["HzOfsMinMon_phsC"] = m_dev_Analyzer_value[guid]["HzOfs_phsC"];
    }
    //Mon频率偏平均值C
    m_Tol_HzOfs_Mon_phsC += m_dev_Analyzer_value[guid]["HzOfs_phsC"];
    m_Count_HzOfs_Mon_phsC++;
    m_dev_Analyzer_value[guid]["HzOfsAvMon_phsC"] = m_Tol_HzOfs_Mon_phsC/m_Count_HzOfs_Mon_phsC;



    //三相电流不平衡累计时间 
    if(m_T_Imb_YX.IsStart() || m_T_Imb_YX.CalOvertime())
    {
        m_dev_time[guid].ImbNgA_Tm_Day ++; 
        m_dev_time[guid].ImbNgA_Tm_Mon ++;  
    }
    
    //三相电压不平衡累计时间
    //待定，如果测试不过改为计时的
    /////////////////////////////////////////////
    // if(m_dev_Analyzer_value_YX["ImbNgV_Alm"] == 1)
    // {
    //     m_dev_time[guid].ImbNgV_Tm_Day ++;  
    //     m_dev_time[guid].ImbNgV_Tm_Mon ++; 
    // }
    ///////////////////////////////////////////////////
    m_dev_Analyzer_value[guid]["ImbNgA_Num_Day"] = m_dev_time[guid].ImbNgA_Num_Day;
    m_dev_Analyzer_value[guid]["ImbNgA_Num_Day"] = m_dev_time[guid].ImbNgA_Num_Day;
    m_dev_Analyzer_value[guid]["ImbNgA_Tm_Day"] = m_dev_time[guid].ImbNgA_Tm_Day;
    m_dev_Analyzer_value[guid]["ImbNgA_Tm_Mon"] = m_dev_time[guid].ImbNgA_Tm_Mon;
    m_dev_Analyzer_value[guid]["ImbNgV_Num_Day"] = m_dev_time[guid].ImbNgV_Num_Day;
    m_dev_Analyzer_value[guid]["ImbNgV_Num_Mon"] = m_dev_time[guid].ImbNgV_Num_Mon;
    m_dev_Analyzer_value[guid]["ImbNgV_Tm_Day"] = m_dev_time[guid].ImbNgV_Tm_Day;
    m_dev_Analyzer_value[guid]["ImbNgV_Tm_Mon"] = m_dev_time[guid].ImbNgV_Tm_Mon;


    //将最新数据写入文件 
    WriteJsonFile_preData();
    return true;
}

//计算可开放容量分析
bool CParamManager::Unpack_ocAnalyzerData()
{
    CIIAutoMutex mutex(&m_cs);
    mskprintf("1秒计算一次配变负载率\n");

    // 打印变量信息
    std::map<std::string, float>::iterator iter;
    for (iter = CParamManager::CreateInstance().m_ADC_value.begin(); iter != CParamManager::CreateInstance().m_ADC_value.end(); iter++)
    {
        mskprintf("name =  %s , value = %f\n", iter->first.c_str(), iter->second);
    }

    // 实时配变负载率
    m_pre_LoadRate = m_ADC_value["TotVA"] / m_Param_value["DeviceLoad"];
    m_LoadRate_sum += m_pre_LoadRate;
    m_LoadRate_num += 1;

    float CT = m_Param_value["ARtg"] / m_Param_value["ARtgSnd"];

    // 实时单相负载率A= A相电流*CT/额定电流 
    m_pre_LoadRate_phsA = m_ADC_value["A_phsA"] * CT / m_Param_value["RtgA"];
    m_LoadRate_phsA_sum += m_pre_LoadRate_phsA;
    m_LoadRate_phsA_num += 1;

    
    // 实时单相负载率B
    m_pre_LoadRate_phsB =  m_ADC_value["A_phsB"] * CT / m_Param_value["RtgA"];;
    m_LoadRate_phsB_sum += m_pre_LoadRate_phsB;
    m_LoadRate_phsB_num += 1;

    // 实时单相负载率C
    m_pre_LoadRate_phsC =  m_ADC_value["A_phsB"] * CT / m_Param_value["RtgA"];;
    m_LoadRate_phsC_sum += m_pre_LoadRate_phsC;
    m_LoadRate_phsC_num += 1;

    mskprintf("last_m_pre_LoadRate: %f, last_m_pre_LoadRate_phsA: %f,last_m_pre_LoadRate_phsB: %f,last_m_pre_LoadRate_phsC:%f\n",m_pre_LoadRate,m_pre_LoadRate_phsA,m_pre_LoadRate_phsB,m_pre_LoadRate_phsC);
    
    float coe = m_Param_value["VRtg"]/m_Param_value["VRtgSnd"] * (m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]); //一次值 二次值 变比

    
    // 配变负载率 
    m_LoadRate = m_LoadRate_sum / m_LoadRate_num *coe; 
    m_LoadRate_phsA = m_LoadRate_phsA_sum / m_LoadRate_phsA_num ;
    m_LoadRate_phsB = m_LoadRate_phsB_sum / m_LoadRate_phsB_num ;
    m_LoadRate_phsC = m_LoadRate_phsC_sum / m_LoadRate_phsC_num ;
    mskprintf("m_LoadRate: %f, m_LoadRate_phsA: %f,m_LoadRate_phsB: %f,m_LoadRate_phsC:%f\n",m_LoadRate,m_LoadRate_phsA,m_LoadRate_phsB,m_LoadRate_phsC);

    // 周期内配变负载率 
    m_MaxLoadRate = m_MaxLoadRate > m_LoadRate ? m_MaxLoadRate : m_LoadRate; 
    m_MaxLoadRate_phsA = m_MaxLoadRate_phsA > m_LoadRate_phsA ? m_MaxLoadRate_phsA : m_LoadRate_phsA;
    m_MaxLoadRate_phsB = m_MaxLoadRate_phsB > m_LoadRate_phsB ? m_MaxLoadRate_phsB : m_LoadRate_phsB;
    m_MaxLoadRate_phsC = m_MaxLoadRate_phsC > m_LoadRate_phsC ? m_MaxLoadRate_phsC : m_LoadRate_phsC;
//    mskprintf("m_MaxLoadRate: %f, m_MaxLoadRate_phsA: %f,m_MaxLoadRate_phsB: %f,m_MaxLoadRate_phsC:%f\n",m_MaxLoadRate,m_MaxLoadRate_phsA,m_MaxLoadRate_phsB,m_MaxLoadRate_phsC);

    //当前最大负荷
    float MaxLoad = m_MaxLoadRate * m_Param_value["DeviceLoad"] ; //1次值
    float MaxLoad_phsA = m_MaxLoadRate_phsA * m_Param_value["DeviceLoad"];//1次值
    float MaxLoad_phsB = m_MaxLoadRate_phsB * m_Param_value["DeviceLoad"];//1次值
    float MaxLoad_phsC = m_MaxLoadRate_phsC * m_Param_value["DeviceLoad"];//1次值
 //   mskprintf("MaxLoad: %f, MaxLoad_phsA: %f,MaxLoad_phsB: %f,MaxLoad_phsC:%f\n",MaxLoad,MaxLoad_phsA,MaxLoad_phsB,MaxLoad_phsC);
 
    // 周期内最大负荷
    m_MaxLoad = m_MaxLoad > MaxLoad?m_MaxLoad : MaxLoad;
    m_MaxLoad_phsA = m_MaxLoad_phsA > MaxLoad_phsA?m_MaxLoad_phsA : MaxLoad_phsA;
    m_MaxLoad_phsB = m_MaxLoad_phsB > MaxLoad_phsB?m_MaxLoad_phsB : MaxLoad_phsB;
    m_MaxLoad_phsC = m_MaxLoad_phsC > MaxLoad_phsC?m_MaxLoad_phsC : MaxLoad_phsC;
 //   mskprintf("m_MaxLoad: %f, m_MaxLoad_phsA: %f,m_MaxLoad_phsB: %f,m_MaxLoad_phsC:%f\n",m_MaxLoad,m_MaxLoad_phsA,m_MaxLoad_phsB,m_MaxLoad_phsC);

    // 可开放容量
    m_TotResLoad   = m_Param_value["DeviceLoad"] - m_MaxLoad;
    m_ResLoad_phsA = m_Param_value["DeviceLoad"]/3.0 - m_MaxLoad_phsA;
    m_ResLoad_phsB = m_Param_value["DeviceLoad"]/3.0 - m_MaxLoad_phsB;
    m_ResLoad_phsC = m_Param_value["DeviceLoad"]/3.0 - m_MaxLoad_phsC;
  //  mskprintf("m_TotResLoad: %f, m_ResLoad_phsA: %f,m_ResLoad_phsB: %f,m_ResLoad_phsC:%f\n",m_TotResLoad,m_ResLoad_phsA,m_ResLoad_phsB,m_ResLoad_phsC);
  

	//将最新数据写入文件
    WriteJsonFile_preData();
    return true;
}


//计算不平衡度
bool CParamManager::Unpack_pdAnalyzerImb()
{
    CIIAutoMutex mutex(&m_cs);

    mskprintf("交采数据更新，计算不平衡度\n");
    mskprintf("交采原始数据\n");

    // 打印变量信息
    std::map<std::string, float>::iterator iter;
    for (iter = CParamManager::CreateInstance().m_ADC_value.begin(); iter != CParamManager::CreateInstance().m_ADC_value.end(); iter++)
    {
        std::string name = iter->first;
        if(name.compare("PhV_phsA") == 0|| name.compare("PhV_phsB") == 0||name.compare("PhV_phsC") == 0
            || name.compare("A_phsA") == 0||name.compare("A_phsB") == 0
            ||name.compare("A_phsC") == 0||name.compare("A_phsN") == 0)
        {
            mskprintf("name =  %s , value = %f\n", iter->first.c_str(), iter->second);
        }        
    } 
    mskprintf("\n");

    
    float va = 0.0f;
    float vb = 0.0f;
    float vc = 0.0f;
    //零序和负序不平衡
    va = m_ADC_value["SeqA_c0"];
    vb = m_ADC_value["SeqA_c1"];
    vc = m_ADC_value["SeqA_c2"];
    if(vb > 0.000001f&&va>0.000001f&&vc>0.000001f)
    {
        mskprintf("aaaaa va:%f vb:%f vc:%f\n",va,vb,vc);
        m_pdAnalyzer_value["ImbA0"] = va/vc;
        m_pdAnalyzer_value["ImbA2"] = vc/vb;
        IEC_LOG_RECORD(eRunType,"零序和负序不平衡 零序电流: %f ,正序电流: %f,负序电流: %f,ImbA0:%f,ImbA2:%f",va, vb, vc,m_pdAnalyzer_value["ImbA0"],m_pdAnalyzer_value["ImbA2"]);
    }
    //mskprintf("aaaaa SeqA_c0: %f, SeqA_c1: %f, SeqA_c2: %f\n",m_ADC_value["SeqA_c0"],m_ADC_value["SeqA_c1"],m_ADC_value["SeqA_c2"]);
    mskprintf("aaaaaaImbA0:%f ImbA2:%f\n", m_pdAnalyzer_value["ImbA0"] , m_pdAnalyzer_value["ImbA2"] );
    va = m_ADC_value["SeqV_c0"];
    vb = m_ADC_value["SeqV_c1"];
    vc = m_ADC_value["SeqV_c2"];
     if(vb > 0.000001f&&va>0.000001f&&vc>0.000001f)
    {
        mskprintf("aaaaaa va:%f vb:%f vc:%f\n",va,vb,vc);
        m_pdAnalyzer_value["ImbV0"] = va/vc;  
        m_pdAnalyzer_value["ImbV2"] = vc/vb;
        IEC_LOG_RECORD(eRunType,"零序和负序不平衡 零序电压: %f ,正序电压: %f,负序电压: %f,ImbV0:%f,ImbV2:%f",va, vb, vc,m_pdAnalyzer_value["ImbV0"],m_pdAnalyzer_value["ImbV2"]);
    }
    mskprintf("aaaaaaImbV0:%f ImbV2:%f\n", m_pdAnalyzer_value["ImbV0"] , m_pdAnalyzer_value["ImbV2"] );
    // 三相电压不平衡度
    va = m_ADC_value["PhV_phsA"];
    vb = m_ADC_value["PhV_phsB"];
    vc = m_ADC_value["PhV_phsC"];

    //float ImbNgV = calculate_imbalance_v(va, vb, vc);  

    // 电压不平衡度根据电流不平衡度计算。     相电流不平衡度=（最大相电流IMAX-最小相电流IMIN）/最大相电流IMAX×100%
    float ImbNgV = (std::max(std::max(va, vb), vc) -std::min(std::min(va, vb), vc) )/ std::max(std::max(va, vb), vc) ;   

    m_pdAnalyzer_value["ImbNgV"] = ImbNgV;

   	IEC_LOG_RECORD(eRunType,"电压变化 Va: %f ,Va: %f,Va: %f,",va, vb, vc);
    
	
    mskprintf("电压不平衡度: va: %f ,vb: %f,vc: %f,ImbNgV:%f,PhvImb_strVal:%f\n",va, vb, vc,ImbNgV,m_Param_value["PhvImb_strVal"]);
    mskprintf("m_pdAnalyzer_value_YX[ImbNgV_Alm]: %d\n",m_pdAnalyzer_value_YX["ImbNgV_Alm"]); 
    //电压不平衡遥信事件
    if(More(ImbNgV , m_Param_value["PhvImb_strVal"]))
    {   
		IEC_LOG_RECORD(eRunType,"电压不平衡越线触发 电压不平衡度：%f ",ImbNgV);
        //mskprintf("ImbNgV_Alm: More\n");             
        if(m_pdAnalyzer_value_YX["ImbNgV_Alm"]==0)
        {
            m_pdAnalyzer_value_YX["ImbNgV_Alm"]=1;   
			IEC_LOG_RECORD(eRunType,"电压不平衡 ImbNgV_Alm: %f ",m_pdAnalyzer_value_YX["ImbNgV_Alm"]);
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ImbNgV_Alm");
            m_voltage_time.ImbNgV_Num_Day++;
            m_voltage_time.ImbNgA_Num_Mon++;
        }
    }
    else
    {         
        mskprintf("ImbNgV_Alm: Less\n");  
        if(m_pdAnalyzer_value_YX["ImbNgV_Alm"]==1)
        {
            m_pdAnalyzer_value_YX["ImbNgV_Alm"]=0;
			IEC_LOG_RECORD(eRunType,"电压不平衡 ImbNgV_Alm: %f ",m_pdAnalyzer_value_YX["ImbNgV_Alm"]);
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ImbNgV_Alm");
        }
    }

    mskprintf("m_pdAnalyzer_value_YX[ImbNgV_Alm]: %d\n",m_pdAnalyzer_value_YX["ImbNgV_Alm"]);


    // 三相电流不平衡度
    va = m_ADC_value["A_phsA"];
    vb = m_ADC_value["A_phsB"];
    vc = m_ADC_value["A_phsC"];
	//IEC_LOG_RECORD(eRunType,"电流变化 Ia: %f ,Ia: %f,Ia: %f,",va, vb, vc);
    //相电流不平衡度=（最大相电流IMAX-最小相电流IMIN）/最大相电流IMAX×100%
    float ImbNgA = (std::max(std::max(va, vb), vc) -std::min(std::min(va, vb), vc) )/ std::max(std::max(va, vb), vc) ;   
    m_pdAnalyzer_value["ImbNgA"] = ImbNgA;
    //Imb_db 三相电流均小于“相电流不平衡启动条件” （变压器额定电流的相对值）时，相电流不平衡度=0%。
    if(va < m_Param_value["Imb_db"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])  
    && vb < m_Param_value["Imb_db"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])
    && vc < m_Param_value["Imb_db"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]))
    {
        m_pdAnalyzer_value["ImbNgA"] = 0.0; 
    }
    
    IEC_LOG_RECORD(eRunType,"电流不平衡度: Ia: %f ,Ib: %f,Ic: %f,ImbNgA:%f,Imb_db:%f\n",va, vb, vc,ImbNgA,m_Param_value["Imb_db"]);
    mskprintf("电流不平衡度: va: %f ,vb: %f,vc: %f,ImbNgA:%f,Imb_db:%f\n",va, vb, vc,ImbNgA,m_Param_value["Imb_db"]);
    
    

    //电流不平衡遥信事件
    if(More(ImbNgA , m_Param_value["ImbA_Lim"]) 
        &&( (More(va,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
        || (Equ(va,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
        || (More(vb,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
        || (Equ(vb,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
        || (More(vc,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
        || (Equ(vc,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))))
    {
        mskprintf("ImbNgA_Alm: %d\n",m_pdAnalyzer_value_YX["ImbNgA_Alm"]);
        if(m_pdAnalyzer_value_YX["ImbNgA_Alm"]==0) 
        {
			if(!m_T_Imb_YX.IsStart())
            {
                m_T_Imb_YX.SetParam(1000*m_Param_value["ImbA_Dly"]); // 三相负荷不平衡告警时间
                m_T_Imb_YX.StartCount();
                m_voltage_time.ImbNgA_Num_Day++;  //三相电流日累计次数加一
                m_voltage_time.ImbNgA_Num_Mon++;
            }
            //m_pdAnalyzer_value_YX["ImbNgA_Alm"]=1;
            //CTaskManager::CreateInstance().MqttPackNotificationData_2("ImbNgA_Alm");
        }        
    }
    else
    {
        mskprintf("m_pdAnalyzer_value_YX[ImbNgA_Alm]: %d\n",m_pdAnalyzer_value_YX["ImbNgA_Alm"]);
        if(m_pdAnalyzer_value_YX["ImbNgA_Alm"]==1)
        {
            m_pdAnalyzer_value_YX["ImbNgA_Alm"]=0;
			IEC_LOG_RECORD(eRunType,"电流不平衡 ImbNgA_Alm: %f ",m_pdAnalyzer_value_YX["ImbNgA_Alm"]);
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ImbNgA_Alm");
        }        
		else
        {
            if(m_T_Imb_YX.IsStart())
            {
                
                m_T_Imb_YX.StopCount();
            }
        }  
    }

    bool result = (More(va,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
    || (Equ(va,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
    || (More(vb,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
    || (Equ(vb,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
    || (More(vc,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
    || (Equ(vc,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])));

    //轻度电流不平衡 
    if(More(ImbNgA , m_Param_value["ImbA_mild_Lim"]) && result)
    {
        mskprintf("ImbNgA_mild_Alm:%d\n",m_pdAnalyzer_value_YX["ImbNgA_mild_Alm"]);
        if(m_pdAnalyzer_value_YX["ImbNgA_mild_Alm"] == 0)
        {
            if(!m_T_Imb_mild_YX.IsStart())
            {
                m_T_Imb_mild_YX.SetParam(1000*m_Param_value["ImbA_mild_Dly"]); 
                m_T_Imb_mild_YX.StartCount();
            }
        }
    }
    else
    {
        mskprintf("ImbNgA_mild_Alm: %d\n",m_pdAnalyzer_value_YX["ImbNgA_mild_Alm"]);
        if(m_pdAnalyzer_value_YX["ImbNgA_mild_Alm"]==1)
        {
            m_pdAnalyzer_value_YX["ImbNgA_mild_Alm"]=0;
			IEC_LOG_RECORD(eRunType,"电流不平衡 ImbNgA_mild_Alm: %f ",m_pdAnalyzer_value_YX["ImbNgA_mild_Alm"]);
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ImbNgA_mild_Alm");
        }
        else
        {
            if(m_T_Imb_mild_YX.IsStart())
            {
                m_T_Imb_mild_YX.StopCount();
            }
        }
    }

    //中度电流不平衡
    if(More(ImbNgA , m_Param_value["ImbA_middle_Lim"]) && result)
    {
        mskprintf("ImbNgA_middle_Alm:%d\n",m_pdAnalyzer_value_YX["ImbNgA_middle_Alm"]);
        if(m_pdAnalyzer_value_YX["ImbNgA_middle_Alm"] == 0)
        {
            if(!m_T_Imb_middle_YX.IsStart())
            {
                m_T_Imb_middle_YX.SetParam(1000*m_Param_value["ImbA_middle_Dly"]); 
                m_T_Imb_middle_YX.StartCount();
            }
        }
    }
    else
    {
        mskprintf("ImbNgA_middle_Alm: %d\n",m_pdAnalyzer_value_YX["ImbNgA_middle_Alm"]);
        if(m_pdAnalyzer_value_YX["ImbNgA_middle_Alm"]==1)
        {
            m_pdAnalyzer_value_YX["ImbNgA_middle_Alm"]=0;
			IEC_LOG_RECORD(eRunType,"电流不平衡 ImbNgA_middle_Alm: %f ",m_pdAnalyzer_value_YX["ImbNgA_middle_Alm"]);
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ImbNgA_middle_Alm");
        }
        else
        {
            if(m_T_Imb_middle_YX.IsStart())
            {
                m_T_Imb_middle_YX.StopCount();
            }
        }
    }

    //重度电流不平衡
    if(More(ImbNgA , m_Param_value["ImbA_severe_Lim"]) && result)
    {
        mskprintf("ImbNgA_severe_Alm:%d\n",m_pdAnalyzer_value_YX["ImbNgA_severe_Alm"]);
        if(m_pdAnalyzer_value_YX["ImbNgA_severe_Alm"] == 0)
        {
            if(!m_T_Imb_severe_YX.IsStart())
            {
                m_T_Imb_severe_YX.SetParam(1000*m_Param_value["ImbA_severe_Dly"]); 
                m_T_Imb_severe_YX.StartCount();
            }
        }
    }
    else
    {
        mskprintf("ImbNgA_severe_Alm: %d\n",m_pdAnalyzer_value_YX["ImbNgA_severe_Alm"]);
        if(m_pdAnalyzer_value_YX["ImbNgA_severe_Alm"]==1)
        {
            m_pdAnalyzer_value_YX["ImbNgA_severe_Alm"]=0;
			IEC_LOG_RECORD(eRunType,"电流不平衡 ImbNgA_severe_Alm: %f ",m_pdAnalyzer_value_YX["ImbNgA_severe_Alm"]);
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ImbNgA_severe_Alm");
        }
        else
        {
            if(m_T_Imb_severe_YX.IsStart())
            {
                m_T_Imb_severe_YX.StopCount();
            }
        }
    }

    

    //0线电流不平衡度  零线电流不平衡度=零线电流/配电变压器额定电流×100%    
    //float ImbNgA_Alm_phsN = m_ADC_value["A_phsN"]/(m_Param_value["RtgA"] *1000)*(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]);
    float ImbNgA_Alm_phsN = m_ADC_value["A_phsC"]/(m_Param_value["RtgA"] *1000)*(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]);
    mskprintf("A_phsN:%f, RtgA: %f ,zeroLineCTPrimaryRating: %f, zeroLineCTSecondaryRating:%f \n",m_ADC_value["A_phsN"], m_Param_value["RtgA"],m_Param_value["ARtg"],m_Param_value["ARtgSnd"]);
    mskprintf("0线电流不平衡度: ImbNgA_Alm_phsN: %f ,限值ZPCImb_strVal: %f\n",ImbNgA_Alm_phsN, m_Param_value["ImbNgA_phsN_Lim"]);
    //m_pdAnalyzer_value["ImbNgA_Alm_phsN"] = ImbNgA_Alm_phsN;
    
    //0线电流不平衡遥信事件
    if(ImbNgA_Alm_phsN >m_Param_value["ImbNgA_phsN_Lim"])
    {
        if(m_pdAnalyzer_value_YX["ImbNgA_Alm_phsN"]== 0)
        {
			if(!m_T_ZImb_YX.IsStart())
            {
                m_T_ZImb_YX.SetParam(1000*m_Param_value["ImbA_Dly"]); // 三相负荷不平衡告警时间
                m_T_ZImb_YX.StartCount();
            }
            //m_pdAnalyzer_value_YX["ImbNgA_Alm_phsN"]=1;
            //CTaskManager::CreateInstance().MqttPackNotificationData_2("ImbNgA_Alm_phsN");
        }
    }
    else
    {
        if(m_pdAnalyzer_value_YX["ImbNgA_Alm_phsN"]== 1)
        {
            m_pdAnalyzer_value_YX["ImbNgA_Alm_phsN"]=0;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ImbNgA_Alm_phsN");
		}
        else
        {
            if(m_T_ZImb_YX.IsStart())
            {
                m_T_ZImb_YX.StopCount();
            }
        }
    }

    // 三相负荷不平衡度
    va = m_ADC_value["A_phsA"];
    vb = m_ADC_value["A_phsB"];
    vc = m_ADC_value["A_phsC"];
    //float ImbNgLoad = calculate_imbalance(va, vb, vc);
    //相负荷不平衡度=（最大相电流IMAX-最小相电流IMIN）/最大相电流IMAX×100%
    float ImbNgLoad = (std::max(std::max(va, vb), vc) -std::min(std::min(va, vb), vc) )/ std::max(std::max(va, vb), vc) ;   
    if(Equ(std::max(std::max(va, vb), vc) , 0 ))
    {
        ImbNgLoad = 0;
    }


    m_pdAnalyzer_value["ImbNgLoad"] = ImbNgLoad;
    mskprintf("三相负荷不平衡度: va: %f ,vb: %f,vc: %f, ImbNgLoad:%f, ImbA_Lim:%f\n",va, vb, vc,ImbNgLoad , m_Param_value["ImbA_Lim"]);
    mskprintf("三相负荷不平衡度: m_T.IsStart(): %d ImbA_Dly, %f\n",m_T.IsStart(),m_Param_value["ImbA_Dly"]);
    //负荷不平衡遥信事件
    if(More(ImbNgLoad , m_Param_value["ImbA_Lim"]) 
        &&( (More(va,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
        || (Equ(va,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
        || (More(vb,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
        || (Equ(vb,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
        || (More(vc,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
        || (Equ(vc,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))))
    {    
        mskprintf("ImbNgLoad_Alm: %d\n",m_pdAnalyzer_value_YX["ImbNgLoad_Alm"]);
        IEC_LOG_RECORD(eRunType,"三相负荷不平衡度: va: %f ,vb: %f,vc: %f, ImbNgLoad:%f, ImbNgLoad_Alm:%f\n",va, vb, vc,ImbNgLoad , m_pdAnalyzer_value_YX["ImbNgLoad_Alm"]);

        if(m_pdAnalyzer_value_YX["ImbNgLoad_Alm"]==0)
        {            
            if(!m_T.IsStart())
            {
                m_T.SetParam(1000*m_Param_value["ImbA_Dly"]); // 三相负荷不平衡告警时间
                m_T.StartCount();
            }
        }        
    }
    else
    {
        mskprintf("exit::::::::::::::: %d\n",m_pdAnalyzer_value_YX["ImbNgLoad_Alm"]);

        if(m_pdAnalyzer_value_YX["ImbNgLoad_Alm"]==1)
        {
            m_pdAnalyzer_value_YX["ImbNgLoad_Alm"]=0;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ImbNgLoad_Alm");
        }
        else
        {
            if(m_T.IsStart())  
            {
                m_T.StopCount();
            }
        }
    }
	//m_pdAnalyzer_value["ImbNgV"] = m_pdAnalyzer_value["ImbNgV"]*100;
	//m_pdAnalyzer_value["ImbNgA"] = m_pdAnalyzer_value["ImbNgA"]*100;//licong
	CTaskManager::CreateInstance().MqttPackSetRealTimeData();
    CTaskManager::CreateInstance().MqttPack_pdAnalyzerImb();
    return true;
}

float CParamManager::calculate_imbalance_v(float a, float b, float c)
{
    // 计算平均值
    float aver = (a + b + c) / 3.0;

    // 求各相与平均的插值
    float da = abs(a - b);
    float db = abs(b - c);
    float dc = abs(c - a);

    // 计算不平衡度
    float ret = std::max(std::max(da, db), dc) / aver;
    return ret;
}

float CParamManager::calculate_imbalance(float a, float b, float c)
{
    // 计算平均值
    float aver = (a + b + c) / 3.0;

    // 求各相与平均的插值
    float da = abs(a - aver);
    float db = abs(b - aver);
    float dc = abs(c - aver);

    // 计算不平衡度
    float ret = std::max(std::max(da, db), dc) / aver;
    return ret;
}

int CParamManager::voltage_limit_analysis(float interval_t,std::string strtime)
{
    mskprintf("计算电压值 interval_t:%f \n",interval_t);    
    
    //电压日总值  
    m_Tol_PhV_Day_phsA += m_ADC_value["PhV_phsA"];
    m_Count_PhV_Day_phsA++;
    m_pdAnalyzer_value["PhVAvDay_phsA"] = m_Tol_PhV_Day_phsA/m_Count_PhV_Day_phsA;//日平均电压

    m_Tol_PhV_Day_phsB += m_ADC_value["PhV_phsB"];
    m_Count_PhV_Day_phsB++;
    m_pdAnalyzer_value["PhVAvDay_phsB"] = m_Tol_PhV_Day_phsB/m_Count_PhV_Day_phsB;//日平均电压

    m_Tol_PhV_Day_phsC += m_ADC_value["PhV_phsC"];
    m_Count_PhV_Day_phsC++;
    m_pdAnalyzer_value["PhVAvDay_phsC"] = m_Tol_PhV_Day_phsC/m_Count_PhV_Day_phsC;//日平均电压

    //电压月总值
    m_Tol_PhV_Mon_phsA += m_ADC_value["PhV_phsA"];
    m_Count_PhV_Mon_phsA++;
    m_pdAnalyzer_value["PhVAvMon_phsA"] = m_Tol_PhV_Mon_phsA/m_Count_PhV_Mon_phsA;//月平均电压

    m_Tol_PhV_Mon_phsB += m_ADC_value["PhV_phsB"];
    m_Count_PhV_Mon_phsB++;
    m_pdAnalyzer_value["PhVAvMon_phsB"] = m_Tol_PhV_Mon_phsB/m_Count_PhV_Mon_phsB;//月平均电压

    m_Tol_PhV_Mon_phsC += m_ADC_value["PhV_phsC"];
    m_Count_PhV_Mon_phsC++;
    m_pdAnalyzer_value["PhVAvMon_phsC"] = m_Tol_PhV_Mon_phsC/m_Count_PhV_Mon_phsC;//月平均电压

    //日最大最小电压
    if(m_pdAnalyzer_value["PhVMaxDay_phsA"] < m_ADC_value["PhV_phsA"])
    {
        m_pdAnalyzer_value["PhVMaxDay_phsA"] = m_ADC_value["PhV_phsA"];
        m_PhVMaxDay_phsA_TIME = strtime;
    }    
    if(m_pdAnalyzer_value["PhVMaxDay_phsB"] < m_ADC_value["PhV_phsB"])
    {
        m_pdAnalyzer_value["PhVMaxDay_phsB"] = m_ADC_value["PhV_phsB"];
        m_PhVMaxDay_phsB_TIME = strtime;
    }
    if(m_pdAnalyzer_value["PhVMaxDay_phsC"] < m_ADC_value["PhV_phsC"])
    {
        m_pdAnalyzer_value["PhVMaxDay_phsC"] = m_ADC_value["PhV_phsC"];
        m_PhVMaxDay_phsC_TIME = strtime;
    }
    if(m_pdAnalyzer_value["PhVMinDay_phsA"] > m_ADC_value["PhV_phsA"])
    {
        m_pdAnalyzer_value["PhVMinDay_phsA"] = m_ADC_value["PhV_phsA"];
        m_PhVMinDay_phsA_TIME = strtime;
    }
    if(m_pdAnalyzer_value["PhVMinDay_phsB"] > m_ADC_value["PhV_phsB"])
    {
        m_pdAnalyzer_value["PhVMinDay_phsB"] = m_ADC_value["PhV_phsB"];
        m_PhVMinDay_phsB_TIME = strtime;
    }
    if(m_pdAnalyzer_value["PhVMinDay_phsC"] > m_ADC_value["PhV_phsC"])
    {
        m_pdAnalyzer_value["PhVMinDay_phsC"] = m_ADC_value["PhV_phsC"];
        m_PhVMinDay_phsC_TIME = strtime;
    }
    //月最大最小电压
    if(m_pdAnalyzer_value["PhVMaxMon_phsA"] < m_ADC_value["PhV_phsA"])
    {
        m_pdAnalyzer_value["PhVMaxMon_phsA"] = m_ADC_value["PhV_phsA"];
        m_PhVMaxMon_phsA_TIME = strtime;
    }    
    if(m_pdAnalyzer_value["PhVMaxMon_phsB"] < m_ADC_value["PhV_phsB"])
    {
        m_pdAnalyzer_value["PhVMaxMon_phsB"] = m_ADC_value["PhV_phsB"];
        m_PhVMaxMon_phsB_TIME = strtime;
    }
    if(m_pdAnalyzer_value["PhVMaxMon_phsC"] < m_ADC_value["PhV_phsC"])
    {
        m_pdAnalyzer_value["PhVMaxMon_phsC"] = m_ADC_value["PhV_phsC"];
        m_PhVMaxMon_phsC_TIME = strtime;
    }
    if(m_pdAnalyzer_value["PhVMinMon_phsA"] > m_ADC_value["PhV_phsA"])
    {
        m_pdAnalyzer_value["PhVMinMon_phsA"] = m_ADC_value["PhV_phsA"];
        m_PhVMinMon_phsA_TIME = strtime;
    }
    if(m_pdAnalyzer_value["PhVMinMon_phsB"] > m_ADC_value["PhV_phsB"])
    {
        m_pdAnalyzer_value["PhVMinMon_phsB"] = m_ADC_value["PhV_phsB"];
        m_PhVMinMon_phsB_TIME = strtime;
    }
    if(m_pdAnalyzer_value["PhVMinMon_phsC"] > m_ADC_value["PhV_phsC"])
    {
        m_pdAnalyzer_value["PhVMinMon_phsC"] = m_ADC_value["PhV_phsC"];
        m_PhVMinMon_phsC_TIME = strtime;
    }
    mskprintf("PTUV_lim_setVal:%f PTOV_lim_setVal:%f std_voltage:%f \n",m_Param_value["PTUV_lim_setVal"],m_Param_value["PTOV_lim_setVal"],m_Param_value["std_voltage"]);

    //越电压上线时间
    //台体下发参数PTOV_lim_setVal为242 实际电压值
     //if(m_ADC_value["PhV_phsA"] > m_Param_value["PTOV_lim_setVal"] * m_Param_value["std_voltage"])
    if(m_ADC_value["PhV_phsA"] > m_Param_value["PTOV_lim_setVal"])
    {
        m_voltage_time.PTOV_Tm_phsA_S += interval_t;
    }
    //越电压下线时间
    //else if(m_ADC_value["PhV_phsA"] < m_Param_value["PTUV_lim_setVal"] * m_Param_value["std_voltage"])
    else if(m_ADC_value["PhV_phsA"] < m_Param_value["PTUV_lim_setVal"] )
    {
        m_voltage_time.PTUV_Tm_phsA_S += interval_t;
    }
    //合格时间
    else
    {
        m_voltage_time.PASS_Tm_phsA_S += interval_t;
    }
    mskprintf("PTOV_Tm_phsA_S:%f PTUV_Tm_phsA_S:%f \n",m_voltage_time.PTOV_Tm_phsA_S,m_voltage_time.PTUV_Tm_phsA_S);
    //越电压上线时间
    //if(m_ADC_value["PhV_phsB"] > m_Param_value["PTOV_lim_setVal"] * m_Param_value["std_voltage"])
    if(m_ADC_value["PhV_phsB"] > m_Param_value["PTOV_lim_setVal"] )
    {
        m_voltage_time.PTOV_Tm_phsB_S += interval_t;
    }
    //越电压下线时间
    // else if(m_ADC_value["PhV_phsB"] < m_Param_value["PTUV_lim_setVal"] * m_Param_value["std_voltage"])
    else if(m_ADC_value["PhV_phsB"] < m_Param_value["PTUV_lim_setVal"] )
    {
        m_voltage_time.PTUV_Tm_phsB_S += interval_t;
    }
    //合格时间
    else
    {
        m_voltage_time.PASS_Tm_phsB_S += interval_t;
    }
    mskprintf("PTOV_Tm_phsB_S:%f PTUV_Tm_phsA_S:%f \n",m_voltage_time.PTUV_Tm_phsB_S,m_voltage_time.PTUV_Tm_phsB_S);
    //越电压上线时间
   // if(m_ADC_value["PhV_phsC"] > m_Param_value["PTOV_lim_setVal"] * m_Param_value["std_voltage"])
    if(m_ADC_value["PhV_phsC"] > m_Param_value["PTOV_lim_setVal"] )
    {
        m_voltage_time.PTOV_Tm_phsC_S += interval_t;
    }
    //越电压下线时间
   // else if(m_ADC_value["PhV_phsC"] < m_Param_value["PTUV_lim_setVal"] * m_Param_value["std_voltage"])
    else if(m_ADC_value["PhV_phsC"] < m_Param_value["PTUV_lim_setVal"] )
    {
        m_voltage_time.PTUV_Tm_phsC_S += interval_t;
    }
    //合格时间
    else
    {
        m_voltage_time.PASS_Tm_phsC_S += interval_t;
    }
    return 0;
}

int CParamManager::voltage_yx_analysis()
{
    mskprintf("计算遥信事件\n"); 
    
    voltage_yx_analysis_PTOV_Alm();         //过压告警 持续时间
    voltage_yx_analysis_SeqOV_Alm();        //遥信状态判断-零序过压告警
    voltage_yx_analysis_PTUV_Alm();         //欠压告警 持续时间
    voltage_yx_analysis_PTUV_Loss_Alm();    //矢压告警/停电 持续时间
    voltage_yx_analysis_PowerOn_Alm();      //有压告警/来电 持续时间
    voltage_yx_analysis_PTUV_Open_Alm();    //断相

    current_yx_analysis_PTUC_Alm();         //遥信状态判断-电源矢流
    current_yx_analysis_PTUC_Open_Alm();    //遥信状态判断-电源断流
    current_yx_analysis_PTOA_Alm();         //遥信状态判断-电流过流
    current_yx_analysis_SeqOA_Alm();        //遥信状态判断-零序过流
    current_yx_analysis_Ovld_Alm();         //遥信状态判断-负荷越线
    current_yx_analysis_PTOC_Hvld_Alm();    //遥信状态判断-配变重载
    current_yx_analysis_PTOC_Ovld_Alm();    //遥信状态判断-配变过载
    current_yx_analysis_Res_Alm();          //剩余电流超限
    PT_yx_analysis_PTUPF_Alm();             //功率因数越限
    current_yx_analysis_DIP_strVal_Alm();//电压暂降
    voltage_yx_analysis_PhsSeqV_Alm();      //电压逆向序
    current_yx_analysis_ThdPhV_Op_Alm();    //电压谐波越限告警
    current_yx_analysis_ThdA_Op_Alm();      //电流谐波越限告警
    current_yx_analysis_TotHzOfs_Alm();     //频率越限告警    

   	m_pdAnalyzer_value["LoadRate"]  = m_ADC_value["TotVA"]*(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]) / m_Param_value["DeviceLoad"];
		
	 mskprintf("LoadRate : %f DeviceLoad : %f \n",m_LoadRate,m_Param_value["DeviceLoad"]);
   	float va = 0,vb =0 , vc = 0,Ia =0 ,Ib =0,Ic =0;
	va = fabs((m_ADC_value["PhV_phsA"]- m_ADC_old_value["PhV_phsA"]));
	vb = fabs((m_ADC_value["PhV_phsB"]- m_ADC_old_value["PhV_phsB"]));
	vc = fabs((m_ADC_value["PhV_phsC"]- m_ADC_old_value["PhV_phsC"]));
	Ia = fabs((m_ADC_value["A_phsA"]- m_ADC_old_value["A_phsA"]));
	Ib = fabs((m_ADC_value["A_phsB"]- m_ADC_old_value["A_phsB"]));
	Ic = fabs((m_ADC_value["A_phsC"]- m_ADC_old_value["A_phsC"]));
 	if(va>5||vb>5||vc>5||Ia>0.1||Ib>0.1||Ic>0.1){  
		if(va>5)	m_ADC_old_value["PhV_phsA"]  = m_ADC_value["PhV_phsA"];
		if(vb>5) m_ADC_old_value["PhV_phsB"]  = m_ADC_value["PhV_phsB"];
		if(vc>5) m_ADC_old_value["PhV_phsC"]  = m_ADC_value["PhV_phsC"];
		if(Ia>0.1)	m_ADC_old_value["A_phsA"]  = m_ADC_value["A_phsA"];
		if(Ib>0.1) m_ADC_old_value["A_phsB"]  = m_ADC_value["A_phsB"];
		if(Ic>0.1) m_ADC_old_value["A_phsC"]  = m_ADC_value["A_phsC"];
	
	 Unpack_pdAnalyzerImb();//计算不平衡度

	IEC_LOG_RECORD(eRunType,"数据变化:Va: %f , Vb: %f ,Vc: %f ,In: %f Ia: %f,Ib: %f Ic :%f",m_ADC_value["PhV_phsA"],m_ADC_value["PhV_phsB"],\
	m_ADC_value["PhV_phsC"],m_ADC_value["A_phsN"],m_ADC_value["A_phsA"],m_ADC_value["A_phsB"],m_ADC_value["A_phsC"]);//licong
	IEC_LOG_RECORD(eRunType,"数据变化:TotPF: %f TotVA : %f m_LoadRate: %f  ImbNgV : %f ImbNgA : %f ",m_ADC_value["TotPF"],\
	m_ADC_value["TotVA"],m_pdAnalyzer_value["LoadRate"],m_pdAnalyzer_value["ImbNgV"],m_pdAnalyzer_value["ImbNgA"]);//licong
	}

    

    //配变负载率


    judge_yx_status();  //判断是否需要上报


    if (exists(SOURCE_FILE)) 
    {
        rotate_logs();
        if (!compress_log()) {
        fprintf(stderr, "Compression failed\n");
        }
        unlink("/run/log/DTAnalyzer/DTAnalyzer.0.log");
    }

    return 0;
}
int CParamManager::voltage_yx_analysis_dev(std::string guid)  //断路器和电表遥信事件
{
    mskprintf("计算端设备和电表遥信事件\n"); 

    //三相电压不平衡---------------------------------------------------------------------------------------------------------------------
    float va = m_dev_value[guid]["PhV_phsA"];
    float vb = m_dev_value[guid]["PhV_phsB"];
    float vc = m_dev_value[guid]["PhV_phsC"];
    float ImbNgV = (std::max(std::max(va, vb), vc) -std::min(std::min(va, vb), vc) )/ std::max(std::max(va, vb), vc) ;   

    m_dev_Analyzer_value[guid]["ImbNgV"] = ImbNgV;

    //电压不平衡遥信事件  
    if(More(ImbNgV , m_Param_value["PhvImb_strVal"]))
    {              
        if(m_dev_Analyzer_value[guid]["ImbNgV_Alm"]==0)
        {
            m_dev_Analyzer_value[guid]["ImbNgV_Alm"]=1; 
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ImbNgV_Alm",guid);
        }
    }
    else
    {         
        if(m_dev_Analyzer_value[guid]["ImbNgV_Alm"]==1)
        {
            m_dev_Analyzer_value[guid]["ImbNgV_Alm"]=0;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ImbNgV_Alm",guid);
        }
    }

    //三相电流不平衡----------------------------------------------------------------------------------------------------------------
    va = m_dev_value[guid]["A_phsA"];
    vb = m_dev_value[guid]["A_phsB"];
    vc = m_dev_value[guid]["A_phsC"];
    //相电流不平衡度=（最大相电流IMAX-最小相电流IMIN）/最大相电流IMAX×100%
    float ImbNgA = (std::max(std::max(va, vb), vc) -std::min(std::min(va, vb), vc) )/ std::max(std::max(va, vb), vc) ;   
    m_dev_Analyzer_value[guid]["ImbNgA"] = ImbNgA;
    //Imb_db 三相电流均小于“相电流不平衡启动条件” （变压器额定电流的相对值）时，相电流不平衡度=0%。
    if(va < m_Param_value["Imb_db"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])  
    && vb < m_Param_value["Imb_db"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])
    && vc < m_Param_value["Imb_db"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]))
    {
        m_dev_Analyzer_value[guid]["ImbNgA"] = 0.0; 
    }
    mskprintf("电流不平衡度: va: %f ,vb: %f,vc: %f,ImbNgA:%f,Imb_db:%f\n",va, vb, vc,ImbNgA,m_Param_value["Imb_db"]);
    
    //电流不平衡遥信事件
    if(More(ImbNgA , m_Param_value["ImbA_Lim"]) 
        &&( (More(va,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
        || (Equ(va,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
        || (More(vb,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
        || (Equ(vb,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
        || (More(vc,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
        || (Equ(vc,m_Param_value["Imb_db"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))))
    {
        if( m_dev_Analyzer_value[guid]["ImbNgA_Alm"]==0) 
        {
            m_dev_Analyzer_value[guid]["ImbNgA_Alm"]=1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ImbNgA_Alm",guid);
        }        
    }
    else
    {
        mskprintf("m_pdAnalyzer_value_YX[ImbNgA_Alm]: %d\n", m_dev_Analyzer_value[guid]["ImbNgA_Alm"]);
        if( m_dev_Analyzer_value[guid]["ImbNgA_Alm"]==1)
        {
            m_dev_Analyzer_value[guid]["ImbNgA_Alm"]=0;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ImbNgA_Alm",guid);
        }          
    }

    //三相电压谐波畸变率

    //三相电流谐波畸变率

    //三相过频检测
    if(1)
    {
        if(m_dev_Analyzer_value[guid]["HzOfs_phsA"] >= m_Param_value["PTOF_Lim"] || m_dev_Analyzer_value[guid]["HzOfs_phsB"] >= m_Param_value["PTOF_Lim"] || m_dev_Analyzer_value[guid]["HzOfs_phsC"] >= m_Param_value["PTOF_Lim"])
        {
            m_dev_time[guid].PTOF_Op_Tm_s ++;
            if(m_dev_time[guid].PTOF_Op_Tm_s > m_Param_value["PTOF_Dly"] && m_dev_Analyzer_value[guid]["PTOF_Alm"] == 0)
            {
                m_dev_Analyzer_value[guid]["PTOF_Alm"] = 1;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Alm",guid);
            }
        }
        else if(m_dev_Analyzer_value[guid]["HzOfs_phsA"] < m_Param_value["RtgHz"] && m_dev_Analyzer_value[guid]["HzOfs_phsB"] < m_Param_value["RtgHz"] && m_dev_Analyzer_value[guid]["HzOfs_phsC"] >= m_Param_value["PTOF_Lim"])
        {
            m_dev_time[guid].PTOF_Op_Tm_s = 0;
            if( m_dev_Analyzer_value[guid]["PTOF_Alm"] == 1)
            {
                m_dev_Analyzer_value[guid]["PTOF_Alm"] = 0;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Alm",guid);
            }
        }
        //A
        if(m_dev_Analyzer_value[guid]["HzOfs_phsA"] >= m_Param_value["PTOF_Lim"]) //A相频率>频率过频定值(50.5HZ)
        {
            m_dev_time[guid].PTOF_Op_phsA_Tm_s++;
            if(m_dev_time[guid].PTOF_Op_Tm_s > m_Param_value["PTOF_Dly"] && m_dev_Analyzer_value[guid]["PTOF_Op_phsA"] == 0)
            {
                m_dev_Analyzer_value[guid]["PTOF_Op_phsA"] = 1;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Op_phsA",guid);
            }
        }
        else if(m_dev_Analyzer_value[guid]["HzOfs_phsA"] < m_Param_value["RtgHz"] ) //A相频率<频率定值(50HZ) //A相过频复归事件
        {
            m_dev_time[guid].PTOF_Op_phsA_Tm_s = 0;
            if( m_dev_Analyzer_value[guid]["PTOF_Op_phsA"] == 1)
            {
                m_dev_Analyzer_value[guid]["PTOF_Op_phsA"] = 0;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Op_phsA",guid);
            }
        }
        //B
        if(m_dev_Analyzer_value[guid]["HzOfs_phsB"] >= m_Param_value["PTOF_Lim"])
        {
            m_dev_time[guid].PTOF_Op_phsB_Tm_s++;
            if(m_dev_time[guid].PTOF_Op_Tm_s > m_Param_value["PTOF_Dly"] && m_dev_Analyzer_value[guid]["PTOF_Op_phsB"] == 0)
            {
                m_dev_Analyzer_value[guid]["PTOF_Op_phsB"] = 1;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Op_phsB",guid);
            }
        }
        else if(m_dev_Analyzer_value[guid]["HzOfs_phsB"] < m_Param_value["RtgHz"] ) 
        {
            m_dev_time[guid].PTOF_Op_phsB_Tm_s = 0;
            if( m_dev_Analyzer_value[guid]["PTOF_Op_phsB"] == 1)
            {
                m_dev_Analyzer_value[guid]["PTOF_Op_phsB"] = 0;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Op_phsB",guid);
            }
        }
        //C
        if(m_dev_Analyzer_value[guid]["HzOfs_phsC"] >= m_Param_value["PTOF_Lim"])
        {     
            m_dev_time[guid].PTOF_Op_phsC_Tm_s++;
            if(m_dev_time[guid].PTOF_Op_Tm_s > m_Param_value["PTOF_Dly"] && m_dev_Analyzer_value[guid]["PTOF_Op_phsC"] == 0)
            {
                m_dev_Analyzer_value[guid]["PTOF_Op_phsC"] = 1;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Op_phsC",guid);
            }
        }
        else if(m_dev_Analyzer_value[guid]["HzOfs_phsC"] < m_Param_value["RtgHz"] ) 
        {
            m_dev_time[guid].PTOF_Op_phsC_Tm_s = 0;
            if( m_dev_Analyzer_value[guid]["PTOF_Op_phsC"] == 1)
            {
                m_dev_Analyzer_value[guid]["PTOF_Op_phsC"] = 0;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Op_phsC",guid);
            }
        }
    }


    //三相欠频检测----------------------------------------------------------------------------------------------------------------------
    if(1)
    {
        if(m_dev_Analyzer_value[guid]["HzOfs_phsA"] <= m_Param_value["PTUF_Lim"] || m_dev_Analyzer_value[guid]["HzOfs_phsB"] <= m_Param_value["PTUF_Lim"] || m_dev_Analyzer_value[guid]["HzOfs_phsC"]  <= m_Param_value["PTUF_Lim"])
        {
            m_dev_time[guid].PTUF_Op_Tm_s++;
            if(m_dev_time[guid].PTOF_Op_Tm_s > m_Param_value["PTUF_Dly"] && m_dev_Analyzer_value[guid]["PTUF_Alm"] == 0)
            {
                m_dev_Analyzer_value[guid]["PTUF_Alm"] = 1;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUF_Alm",guid);
            }
        }
        else if(m_dev_Analyzer_value[guid]["HzOfs_phsA"] > m_Param_value["RtgHz"] && m_dev_Analyzer_value[guid]["HzOfs_phsB"] > m_Param_value["RtgHz"] || m_dev_Analyzer_value[guid]["HzOfs_phsC"] > m_Param_value["RtgHz"])
        {
            m_dev_time[guid].PTUF_Op_Tm_s =0;
            if( m_dev_Analyzer_value[guid]["PTUF_Alm"] == 1)
            {
                m_dev_Analyzer_value[guid]["PTUF_Alm"] = 0;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUF_Alm",guid);
            }
        }
        //A
        if(m_dev_Analyzer_value[guid]["HzOfs_phsA"] <= m_Param_value["PTUF_Lim"]) //A相频率<频率过频定值(49.5HZ)
        {
            m_dev_time[guid].PTUF_Op_phsA_Tm_s++;
            if(m_dev_time[guid].PTOF_Op_Tm_s > m_Param_value["PTUF_Dly"] && m_dev_Analyzer_value[guid]["PTUF_Op_phsA"] == 0)
            {
                m_dev_Analyzer_value[guid]["PTUF_Op_phsA"] = 1;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUF_Op_phsA",guid);
            }
        }
        else if(m_dev_Analyzer_value[guid]["HzOfs_phsA"] > m_Param_value["RtgHz"] ) //A相频率>频率定值(50HZ)
        {
            m_dev_time[guid].PTUF_Op_phsA_Tm_s = 0;
            if( m_dev_Analyzer_value[guid]["PTUF_Op_phsA"] == 1)
            {
                m_dev_Analyzer_value[guid]["PTUF_Op_phsA"] = 0;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUF_Op_phsA",guid);
            }
        }
        //B
        if(m_dev_Analyzer_value[guid]["HzOfs_phsB"] <= m_Param_value["PTUF_Lim"])
        {
            m_dev_time[guid].PTUF_Op_phsB_Tm_s++;
            if(m_dev_time[guid].PTOF_Op_Tm_s > m_Param_value["PTUF_Dly"] && m_dev_Analyzer_value[guid]["PTUF_Op_phsB"] == 0)
            {
                m_dev_Analyzer_value[guid]["PTUF_Op_phsB"] = 1;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUF_Op_phsB",guid);
            }
        }
        else if(m_dev_Analyzer_value[guid]["HzOfs_phsB"] > m_Param_value["RtgHz"])  
        {
            m_dev_time[guid].PTUF_Op_phsB_Tm_s = 0;
            if( m_dev_Analyzer_value[guid]["PTUF_Op_phsB"] == 1)
            {
                m_dev_Analyzer_value[guid]["PTUF_Op_phsB"] = 0;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUF_Op_phsB",guid);
            }
        }
        //C  
        if(m_dev_Analyzer_value[guid]["HzOfs_phsC"]  <= m_Param_value["PTUF_Lim"])
        {     
            m_dev_time[guid].PTUF_Op_phsC_Tm_s++;
            if(m_dev_time[guid].PTOF_Op_Tm_s > m_Param_value["PTUF_Dly"] && m_dev_Analyzer_value[guid]["PTUF_Op_phsC"] == 0)
            {
                m_dev_Analyzer_value[guid]["PTUF_Op_phsC"] = 1;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUF_Op_phsC",guid);
            }
        }
        else if(m_dev_Analyzer_value[guid]["HzOfs_phsC"] > m_Param_value["RtgHz"] ) 
        {
            m_dev_time[guid].PTUF_Op_phsC_Tm_s = 0;
            if( m_dev_Analyzer_value[guid]["PTUF_Op_phsC"] == 1)
            {
                m_dev_Analyzer_value[guid]["PTUF_Op_phsC"] = 0;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUF_Op_phsC",guid);
            }
        }
    }

    //三相过压检测----------------------------------------------------------------------------------------------------------------------

    mskprintf("phV_phsA: %f,  alm: %f \n",m_dev_value[guid]["PhV_phsA"], m_Param_value["PTOV_Lim"] * m_Param_value["std_voltage"]);
   
    //A越电压上线时间 
    if(m_dev_value[guid]["PhV_phsA"] > m_Param_value["PTOV_Lim"] * m_Param_value["std_voltage"])
    {
        m_dev_time[guid].PTOV_Op_phsA_Tm_s++;
        if(m_dev_time[guid].PTOF_Op_Tm_s > m_Param_value["PTOV_Dly"] && m_dev_Analyzer_value[guid]["PTOV_Op_phsA"] == 0)
        {
            m_dev_Analyzer_value[guid]["PTOV_Op_phsA"] = 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Op_phsA",guid);
        }
    }
    else
    {
        m_dev_time[guid].PTOV_Op_phsA_Tm_s = 0;
        if( m_dev_Analyzer_value[guid]["PTOV_Op_phsA"] == 1)
        {
            m_dev_Analyzer_value[guid]["PTOV_Op_phsA"] = 0;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Op_phsA",guid);
        }
    }

    //B越电压上线时间
    if(m_dev_value[guid]["PhV_phsB"] > m_Param_value["PTOV_Lim"] * m_Param_value["std_voltage"])
    {
        m_dev_time[guid].PTOV_Op_phsB_Tm_s++;
        if(m_dev_time[guid].PTOF_Op_Tm_s > m_Param_value["PTOV_Dly"] && m_dev_Analyzer_value[guid]["PTOV_Op_phsB"] == 0)
        {
            m_dev_Analyzer_value[guid]["PTOV_Op_phsB"] = 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Op_phsB",guid);
        }
    }
    else
    {
        m_dev_time[guid].PTOV_Op_phsB_Tm_s = 0;
        if( m_dev_Analyzer_value[guid]["PTOV_Op_phsB"] == 1)
        {
            m_dev_Analyzer_value[guid]["PTOV_Op_phsB"] = 0;  
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Op_phsB",guid);
        }
    }

    //C越电压上线时间
    if(m_dev_value[guid]["PhV_phsC"] > m_Param_value["PTOV_Lim"] * m_Param_value["std_voltage"])
    {
        m_dev_time[guid].PTOV_Op_phsC_Tm_s++;
        if(m_dev_time[guid].PTOF_Op_Tm_s > m_Param_value["PTOV_Dly"] && m_dev_Analyzer_value[guid]["PTOV_Op_phsC"] == 0)
        {
            m_dev_Analyzer_value[guid]["PTOV_Op_phsC"] = 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Op_phsC",guid);
        }
    }
    else
    {
        m_dev_time[guid].PTOV_Op_phsC_Tm_s = 0;
        if( m_dev_Analyzer_value[guid]["PTOV_Op_phsC"] == 1)
        {
            m_dev_Analyzer_value[guid]["PTOV_Op_phsC"] = 0;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Op_phsC",guid);
        }
    }
    
    //过压
    if(m_dev_value[guid]["PhV_phsA"] > m_Param_value["PTOV_Lim"] * m_Param_value["std_voltage"]
        ||m_dev_value[guid]["PhV_phsB"] > m_Param_value["PTOV_Lim"] * m_Param_value["std_voltage"]
        ||m_dev_value[guid]["PhV_phsC"] > m_Param_value["PTOV_Lim"] * m_Param_value["std_voltage"])
    {
        m_dev_time[guid].PTOV_Alm_Tm_s++;
        if(m_dev_time[guid].PTOF_Op_Tm_s > m_Param_value["PTOF_Dly"] && m_dev_Analyzer_value[guid]["PTOV_Alm"] == 0)
        {
            m_dev_Analyzer_value[guid]["PTOV_Alm"] = 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Alm",guid);
        }
    }
    else
    {
        m_dev_time[guid].PTOV_Alm_Tm_s = 0;
        if( m_dev_Analyzer_value[guid]["PTOV_Alm"] == 1)
        {
            m_dev_Analyzer_value[guid]["PTOV_Alm"] = 0;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Alm",guid);
        }
    }
    
    
    //三相欠压检测
    if(1)
    {
        if(m_dev_value[guid]["PhV_phsA"] < m_Param_value["PTUV_Lim"] * m_Param_value["std_voltage"])
        {
            m_dev_time[guid].PTUV_Op_phsA_Tm_s++;
            if(m_dev_time[guid].PTOF_Op_Tm_s > m_Param_value["PTOF_Dly"] && m_dev_Analyzer_value[guid]["PTOF_Alm"] == 0)
            {
                m_dev_Analyzer_value[guid]["PTOF_Alm"] = 1;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Alm");
            }
        }
        else
        {
            m_dev_time[guid].PTUV_Op_phsA_Tm_s = 0;
            if( m_dev_Analyzer_value[guid]["PTOF_Alm"] == 1)
            {
                m_dev_Analyzer_value[guid]["PTOF_Alm"] = 0;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Alm");
            }
        }
    
        //B越电压下线时间
        if(m_dev_value[guid]["PhV_phsB"] < m_Param_value["PTUV_Lim"] * m_Param_value["std_voltage"])
        {
            m_dev_time[guid].PTUV_Op_phsB_Tm_s++;
            if(m_dev_time[guid].PTOF_Op_Tm_s > m_Param_value["PTOF_Dly"] && m_dev_Analyzer_value[guid]["PTOF_Alm"] == 0)
            {
                m_dev_Analyzer_value[guid]["PTOF_Alm"] = 1;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Alm");
            }
        }
        else
        {
            m_dev_time[guid].PTUV_Op_phsB_Tm_s = 0;
            if( m_dev_Analyzer_value[guid]["PTOF_Alm"] == 1)
            {
                m_dev_Analyzer_value[guid]["PTOF_Alm"] = 0;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Alm");
            }
        }
    
        //C越电压下线时间
        if(m_dev_value[guid]["PhV_phsC"] < m_Param_value["PTUV_Lim"] * m_Param_value["std_voltage"])
        {
            m_dev_time[guid].PTUV_Op_phsC_Tm_s++;
            if(m_dev_time[guid].PTOF_Op_Tm_s > m_Param_value["PTOF_Dly"] && m_dev_Analyzer_value[guid]["PTOF_Alm"] == 0)
            {
                m_dev_Analyzer_value[guid]["PTOF_Alm"] = 1;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Alm");
            }
        }
        else
        {
            m_dev_time[guid].PTUV_Op_phsC_Tm_s = 0;
            if( m_dev_Analyzer_value[guid]["PTOF_Alm"] == 1)
            {
                m_dev_Analyzer_value[guid]["PTOF_Alm"] = 0;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Alm");
            }
        }
    
        //低压
        if(m_dev_value[guid]["PhV_phsA"] < m_Param_value["PTUV_Lim"] * m_Param_value["std_voltage"]
            ||m_dev_value[guid]["PhV_phsB"] < m_Param_value["PTUV_Lim"] * m_Param_value["std_voltage"]
            ||m_dev_value[guid]["PhV_phsC"] < m_Param_value["PTUV_Lim"] * m_Param_value["std_voltage"])
        {
            m_dev_time[guid].PTUV_Alm_Tm_s++;
            if(m_dev_time[guid].PTOF_Op_Tm_s > m_Param_value["PTOF_Dly"] && m_dev_Analyzer_value[guid]["PTOF_Alm"] == 0)
            {
                m_dev_Analyzer_value[guid]["PTOF_Alm"] = 1;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Alm");
            }
        }
        else
        {
            m_dev_time[guid].PTUV_Alm_Tm_s=0;
            if( m_dev_Analyzer_value[guid]["PTOF_Alm"] == 1)
            {
                m_dev_Analyzer_value[guid]["PTOF_Alm"] = 0;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Alm");
            }
        }
    }

    //分相、总功率因素越限
    if(1)
    {
        //A功率因数越限
        if(m_dev_value[guid]["PhPF_phsA"] < m_Param_value["PTUPF_Lim"])
        {
            m_dev_time[guid].PTUPF_phsA_Tm_s++;
            if(m_dev_time[guid].PTOF_Op_Tm_s > m_Param_value["PTOF_Dly"] && m_dev_Analyzer_value[guid]["PTOF_Alm"] == 0)
            {
                m_dev_Analyzer_value[guid]["PTOF_Alm"] = 1;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Alm");
            }
        }
        else
        {
            m_dev_time[guid].PTUPF_phsA_Tm_s = 0;
            if( m_dev_Analyzer_value[guid]["PTOF_Alm"] == 1)
            {
                m_dev_Analyzer_value[guid]["PTOF_Alm"] = 0;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Alm");
            }
        }
        
        //B功率因数越限
        if(m_dev_value[guid]["PhPF_phsB"] < m_Param_value["PTUPF_Lim"])
        {
            m_dev_time[guid].PTUPF_phsB_Tm_s++;
            if(m_dev_time[guid].PTOF_Op_Tm_s > m_Param_value["PTOF_Dly"] && m_dev_Analyzer_value[guid]["PTOF_Alm"] == 0)
            {
                m_dev_Analyzer_value[guid]["PTOF_Alm"] = 1;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Alm");
            }
        }
        else
        {
            m_dev_time[guid].PTUPF_phsB_Tm_s = 0;
            if( m_dev_Analyzer_value[guid]["PTOF_Alm"] == 1)
            {
                m_dev_Analyzer_value[guid]["PTOF_Alm"] = 0;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Alm");
            }
        }

        //C功率因数越限
        if(m_dev_value[guid]["PhPF_phsC"] < m_Param_value["PTUPF_Lim"])
        {
            m_dev_time[guid].PTUPF_phsC_Tm_s++;
            if(m_dev_time[guid].PTOF_Op_Tm_s > m_Param_value["PTOF_Dly"] && m_dev_Analyzer_value[guid]["PTOF_Alm"] == 0)
            {
                m_dev_Analyzer_value[guid]["PTOF_Alm"] = 1;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Alm");
            }
        }
        else
        {
            m_dev_time[guid].PTUPF_phsC_Tm_s = 0;
            if( m_dev_Analyzer_value[guid]["PTOF_Alm"] == 1)
            {
                m_dev_Analyzer_value[guid]["PTOF_Alm"] = 0;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Alm");
            }
        }


        //功率因数越限
        if(m_dev_value[guid]["PhPF_phsA"] < m_Param_value["PTUPF_Lim"]
            ||m_dev_value[guid]["PhPF_phsB"] < m_Param_value["PTUPF_Lim"]
            ||m_dev_value[guid]["PhPF_phsC"] < m_Param_value["PTUPF_Lim"])
        {
            m_dev_time[guid].PTUPF_Alm_Tm_s++;
            if(m_dev_time[guid].PTOF_Op_Tm_s > m_Param_value["PTOF_Dly"] && m_dev_Analyzer_value[guid]["PTOF_Alm"] == 0)
            {
                m_dev_Analyzer_value[guid]["PTOF_Alm"] = 1;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Alm");
            }
        }
        else
        {
            m_dev_time[guid].PTUPF_Alm_Tm_s = 0;
            if( m_dev_Analyzer_value[guid]["PTOF_Alm"] == 1)
            {
                m_dev_Analyzer_value[guid]["PTOF_Alm"] = 0;
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Alm");
            }
        }
    }
    

    return 0;
}


//过压告警 持续时间
int CParamManager::voltage_yx_analysis_PTOV_Alm()
{

    mskprintf("phV_phsA: %f,  alm: %f \n",m_ADC_value["PhV_phsA"], m_Param_value["PTOV_Lim"] * m_Param_value["std_voltage"]);
   
    //A越电压上线时间
    if(m_ADC_value["PhV_phsA"] > m_Param_value["PTOV_Lim"] * m_Param_value["std_voltage"])
    {

        m_voltage_time.PTOV_Op_phsA_Tm_s++;
    }
    else
    {
   
        m_voltage_time.PTOV_Op_phsA_Tm_s = 0;
    }

    //B越电压上线时间
    if(m_ADC_value["PhV_phsB"] > m_Param_value["PTOV_Lim"] * m_Param_value["std_voltage"])
    {
        m_voltage_time.PTOV_Op_phsB_Tm_s++;
    }
    else
    {
        m_voltage_time.PTOV_Op_phsB_Tm_s = 0;
    }

    //C越电压上线时间
    if(m_ADC_value["PhV_phsC"] > m_Param_value["PTOV_Lim"] * m_Param_value["std_voltage"])
    {
        m_voltage_time.PTOV_Op_phsC_Tm_s++;
    }
    else
    {
        m_voltage_time.PTOV_Op_phsC_Tm_s = 0;
    }

    //过压
    if(m_ADC_value["PhV_phsA"] > m_Param_value["PTOV_Lim"] * m_Param_value["std_voltage"]
        ||m_ADC_value["PhV_phsB"] > m_Param_value["PTOV_Lim"] * m_Param_value["std_voltage"]
        ||m_ADC_value["PhV_phsC"] > m_Param_value["PTOV_Lim"] * m_Param_value["std_voltage"])
    {
        m_voltage_time.PTOV_Alm_Tm_s++;
    }
    else
    {
        m_voltage_time.PTOV_Alm_Tm_s = 0;
    }
    return 0;
}

//零序过压告警 持续时间
int CParamManager::voltage_yx_analysis_SeqOV_Alm()
{
    mskprintf("SeqV_c0: %f,  Seqc0OV_Lim: %f \n",m_ADC_value["SeqV_c0"], m_Param_value["Seqc0OV_Lim"]);
   
    //零序过压时间
    if(m_ADC_value["SeqV_c0"] > m_Param_value["Seqc0OV_Lim"])
    {
        m_voltage_time.SeqOV_Alm_Tm_s++;
    }
    else
    {
        m_voltage_time.SeqOV_Alm_Tm_s = 0;
    }

    return 0;
}

//欠压告警 持续时间
int CParamManager::voltage_yx_analysis_PTUV_Alm()
{
	mskprintf("phV_phsA: %f,  alm: %f \n",m_ADC_value["PhV_phsA"], m_Param_value["PTUV_Lim"] * m_Param_value["std_voltage"]);
    //A越电压下线时间
    if(m_ADC_value["PhV_phsA"] < m_Param_value["PTUV_Lim"] * m_Param_value["std_voltage"])
    {
        m_voltage_time.PTUV_Op_phsA_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUV_Op_phsA_Tm_s = 0;
    }

    //B越电压下线时间
    if(m_ADC_value["PhV_phsB"] < m_Param_value["PTUV_Lim"] * m_Param_value["std_voltage"])
    {
        m_voltage_time.PTUV_Op_phsB_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUV_Op_phsB_Tm_s = 0;
    }

    //C越电压下线时间
    if(m_ADC_value["PhV_phsC"] < m_Param_value["PTUV_Lim"] * m_Param_value["std_voltage"])
    {
        m_voltage_time.PTUV_Op_phsC_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUV_Op_phsC_Tm_s = 0;
    }

    //低压
    if(m_ADC_value["PhV_phsA"] < m_Param_value["PTUV_Lim"] * m_Param_value["std_voltage"]
        ||m_ADC_value["PhV_phsB"] < m_Param_value["PTUV_Lim"] * m_Param_value["std_voltage"]
        ||m_ADC_value["PhV_phsC"] < m_Param_value["PTUV_Lim"] * m_Param_value["std_voltage"])
    {
        m_voltage_time.PTUV_Alm_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUV_Alm_Tm_s=0;
    }

    return 0;
}

//矢压告警 持续时间
int CParamManager::voltage_yx_analysis_PTUV_Loss_Alm()
{
    //A矢压
    if(m_ADC_value["PhV_phsA"] < m_Param_value["PTUV_Loss_V_Lim"])
    {
        m_voltage_time.PTUV_Loss_Op_phsA_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUV_Loss_Op_phsA_Tm_s = 0;
    }

    //B矢压
    if(m_ADC_value["PhV_phsB"] < m_Param_value["PTUV_Loss_V_Lim"])
    {
        m_voltage_time.PTUV_Loss_Op_phsB_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUV_Loss_Op_phsB_Tm_s = 0;
    }

    //C矢压
    if(m_ADC_value["PhV_phsC"] < m_Param_value["PTUV_Loss_V_Lim"])
    {
        m_voltage_time.PTUV_Loss_Op_phsC_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUV_Loss_Op_phsC_Tm_s = 0;
    }

    //矢压
    if(m_ADC_value["PhV_phsA"] < m_Param_value["PTUV_Loss_V_Lim"]
        || m_ADC_value["PhV_phsB"] < m_Param_value["PTUV_Loss_V_Lim"]
        || m_ADC_value["PhV_phsC"] < m_Param_value["PTUV_Loss_V_Lim"])
    {

        m_voltage_time.PTUV_Loss_Alm_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUV_Loss_Alm_Tm_s = 0;
    }

    //停电
    //A矢压
    if(m_ADC_value["PhV_phsA"] < m_Param_value["PowerOff_V_Lim"])
    {
        m_voltage_time.PWR_Off_Op_phsA_Tm_s++;
    }
    else
    {
        m_voltage_time.PWR_Off_Op_phsA_Tm_s = 0;
    }

    //B矢压
    if(m_ADC_value["PhV_phsB"] < m_Param_value["PowerOff_V_Lim"])
    {
        m_voltage_time.PWR_Off_Op_phsB_Tm_s++;
    }
    else
    {
        m_voltage_time.PWR_Off_Op_phsB_Tm_s = 0;
    }

    //C矢压
    if(m_ADC_value["PhV_phsC"] < m_Param_value["PowerOff_V_Lim"])
    {
        m_voltage_time.PWR_Off_Op_phsC_Tm_s++;
    }
    else
    {
        m_voltage_time.PWR_Off_Op_phsC_Tm_s = 0;
    }

    //矢压
    if(m_ADC_value["PhV_phsA"] < m_Param_value["PowerOff_V_Lim"]
        || m_ADC_value["PhV_phsB"] < m_Param_value["PowerOff_V_Lim"]
        || m_ADC_value["PhV_phsC"] < m_Param_value["PowerOff_V_Lim"])
    {

        m_voltage_time.PowerOff_Tm_s++;
    }
    else 
    {
        m_voltage_time.PowerOff_Tm_s = 0;
    }


    return 0;
}

//有压告警 持续时间
int CParamManager::voltage_yx_analysis_PowerOn_Alm()
{
    //A有压
    if(m_ADC_value["PhV_phsA"] < m_Param_value["PowerOn_V_Lim"])
    {
        m_voltage_time.PowerOn_phsA_Tm_s++;
    }
    else
    {
        m_voltage_time.PowerOn_phsA_Tm_s = 0;
    }

    //B有压
    if(m_ADC_value["PhV_phsB"] < m_Param_value["PowerOn_V_Lim"])
    {
        m_voltage_time.PowerOn_phsB_Tm_s++;
    }
    else
    {
        m_voltage_time.PowerOn_phsB_Tm_s = 0;
    }

    //C有压
    if(m_ADC_value["PhV_phsC"] < m_Param_value["PowerOn_V_Lim"])
    {
        m_voltage_time.PowerOn_phsC_Tm_s++;
    }
    else
    {
        m_voltage_time.PowerOn_phsC_Tm_s = 0;
    }

    //有压
    if(m_ADC_value["PhV_phsA"] > m_Param_value["PowerOn_V_Lim"]
        &&m_ADC_value["PhV_phsB"] > m_Param_value["PowerOn_V_Lim"]
        &&m_ADC_value["PhV_phsC"] > m_Param_value["PowerOn_V_Lim"])
    {
        m_voltage_time.PowerOn_Alm_Tm_s++;
    }
    else
    {
        m_voltage_time.PowerOn_Alm_Tm_s = 0;
    }
    return 0;
}

//电源断相 - 持续时间  定值：PTUV_Open_V_Lim 90V  延时：PTUV_Open_OpDLTmms 0s  
int CParamManager::voltage_yx_analysis_PTUV_Open_Alm()   
{
    //A断相
    if(m_ADC_value["PhV_phsA"] < m_Param_value["PTUV_Open_V_Lim"])
    {
        m_voltage_time.PTUV_Open_phsA_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUV_Open_phsA_Tm_s = 0;
    }

    //B断相
    if(m_ADC_value["PhV_phsB"] < m_Param_value["PTUV_Open_V_Lim"])
    {
        m_voltage_time.PTUV_Open_phsB_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUV_Open_phsB_Tm_s = 0;
    }

    //B断相
    if(m_ADC_value["PhV_phsC"] < m_Param_value["PTUV_Open_V_Lim"])
    {
        m_voltage_time.PTUV_Open_phsC_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUV_Open_phsC_Tm_s = 0;
    }

    mskprintf("PTUV_Open_V_Lim: %f \n", m_Param_value["PTUV_Open_V_Lim"]);
    //断相
    if(m_ADC_value["PhV_phsA"] < m_Param_value["PTUV_Open_V_Lim"]
        ||m_ADC_value["PhV_phsB"] < m_Param_value["PTUV_Open_V_Lim"]
        ||m_ADC_value["PhV_phsC"] < m_Param_value["PTUV_Open_V_Lim"])
    {
        m_voltage_time.PTUV_Open_Alm_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUV_Open_Alm_Tm_s = 0;
    }

    return 0;
}

int CParamManager::current_yx_analysis_PTOV_Open_Op()
{
    float VA = m_ADC_value["PhV_phsA"];  // A相电压
    float VB = m_ADC_value["PhV_phsB"];  // B相电压
    float VC = m_ADC_value["PhV_phsC"];  // C相电压
    
    float error_threshold_L1 = 30.0f;   // 级别1误差阈值：30V
    float error_threshold_L2 = 40.0f;   // 级别2误差阈值：40V

    mskprintf("PT_Type: %f \n", m_Param_value["PT_Type"]);
    if(m_Param_value["PT_Type"] > 0.5)   
    {
        //-------------------------------高压侧A相断电检测（VB = VA + VC）------------------------------------------------//
        float voltage_sum_A = VA + VC;
        float voltage_diff_A = fabs(VB - voltage_sum_A);     
        
        // A相断相级别1检测（30V误差，持续1分钟）
        if(voltage_diff_A <= error_threshold_L1)
        {
            m_voltage_time.PhaseA_Break_L1_Tm_s++;
        }
        else
        {
            m_voltage_time.PhaseA_Break_L1_Tm_s = 0;   
        }
        mskprintf("高压侧PhaseA_Break_L1_Tm_s: %d \n", m_voltage_time.PhaseA_Break_L1_Tm_s);
        // A相断相级别1告警判断
        if(m_voltage_time.PhaseA_Break_L1_Tm_s >= m_Param_value["PTUV_Open_Dly_DYN11"] && m_pdAnalyzer_value_YX["PTOV_Open_Op"] == 0)
        {
            m_pdAnalyzer_value_YX["PTOV_Open_Op"] = 1;         // 断相告警
            m_pdAnalyzer_value_YX["PTOV_Open_phs"] = 1;        // A相断相
            m_pdAnalyzer_value_YX["PTOV_Open_Level"] = 1;      // 级别1
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Op");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_phs");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Level");
            mskprintf("高压侧告警: %d,断相相位：%d,断相级别：%d\n", m_pdAnalyzer_value_YX["PTOV_Open_Op"],m_pdAnalyzer_value_YX["PTOV_Open_phs"],m_pdAnalyzer_value_YX["PTOV_Open_Level"]);
        }
        else if(m_voltage_time.PhaseA_Break_L1_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOV_Open_Op"] == 1 )
        {
            m_pdAnalyzer_value_YX["PTOV_Open_Op"] = 0;         // 断相告警
            m_pdAnalyzer_value_YX["PTOV_Open_phs"] = 0;        // 未断相 
            m_pdAnalyzer_value_YX["PTOV_Open_Level"] = 0;      // 级别0
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Op");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_phs");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Level");
            mskprintf("高压侧告警: %d,断相相位：%d,断相级别：%d\n", m_pdAnalyzer_value_YX["PTOV_Open_Op"],m_pdAnalyzer_value_YX["PTOV_Open_phs"],m_pdAnalyzer_value_YX["PTOV_Open_Level"]);
        } 
        // A相断相级别2检测（40V误差，持续2分钟）
        if(voltage_diff_A <= error_threshold_L2)
        {
            m_voltage_time.PhaseA_Break_L2_Tm_s++;
        }
        else
        {
            m_voltage_time.PhaseA_Break_L2_Tm_s = 0;
        }
        //A相断相级别2告警判断
        if(m_voltage_time.PhaseA_Break_L2_Tm_s >= m_Param_value["PTUV_Open_Dly_DYN11_2"] && m_pdAnalyzer_value_YX["PhaseA_Break_L2_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["PTOV_Open_Op"] = 1;         // 断相告警
            m_pdAnalyzer_value_YX["PTOV_Open_phs"] = 2;        // B相断相
            m_pdAnalyzer_value_YX["PTOV_Open_Level"] = 1;      // 级别1
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Op");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_phs");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Level");
            mskprintf("高压侧告警: %d,断相相位：%d,断相级别：%d\n", m_pdAnalyzer_value_YX["PTOV_Open_Op"],m_pdAnalyzer_value_YX["PTOV_Open_phs"],m_pdAnalyzer_value_YX["PTOV_Open_Level"]);
        }
        else if(m_voltage_time.PhaseA_Break_L2_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOV_Open_Op"] == 1 )
        {
            m_pdAnalyzer_value_YX["PTOV_Open_Op"] = 0;         // 断相告警
            m_pdAnalyzer_value_YX["PTOV_Open_phs"] = 0;        // 未断相 
            m_pdAnalyzer_value_YX["PTOV_Open_Level"] = 0;      // 级别0
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Op");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_phs");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Level");
            mskprintf("高压侧告警: %d,断相相位：%d,断相级别：%d\n", m_pdAnalyzer_value_YX["PTOV_Open_Op"],m_pdAnalyzer_value_YX["PTOV_Open_phs"],m_pdAnalyzer_value_YX["PTOV_Open_Level"]);
        } 

        
        //-------------------------------高压侧B相断电检测（VC = VA + VB）------------------------------------------------//
        float voltage_sum_B = VA + VB;
        float voltage_diff_B = fabs(VC - voltage_sum_B);
        
        // B相断相级别1检测
        if(voltage_diff_B <= error_threshold_L1)
        {
            m_voltage_time.PhaseB_Break_L1_Tm_s++;
        }
        else
        {
            m_voltage_time.PhaseB_Break_L1_Tm_s = 0;
        }
        
        // B相断相级别1告警判断
        if(m_voltage_time.PhaseB_Break_L1_Tm_s >= m_Param_value["PhaseBreak_L1_Dly"] && m_pdAnalyzer_value_YX["PhaseB_Break_L1_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["PTOV_Open_Op"] = 1;         // 断相告警
            m_pdAnalyzer_value_YX["PTOV_Open_phs"] = 2;        // A相断相
            m_pdAnalyzer_value_YX["PTOV_Open_Level"] = 1;      // 级别1
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Op");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_phs");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Level");
            mskprintf("高压侧告警: %d,断相相位：%d,断相级别：%d\n", m_pdAnalyzer_value_YX["PTOV_Open_Op"],m_pdAnalyzer_value_YX["PTOV_Open_phs"],m_pdAnalyzer_value_YX["PTOV_Open_Level"]);
        }
        else if(m_voltage_time.PhaseB_Break_L1_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOV_Open_Op"] == 1 )
        {
            m_pdAnalyzer_value_YX["PTOV_Open_Op"] = 0;         // 断相告警
            m_pdAnalyzer_value_YX["PTOV_Open_phs"] = 0;        // 未断相 
            m_pdAnalyzer_value_YX["PTOV_Open_Level"] = 0;      // 级别0
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Op");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_phs");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Level");
            mskprintf("高压侧告警: %d,断相相位：%d,断相级别：%d\n", m_pdAnalyzer_value_YX["PTOV_Open_Op"],m_pdAnalyzer_value_YX["PTOV_Open_phs"],m_pdAnalyzer_value_YX["PTOV_Open_Level"]);
        }
    
        
        // B相断相级别2检测
        if(voltage_diff_B <= error_threshold_L2)
        {
            m_voltage_time.PhaseB_Break_L2_Tm_s++;
        }
        else
        {
            m_voltage_time.PhaseB_Break_L2_Tm_s = 0;
        }
        
        // B相断相级别2告警判断
        if(m_voltage_time.PhaseB_Break_L2_Tm_s >= m_Param_value["PTUV_Open_Dly_DYN11_2"] && m_pdAnalyzer_value_YX["PhaseB_Break_L2_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["PTOV_Open_Op"] = 1;         // 断相告警
            m_pdAnalyzer_value_YX["PTOV_Open_phs"] = 2;        // A相断相
            m_pdAnalyzer_value_YX["PTOV_Open_Level"] = 2;      // 级别1
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Op");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_phs");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Level");
            mskprintf("高压侧告警: %d,断相相位：%d,断相级别：%d\n", m_pdAnalyzer_value_YX["PTOV_Open_Op"],m_pdAnalyzer_value_YX["PTOV_Open_phs"],m_pdAnalyzer_value_YX["PTOV_Open_Level"]);
        }
        else if(m_voltage_time.PhaseB_Break_L2_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOV_Open_Op"] == 1 )
        {
            m_pdAnalyzer_value_YX["PTOV_Open_Op"] = 0;         // 断相告警
            m_pdAnalyzer_value_YX["PTOV_Open_phs"] = 0;        // 未断相 
            m_pdAnalyzer_value_YX["PTOV_Open_Level"] = 0;      // 级别0
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Op");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_phs");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Level");
            mskprintf("高压侧告警: %d,断相相位：%d,断相级别：%d\n", m_pdAnalyzer_value_YX["PTOV_Open_Op"],m_pdAnalyzer_value_YX["PTOV_Open_phs"],m_pdAnalyzer_value_YX["PTOV_Open_Level"]);
        }
    
        //-------------------------------高压侧C相断电检测（VA = VB + VC）------------------------------------------------//
        float voltage_sum_C = VB + VC;
        float voltage_diff_C = fabs(VA - voltage_sum_C);
        
        
        
        // C相断相级别1检测
        if(voltage_diff_C <= error_threshold_L1)
        {
            m_voltage_time.PhaseC_Break_L1_Tm_s++;
        }
        else
        {
            m_voltage_time.PhaseC_Break_L1_Tm_s = 0;
        }
        
        // C相断相级别1告警判断
        if(m_voltage_time.PhaseC_Break_L1_Tm_s >= m_Param_value["PhaseBreak_L1_Dly"] && m_pdAnalyzer_value_YX["PhaseC_Break_L1_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["PTOV_Open_Op"] = 1;         // 断相告警
            m_pdAnalyzer_value_YX["PTOV_Open_phs"] = 3;        // A相断相
            m_pdAnalyzer_value_YX["PTOV_Open_Level"] = 1;      // 级别1
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Op");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_phs");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Level");
            mskprintf("高压侧告警: %d,断相相位：%d,断相级别：%d\n", m_pdAnalyzer_value_YX["PTOV_Open_Op"],m_pdAnalyzer_value_YX["PTOV_Open_phs"],m_pdAnalyzer_value_YX["PTOV_Open_Level"]);
        }
        else if(m_voltage_time.PhaseC_Break_L1_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOV_Open_Op"] == 1 )
        {
            m_pdAnalyzer_value_YX["PTOV_Open_Op"] = 0;         // 断相告警
            m_pdAnalyzer_value_YX["PTOV_Open_phs"] = 0;        // 未断相 
            m_pdAnalyzer_value_YX["PTOV_Open_Level"] = 0;      // 级别0
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Op");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_phs");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Level");
            mskprintf("高压侧告警: %d,断相相位：%d,断相级别：%d\n", m_pdAnalyzer_value_YX["PTOV_Open_Op"],m_pdAnalyzer_value_YX["PTOV_Open_phs"],m_pdAnalyzer_value_YX["PTOV_Open_Level"]);
        }
        
        // C相断相级别2检测
        if(voltage_diff_C <= error_threshold_L2)
        {
            m_voltage_time.PhaseC_Break_L2_Tm_s++;
        }
        else
        {
            m_voltage_time.PhaseC_Break_L2_Tm_s = 0;
        }
        
        // C相断相级别2告警判断
        if(m_voltage_time.PhaseC_Break_L2_Tm_s >= m_Param_value["PTUV_Open_Dly_DYN11_2"] && m_pdAnalyzer_value_YX["PhaseC_Break_L2_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["PTOV_Open_Op"] = 1;         // 断相告警
            m_pdAnalyzer_value_YX["PTOV_Open_phs"] = 3;        // A相断相
            m_pdAnalyzer_value_YX["PTOV_Open_Level"] = 2;      // 级别1
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Op");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_phs");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Level");
            mskprintf("高压侧告警: %d,断相相位：%d,断相级别：%d\n", m_pdAnalyzer_value_YX["PTOV_Open_Op"],m_pdAnalyzer_value_YX["PTOV_Open_phs"],m_pdAnalyzer_value_YX["PTOV_Open_Level"]);
        }
        else if(m_voltage_time.PhaseC_Break_L2_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOV_Open_Op"] == 1 )
        {
            m_pdAnalyzer_value_YX["PTOV_Open_Op"] = 0;         // 断相告警
            m_pdAnalyzer_value_YX["PTOV_Open_phs"] = 0;        // 未断相 
            m_pdAnalyzer_value_YX["PTOV_Open_Level"] = 0;      // 级别0
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Op");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_phs");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Level");
            mskprintf("高压侧告警: %d,断相相位：%d,断相级别：%d\n", m_pdAnalyzer_value_YX["PTOV_Open_Op"],m_pdAnalyzer_value_YX["PTOV_Open_phs"],m_pdAnalyzer_value_YX["PTOV_Open_Level"]);
        }
    }
    else  if(m_Param_value["PT_Type"] > 0.5)     //Yyn0型配变逻辑  
    {
        float VA = m_ADC_value["PhV_phsA"], VB = m_ADC_value["PhV_phsB"], VC = m_ADC_value["PhV_phsC"];
        float Vexp = 0.866f * m_Param_value["std_voltage"];
        float tol = 12.0f, v0 = m_Param_value["PTUV_Open_V_Lim"];

        // A相断相：A≈0，B/C≈0.866Vn  
        if (VA < v0 && fabs(VB - Vexp) <= tol && fabs(VC - Vexp) <= tol)
        {
            m_voltage_time.PhaseA_Break_L1_Tm_s++;
        }   
        else
        {
            m_voltage_time.PhaseA_Break_L1_Tm_s = 0;
        }

        // B 相断相：B≈0，A/C≈0.866Vn
        if (VB < v0 && fabs(VA - Vexp) <= tol && fabs(VC - Vexp) <= tol)
        {
            m_voltage_time.PhaseB_Break_L1_Tm_s++;
        }
        else
        {
            m_voltage_time.PhaseB_Break_L1_Tm_s = 0;
        }
        
        // C 相断相：C≈0，A/B≈0.866Vn
        if (VC < v0 && fabs(VA - Vexp) <= tol && fabs(VB - Vexp) <= tol)
        {
            m_voltage_time.PhaseC_Break_L1_Tm_s++;
        }
        else
        {
            m_voltage_time.PhaseC_Break_L1_Tm_s = 0;
        }

        if(m_voltage_time.PhaseA_Break_L1_Tm_s >= m_Param_value["PTUV_Open_Dly_Yyn0"] && m_pdAnalyzer_value_YX["PTOV_Open_Op"] == 0)
        {
            m_pdAnalyzer_value_YX["PTOV_Open_Op"] = 1;         // 断相告警
            m_pdAnalyzer_value_YX["PTOV_Open_phs"] = 1;        // A相断相
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Op");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_phs");
        }
        else
        {
            m_pdAnalyzer_value_YX["PTOV_Open_Op"] = 0;         // 断相告警
            m_pdAnalyzer_value_YX["PTOV_Open_phs"] = 0;        // A相断相
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Op");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_phs");
        }

        if(m_voltage_time.PhaseB_Break_L1_Tm_s >= m_Param_value["PTUV_Open_Dly_Yyn0"] && m_pdAnalyzer_value_YX["PTOV_Open_Op"] == 0)
        {
            m_pdAnalyzer_value_YX["PTOV_Open_Op"] = 1;         // 断相告警
            m_pdAnalyzer_value_YX["PTOV_Open_phs"] = 2;        // A相断相
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Op");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_phs");
        }
        else
        {
            m_pdAnalyzer_value_YX["PTOV_Open_Op"] = 0;         // 断相告警
            m_pdAnalyzer_value_YX["PTOV_Open_phs"] = 0;        // A相断相
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Op");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_phs");
        }
        
        if(m_voltage_time.PhaseC_Break_L1_Tm_s >= m_Param_value["PTUV_Open_Dly_Yyn0"] && m_pdAnalyzer_value_YX["PTOV_Open_Op"] == 0)
        {
            m_pdAnalyzer_value_YX["PTOV_Open_Op"] = 1;         // 断相告警
            m_pdAnalyzer_value_YX["PTOV_Open_phs"] = 3;        // A相断相
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Op");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_phs");
        }
        else
        {
            m_pdAnalyzer_value_YX["PTOV_Open_Op"] = 0;         // 断相告警
            m_pdAnalyzer_value_YX["PTOV_Open_phs"] = 0;        // A相断相
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_Op");
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Open_phs");
        }
        
    }
      
}

//电压逆向序
int CParamManager::voltage_yx_analysis_PhsSeqV_Alm()
{
    // /* 3相电压大于启动电压逆向序判定阈值， */ 报遥信事件
    if (m_ADC_value["PhV_phsA"] >= m_Param_value["PowerOn_V_Lim"] && m_ADC_value["PhV_phsB"] >= m_Param_value["PowerOn_V_Lim"] && m_ADC_value["PhV_phsC"] >= m_Param_value["PowerOn_V_Lim"] 
        && ( // m_ADC_value["PhVAng_phsA"] > m_Param_value["inverse_offset_threshold_fs"] || 
            m_ADC_value["PhVAng_phsB"] - 120 > m_Param_value["inverse_offset_threshold_fs"] || m_ADC_value["PhVAng_phsC"] - 120 > m_Param_value["inverse_offset_threshold_fs"]))
    {
        m_voltage_time.PhsSeqV_Alm_Tm_s++; 
    } 
    else 
    {
        m_voltage_time.PhsSeqV_Alm_Tm_s = 0;
    }
    return 0; 
}

//遥信状态判断-电源矢流
int CParamManager::current_yx_analysis_PTUC_Alm()
{
    //A矢流
    if(m_ADC_value["A_phsA"] < m_Param_value["A_Zero_Drift"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]))
    {
        m_voltage_time.PTUC_Op_phsA_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUC_Op_phsA_Tm_s = 0;
    }

     //b矢流
    if(m_ADC_value["A_phsB"] < m_Param_value["A_Zero_Drift"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]))
    {
        m_voltage_time.PTUC_Op_phsB_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUC_Op_phsB_Tm_s = 0;
    }
    

    //c矢流
    if(m_ADC_value["A_phsC"] < m_Param_value["A_Zero_Drift"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]))
    {
        m_voltage_time.PTUC_Op_phsC_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUC_Op_phsC_Tm_s = 0;
    }
    //mskprintf("PhV_phsA:%f,PhV_phsB:%f,PhV_phsC:%f\n",m_ADC_value["PhV_phsA"] ,m_ADC_value["PhV_phsB"] ,m_ADC_value["PhV_phsC"] );
    //mskprintf("PowerOn_V_Lim:%f,A_Zero_Drift:%f\n",m_Param_value["PowerOn_V_Lim"],m_Param_value["A_Zero_Drift"]);
    //mskprintf("RtgA:%f,ARtg:%f,ARtgSnd:%f.\n",m_Param_value["RtgA"] ,m_Param_value["ARtg"],m_Param_value["ARtgSnd"]);
    //矢流：三相有压，任一相或两相电流小于启动电流（电流零漂），剩余相电流大于启动电流，上报对应告警
    if( m_ADC_value["PhV_phsA"] >= m_Param_value["PowerOn_V_Lim"]
        && m_ADC_value["PhV_phsB"] >= m_Param_value["PowerOn_V_Lim"]
        && m_ADC_value["PhV_phsC"] >= m_Param_value["PowerOn_V_Lim"]
        &&(max(max(m_ADC_value["A_phsA"],m_ADC_value["A_phsB"]),m_ADC_value["A_phsC"]) > m_Param_value["A_Zero_Drift"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]))
        &&(min(min(m_ADC_value["A_phsA"],m_ADC_value["A_phsB"]),m_ADC_value["A_phsC"]) < m_Param_value["A_Zero_Drift"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]))
        &&fabs(findMiddle(m_ADC_value["A_phsA"],m_ADC_value["A_phsB"],m_ADC_value["A_phsC"]) - m_Param_value["A_Zero_Drift"] * (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])) > 0.0000001)
    {
        m_voltage_time.PTUC_Op_Alm_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUC_Op_Alm_Tm_s = 0;
    }   

    //mskprintf("PTUC_Op_Alm_Tm_s:%d\n",m_voltage_time.PTUC_Op_Alm_Tm_s);


    return 0;
}

//遥信状态判断-电流过流
int CParamManager::current_yx_analysis_PTOA_Alm()
{ 
       
    return 0; 
}

//遥信状态判断-零序过流
int CParamManager::current_yx_analysis_SeqOA_Alm()
{
    mskprintf("SeqA_c0: %f,  Seqc0OA_Lim: %f \n",m_ADC_value["SeqA_c0"], m_Param_value["Seqc0OA_Lim"]);
   
    //零序电流越上线时间
    if(m_ADC_value["SeqA_c0"] > m_Param_value["Seqc0OC_Lim"])
    {
        m_voltage_time.SeqOC_Alm_Tm_s++;
    }
    else
    {
        m_voltage_time.SeqOC_Alm_Tm_s = 0;
    }

    return 0;
}

//遥信状态判断-负荷越线
int CParamManager::current_yx_analysis_Ovld_Alm()
{
    //A负荷越线
    if(m_ADC_value["A_phsA"] > m_Param_value["Load_Lim"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]) )
    {
        m_voltage_time.Ovld_phsA_Tm_s++;
    }
    else
    {
        m_voltage_time.Ovld_phsA_Tm_s = 0;
    }

    //b负荷越线
    if(m_ADC_value["A_phsB"] > m_Param_value["Load_Lim"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]) )
    {
        m_voltage_time.Ovld_phsB_Tm_s++;
    }
    else
    {
        m_voltage_time.Ovld_phsB_Tm_s = 0;
    }

    //C负荷越线
    if(m_ADC_value["A_phsC"] > m_Param_value["Load_Lim"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]) )
    {
        m_voltage_time.Ovld_phsC_Tm_s++;
    }
    else
    {
        m_voltage_time.Ovld_phsC_Tm_s = 0;
    }

    
    

    float ovld_Alm = m_Param_value["Load_Lim"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]);
    mskprintf("aaaaaa ovld_Alm  :%f RtgA: %f\n",ovld_Alm,m_Param_value["RtgA"]);  
    //负荷越线
    if(m_ADC_value["A_phsA"] > m_Param_value["Load_Lim"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])
        ||m_ADC_value["A_phsB"] > m_Param_value["Load_Lim"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])
        ||m_ADC_value["A_phsC"] > m_Param_value["Load_Lim"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]))
    {
        m_voltage_time.Ovld_Alm_Tm_s++;
    }
    else
    {
        m_voltage_time.Ovld_Alm_Tm_s = 0;
    }

    return 0;
}

//遥信状态判断-配变重载，空载，轻载
int CParamManager::current_yx_analysis_PTOC_Hvld_Alm()
{
    //配变重载
	
    // if(m_ADC_value["A_phsA"] > m_Param_value["PTOC_Hvld_Lim"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])
    //     ||m_ADC_value["A_phsB"] > m_Param_value["PTOC_Hvld_Lim"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])
    //     ||m_ADC_value["A_phsC"] > m_Param_value["PTOC_Hvld_Lim"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]))
//m_ADC_value["TotVA"]*(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]) / m_Param_value["DeviceLoad"];
//   if((m_ADC_value["PhVA_phsA"])>(m_Param_value["PTOC_Hvld_Lim"]*( m_Param_value["DeviceLoad"]/3/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
//   ||(m_ADC_value["PhVA_phsB"])>(m_Param_value["PTOC_Hvld_Lim"]*(m_Param_value["DeviceLoad"]/3/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
//   ||(m_ADC_value["PhVA_phsC"])>(m_Param_value["PTOC_Hvld_Lim"]*( m_Param_value["DeviceLoad"]/3/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]))))
//     {
//         m_voltage_time.PTOC_Hvld_Alm_Tm_s++;
//     }
//     else
//     {
//         m_voltage_time.PTOC_Hvld_Alm_Tm_s = 0;
//     }
    
    

    mskprintf("DeviceLoad :%f ARtg : %f \n",m_Param_value["DeviceLoad"],m_Param_value["ARtg"]);
    mskprintf("VRtgSnd :%f ARtgSnd : %f  PTOC_Hvld_Lim:%f \n",m_Param_value["VRtgSnd"],m_Param_value["ARtgSnd"],m_Param_value["PTOC_Hvld_Lim"]);
    float hvld = 0;float ovld = 0;
    hvld = ((m_Param_value["DeviceLoad"] * 1000 / (m_Param_value["ARtg"] / m_Param_value["ARtgSnd"]) / m_Param_value["VRtgSnd"] / 3) * m_Param_value["PTOC_Hvld_Lim"]) ;
    ovld = ((m_Param_value["DeviceLoad"] * 1000 / (m_Param_value["ARtg"] / m_Param_value["ARtgSnd"]) / m_Param_value["VRtgSnd"] / 3 )* m_Param_value["PTOC_Ovld_Lim"]) ;
    mskprintf("hvld :%f ovld : %f \n",hvld,ovld);
    if ((m_ADC_value["A_phsA"] > hvld && m_ADC_value["A_phsA"] < ovld) || (m_ADC_value["A_phsB"] > hvld && m_ADC_value["A_phsA"] < ovld) || (m_ADC_value["A_phsC"] > hvld && m_ADC_value["A_phsA"] < ovld))
    {
        m_voltage_time.PTOC_Hvld_Alm_Tm_s++;
    }   
    else
    {
        m_voltage_time.PTOC_Hvld_Alm_Tm_s = 0;
    }
    //200*1000/（500/5/220/3）   
                        
    float Unld = ((m_Param_value["DeviceLoad"] * 1000 / (m_Param_value["ARtg"] / m_Param_value["ARtgSnd"]) / m_Param_value["VRtgSnd"] / 3) * m_Param_value["PTUC_Unld_Lim"]) ;

    float Lvld = ((m_Param_value["DeviceLoad"] * 1000 / (m_Param_value["ARtg"] / m_Param_value["ARtgSnd"]) / m_Param_value["VRtgSnd"] / 3) * m_Param_value["PTUC_Lvld_Lim"]) ;
    
    mskprintf("unld:%f lvld:%f\n",Unld,Lvld);
    cout<<"A_pshA:"<<m_ADC_value["A_pshA"]<<","<< m_ADC_value["A_pshB"]<<","<<  m_ADC_value["A_pshC"]<<endl;
    //配变空载  
    if(m_ADC_value["A_pshA"] < Unld  || m_ADC_value["A_pshB"] < Unld || m_ADC_value["A_pshC"] < Unld )
    {
        m_voltage_time.PTUC_Unld_Alm_Tm_s ++;  
    }
    else
    {
        m_voltage_time.PTUC_Unld_Alm_Tm_s = 0;
    }
    if(m_ADC_value["A_pshA"] < Unld)
    {
        m_voltage_time.PTOC_Unld_phsA_Tm_s ++;
    }
    else
    {
        m_voltage_time.PTOC_Unld_phsA_Tm_s = 0;
    }
    if(m_ADC_value["A_pshB"] < Unld)
    {
        m_voltage_time.PTOC_Unld_phsB_Tm_s ++;
    }
    else
    {
        m_voltage_time.PTOC_Unld_phsB_Tm_s = 0;
    }
    if(m_ADC_value["A_pshC"] < Unld)
    {
        m_voltage_time.PTOC_Unld_phsC_Tm_s ++;
    }
    else
    {
        m_voltage_time.PTOC_Unld_phsC_Tm_s = 0;
    }

    //配变轻载
    if((m_ADC_value["A_pshA"] > Unld && m_ADC_value["A_pshA"] < Lvld) || (m_ADC_value["A_pshB"] > Unld && m_ADC_value["A_pshB"] < Lvld) || (m_ADC_value["A_pshC"] > Unld && m_ADC_value["A_pshC"] < Lvld))
    {
        m_voltage_time.PTUC_lvld_Alm_Tm_s ++;
    }
    else
    {
        m_voltage_time.PTUC_lvld_Alm_Tm_s = 0;
    }

    if( m_ADC_value["A_pshA"] > Unld && m_ADC_value["A_pshA"] < Lvld)
    {
        m_voltage_time.PTOC_lvld_phsA_Tm_s ++;
    }
    else
    {
        m_voltage_time.PTOC_lvld_phsA_Tm_s = 0;
    }
    if( m_ADC_value["A_pshB"] > Unld && m_ADC_value["A_pshB"] < Lvld)
    {
        m_voltage_time.PTOC_lvld_phsB_Tm_s ++;
    }
    else
    {
        m_voltage_time.PTOC_lvld_phsB_Tm_s = 0;
    }
    if( m_ADC_value["A_pshC"] > Unld && m_ADC_value["A_pshC"] < Lvld)
    {
        m_voltage_time.PTOC_lvld_phsC_Tm_s ++;
    }
    else
    {
        m_voltage_time.PTOC_lvld_phsC_Tm_s = 0;
    }
    

    return 0;
}

//遥信状态判断-配变过载
int CParamManager::current_yx_analysis_PTOC_Ovld_Alm()
{
    //配变过载
	mskprintf("PTOC_Ovld_Lim :%f RtgA : %f \n",m_Param_value["PTOC_Ovld_Lim"],m_Param_value["RtgA"]);
    // if(m_ADC_value["A_phsA"] > m_Param_value["PTOC_Ovld_Lim"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])
    //     ||m_ADC_value["A_phsB"] > m_Param_value["PTOC_Ovld_Lim"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])
    //     ||m_ADC_value["A_phsC"] > m_Param_value["PTOC_Ovld_Lim"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]))
// 	if((m_ADC_value["PhVA_phsA"])>(m_Param_value["PTOC_Ovld_Lim"]*( m_Param_value["DeviceLoad"]/3/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
//   ||(m_ADC_value["PhVA_phsB"])>(m_Param_value["PTOC_Ovld_Lim"]*( m_Param_value["DeviceLoad"]/3/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
//   ||(m_ADC_value["PhVA_phsC"])>(m_Param_value["PTOC_Ovld_Lim"]*( m_Param_value["DeviceLoad"]/3/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]))))
//     {
//         m_voltage_time.PTOC_Ovld_Alm_Tm_s++;
//     }
//     else
//     {
//         m_voltage_time.PTOC_Ovld_Alm_Tm_s = 0;
//     }
    
    float ovld = ((m_Param_value["DeviceLoad"] * 1000 / (m_Param_value["ARtg"] / m_Param_value["ARtgSnd"]) / m_Param_value["VRtgSnd"] / 3) * m_Param_value["PTOC_Ovld_Lim"]);
    mskprintf("aaaaa-----ovld :%f PTOC_Ovld_Lim:%f \n",ovld,m_Param_value["PTOC_Ovld_Lim"] );
    if (m_ADC_value["A_phsA"] > ovld || m_ADC_value["A_phsB"] > ovld || m_ADC_value["A_phsC"] > ovld)
    {
        m_voltage_time.PTOC_Ovld_Alm_Tm_s++;
    }
    else
    {
        m_voltage_time.PTOC_Ovld_Alm_Tm_s = 0;
    }
    

    float PhVAAng_phsA = (m_ADC_value["PhAAng_phsA"] - m_ADC_value["PhVAng_phsA"])  ; 
    float PhVAAng_phsB = (m_ADC_value["PhAAng_phsB"] - m_ADC_value["PhVAng_phsB"])  ; 
    float PhVAAng_phsC = (m_ADC_value["PhAAng_phsC"] - m_ADC_value["PhVAng_phsC"])  ; 


    float mild_ovld = ((m_Param_value["DeviceLoad"] * 1000 / (m_Param_value["ARtg"] / m_Param_value["ARtgSnd"]) / m_Param_value["VRtgSnd"] / 3) * m_Param_value["PTOC_mild_Ovld_Lim"]);

    //配变轻度过载 
    if ((m_ADC_value["A_phsA"] > mild_ovld && PhVAAng_phsA < 180.0) || (m_ADC_value["A_phsB"] > mild_ovld && PhVAAng_phsB < 180.0 ) || (m_ADC_value["A_phsC"] > mild_ovld && PhVAAng_phsC > 180.0 ))
    {
        m_voltage_time.PTOC_mild_Ovld_Alm_Tm_s ++;
        m_voltage_time.Re_PTOC_mild_Ovld_Alm_Tm_s = 0; 

    }
    else if ((m_ADC_value["A_phsA"] > mild_ovld && PhVAAng_phsA > 180.0) && (m_ADC_value["A_phsB"] > mild_ovld && PhVAAng_phsB > 180.0 ) && (m_ADC_value["A_phsC"] > mild_ovld && PhVAAng_phsC > 180.0 ))
    {
        m_voltage_time.Re_PTOC_mild_Ovld_Alm_Tm_s ++; 
        m_voltage_time.PTOC_mild_Ovld_Alm_Tm_s = 0;

    }
    else 
    {
        m_voltage_time.PTOC_mild_Ovld_Alm_Tm_s = 0;
        m_voltage_time.Re_PTOC_mild_Ovld_Alm_Tm_s = 0; 
    }
    //A相轻度过载 
    if (m_ADC_value["A_phsA"] > mild_ovld && PhVAAng_phsA < 180.0)
    {
        m_voltage_time.PTOC_mild_Ovld_phsA_Tm_s++;
        m_voltage_time.Re_PTOC_mild_Ovld_phsA_Tm_s = 0;
    }
    else if((m_ADC_value["A_phsA"] > mild_ovld && PhVAAng_phsA > 180.0))
    {
        m_voltage_time.Re_PTOC_mild_Ovld_phsA_Tm_s ++;
        m_voltage_time.PTOC_mild_Ovld_phsA_Tm_s = 0;
    }
    else 
    {
        m_voltage_time.PTOC_mild_Ovld_phsA_Tm_s = 0;
        m_voltage_time.Re_PTOC_mild_Ovld_phsA_Tm_s = 0;
    }
    //B相轻度过载 
    if (m_ADC_value["A_phsB"] > mild_ovld && PhVAAng_phsB < 180.0)
    {
        m_voltage_time.PTOC_mild_Ovld_phsB_Tm_s++;
        m_voltage_time.Re_PTOC_mild_Ovld_phsB_Tm_s = 0;
    }
    else if((m_ADC_value["A_phsB"] > mild_ovld && PhVAAng_phsB > 180.0))
    {
        m_voltage_time.Re_PTOC_mild_Ovld_phsB_Tm_s ++;
        m_voltage_time.PTOC_mild_Ovld_phsB_Tm_s = 0;
    }
    else 
    {
        m_voltage_time.PTOC_mild_Ovld_phsB_Tm_s = 0;
        m_voltage_time.Re_PTOC_mild_Ovld_phsB_Tm_s = 0;
    }
    //C相轻度过载 
    if (m_ADC_value["A_phsC"] > mild_ovld && PhVAAng_phsC < 180.0)
    {
        m_voltage_time.PTOC_mild_Ovld_phsC_Tm_s++;
        m_voltage_time.Re_PTOC_mild_Ovld_phsC_Tm_s = 0;
    }
    else if((m_ADC_value["A_phsC"] > mild_ovld && PhVAAng_phsC > 180.0))
    {
        m_voltage_time.Re_PTOC_mild_Ovld_phsC_Tm_s ++;
        m_voltage_time.PTOC_mild_Ovld_phsC_Tm_s = 0;
    }
    else 
    {
        m_voltage_time.PTOC_mild_Ovld_phsC_Tm_s = 0;
        m_voltage_time.Re_PTOC_mild_Ovld_phsC_Tm_s = 0;
    }


    float middle_ovld = ((m_Param_value["DeviceLoad"] * 1000 / (m_Param_value["ARtg"] / m_Param_value["ARtgSnd"]) / m_Param_value["VRtgSnd"] / 3) * m_Param_value["PTOC_middle_Ovld_Lim"]);
    //配变中度过载
    if ((m_ADC_value["A_phsA"] > middle_ovld && PhVAAng_phsA < 180.0) || (m_ADC_value["A_phsB"] > middle_ovld && PhVAAng_phsB < 180.0 ) || (m_ADC_value["A_phsC"] > middle_ovld && PhVAAng_phsC > 180.0 ))
    {
        m_voltage_time.PTOC_middle_Ovld_Alm_Tm_s ++;
        m_voltage_time.Re_PTOC_middle_Ovld_Alm_Tm_s = 0; 

    }
    else if ((m_ADC_value["A_phsA"] > middle_ovld && PhVAAng_phsA > 180.0) && (m_ADC_value["A_phsB"] > middle_ovld && PhVAAng_phsB > 180.0 ) && (m_ADC_value["A_phsC"] > middle_ovld && PhVAAng_phsC > 180.0 ))
    {
        m_voltage_time.Re_PTOC_middle_Ovld_Alm_Tm_s ++; 
        m_voltage_time.PTOC_middle_Ovld_Alm_Tm_s = 0;

    }
    else 
    {
        m_voltage_time.PTOC_middle_Ovld_Alm_Tm_s = 0;
        m_voltage_time.Re_PTOC_middle_Ovld_Alm_Tm_s = 0; 
    }
    //A相轻度过载 
    if (m_ADC_value["A_phsA"] > middle_ovld && PhVAAng_phsA < 180.0)
    {
        m_voltage_time.PTOC_middle_Ovld_phsA_Tm_s++;
        m_voltage_time.Re_PTOC_middle_Ovld_phsA_Tm_s = 0;
    }
    else if((m_ADC_value["A_phsA"] > middle_ovld && PhVAAng_phsA > 180.0))
    {
        m_voltage_time.Re_PTOC_middle_Ovld_phsA_Tm_s ++;
        m_voltage_time.PTOC_middle_Ovld_phsA_Tm_s = 0;
    }
    else 
    {
        m_voltage_time.PTOC_middle_Ovld_phsA_Tm_s = 0;
        m_voltage_time.Re_PTOC_middle_Ovld_phsA_Tm_s = 0;
    }
    //B相轻度过载 
    if (m_ADC_value["A_phsB"] > middle_ovld && PhVAAng_phsB < 180.0)
    {
        m_voltage_time.PTOC_middle_Ovld_phsB_Tm_s++;
        m_voltage_time.Re_PTOC_middle_Ovld_phsB_Tm_s = 0;
    }
    else if((m_ADC_value["A_phsB"] > middle_ovld && PhVAAng_phsB > 180.0))
    {
        m_voltage_time.Re_PTOC_middle_Ovld_phsB_Tm_s ++;
        m_voltage_time.PTOC_middle_Ovld_phsB_Tm_s = 0;
    }
    else 
    {
        m_voltage_time.PTOC_middle_Ovld_phsB_Tm_s = 0;
        m_voltage_time.Re_PTOC_middle_Ovld_phsB_Tm_s = 0;
    }
    //C相轻度过载 
    if (m_ADC_value["A_phsC"] > middle_ovld && PhVAAng_phsC < 180.0)
    {
        m_voltage_time.PTOC_middle_Ovld_phsC_Tm_s++;
        m_voltage_time.Re_PTOC_middle_Ovld_phsC_Tm_s = 0;
    }
    else if((m_ADC_value["A_phsC"] > middle_ovld && PhVAAng_phsC > 180.0))
    {
        m_voltage_time.Re_PTOC_middle_Ovld_phsC_Tm_s ++;
        m_voltage_time.PTOC_middle_Ovld_phsC_Tm_s = 0;
    }
    else 
    {
        m_voltage_time.PTOC_middle_Ovld_phsC_Tm_s = 0;
        m_voltage_time.Re_PTOC_middle_Ovld_phsC_Tm_s = 0;
    }


    float severe_ovld = ((m_Param_value["DeviceLoad"] * 1000 / (m_Param_value["ARtg"] / m_Param_value["ARtgSnd"]) / m_Param_value["VRtgSnd"] / 3) * m_Param_value["PTOC_severe_Ovld_Lim"]);

    //配变重度过载
    if ((m_ADC_value["A_phsA"] > severe_ovld && PhVAAng_phsA < 180.0) || (m_ADC_value["A_phsB"] > severe_ovld && PhVAAng_phsB < 180.0 ) || (m_ADC_value["A_phsC"] > severe_ovld && PhVAAng_phsC > 180.0 ))
    {
        m_voltage_time.PTOC_severe_Ovld_Alm_Tm_s ++;
        m_voltage_time.Re_PTOC_severe_Ovld_Alm_Tm_s = 0; 

    }
    else if ((m_ADC_value["A_phsA"] > severe_ovld && PhVAAng_phsA > 180.0) && (m_ADC_value["A_phsB"] > severe_ovld && PhVAAng_phsB > 180.0 ) && (m_ADC_value["A_phsC"] > severe_ovld && PhVAAng_phsC > 180.0 ))
    {
        m_voltage_time.Re_PTOC_severe_Ovld_Alm_Tm_s ++; 
        m_voltage_time.PTOC_severe_Ovld_Alm_Tm_s = 0;

    }
    else 
    {
        m_voltage_time.PTOC_severe_Ovld_Alm_Tm_s = 0;
        m_voltage_time.Re_PTOC_severe_Ovld_Alm_Tm_s = 0; 
    }
    //A
    if (m_ADC_value["A_phsA"] > severe_ovld && PhVAAng_phsA < 180.0)
    {
        m_voltage_time.PTOC_severe_Ovld_phsA_Tm_s++;
        m_voltage_time.Re_PTOC_severe_Ovld_phsA_Tm_s = 0;
    }
    else if((m_ADC_value["A_phsA"] > severe_ovld && PhVAAng_phsA > 180.0))
    {
        m_voltage_time.Re_PTOC_severe_Ovld_phsA_Tm_s ++;
        m_voltage_time.PTOC_severe_Ovld_phsA_Tm_s = 0;
    }
    else 
    {
        m_voltage_time.PTOC_severe_Ovld_phsA_Tm_s = 0;
        m_voltage_time.Re_PTOC_severe_Ovld_phsA_Tm_s = 0;
    }
    //B
    if (m_ADC_value["A_phsB"] > severe_ovld && PhVAAng_phsB < 180.0)
    {
        m_voltage_time.PTOC_severe_Ovld_phsB_Tm_s++;
        m_voltage_time.Re_PTOC_severe_Ovld_phsB_Tm_s = 0;
    }
    else if((m_ADC_value["A_phsB"] > severe_ovld && PhVAAng_phsB > 180.0))
    {
        m_voltage_time.Re_PTOC_severe_Ovld_phsB_Tm_s ++;
        m_voltage_time.PTOC_severe_Ovld_phsB_Tm_s = 0;
    }
    else 
    {
        m_voltage_time.PTOC_severe_Ovld_phsB_Tm_s = 0;
        m_voltage_time.Re_PTOC_severe_Ovld_phsB_Tm_s = 0;
    }
    //C
    if (m_ADC_value["A_phsC"] > severe_ovld && PhVAAng_phsC < 180.0)
    {
        m_voltage_time.PTOC_severe_Ovld_phsC_Tm_s++;
        m_voltage_time.Re_PTOC_severe_Ovld_phsC_Tm_s = 0;
    }
    else if((m_ADC_value["A_phsC"] > severe_ovld && PhVAAng_phsC > 180.0))
    {
        m_voltage_time.Re_PTOC_severe_Ovld_phsC_Tm_s ++;
        m_voltage_time.PTOC_severe_Ovld_phsC_Tm_s = 0;
    }
    else 
    {
        m_voltage_time.PTOC_severe_Ovld_phsC_Tm_s = 0;
        m_voltage_time.Re_PTOC_severe_Ovld_phsC_Tm_s = 0;
    }
    

    float abnormal_ovld = ((m_Param_value["DeviceLoad"] * 1000 / (m_Param_value["ARtg"] / m_Param_value["ARtgSnd"]) / m_Param_value["VRtgSnd"] / 3) * m_Param_value["PTOC_abnormal_Ovld_Lim"]);

    //配变异常过载
    if ((m_ADC_value["A_phsA"] > abnormal_ovld && PhVAAng_phsA < 180.0) || (m_ADC_value["A_phsB"] > abnormal_ovld && PhVAAng_phsB < 180.0 ) || (m_ADC_value["A_phsC"] > abnormal_ovld && PhVAAng_phsC > 180.0 ))
    {
        m_voltage_time.PTOC_abnormal_Ovld_Alm_Tm_s ++;
        m_voltage_time.Re_PTOC_abnormal_Ovld_Alm_Tm_s = 0; 

    }
    else if ((m_ADC_value["A_phsA"] > abnormal_ovld && PhVAAng_phsA > 180.0) && (m_ADC_value["A_phsB"] > abnormal_ovld && PhVAAng_phsB > 180.0 ) && (m_ADC_value["A_phsC"] > abnormal_ovld && PhVAAng_phsC > 180.0 ))
    {
        m_voltage_time.Re_PTOC_abnormal_Ovld_Alm_Tm_s ++; 
        m_voltage_time.PTOC_abnormal_Ovld_Alm_Tm_s = 0;
    }
    else 
    {
        m_voltage_time.PTOC_abnormal_Ovld_Alm_Tm_s = 0;
        m_voltage_time.Re_PTOC_abnormal_Ovld_Alm_Tm_s = 0; 
        //重度过载时间清零 
        m_voltage_time.PTOC_severe_Ovld_Alm_Tm_s = 0;
        m_voltage_time.Re_PTOC_severe_Ovld_Alm_Tm_s = 0; 
    }
    //A
    if (m_ADC_value["A_phsA"] > abnormal_ovld && PhVAAng_phsA < 180.0)
    {
        m_voltage_time.PTOC_abnormal_Ovld_phsA_Tm_s++;
        m_voltage_time.Re_PTOC_abnormal_Ovld_phsA_Tm_s = 0;
    }
    else if((m_ADC_value["A_phsA"] > abnormal_ovld && PhVAAng_phsA > 180.0))
    {
        m_voltage_time.Re_PTOC_abnormal_Ovld_phsA_Tm_s ++;
        m_voltage_time.PTOC_abnormal_Ovld_phsA_Tm_s = 0;
    }
    else 
    {
        m_voltage_time.PTOC_abnormal_Ovld_phsA_Tm_s = 0;
        m_voltage_time.Re_PTOC_abnormal_Ovld_phsA_Tm_s = 0;
        //A相重度过载时间清零 
        m_voltage_time.PTOC_severe_Ovld_phsA_Tm_s = 0;
        m_voltage_time.Re_PTOC_severe_Ovld_phsA_Tm_s = 0;
    }
    //B 
    if (m_ADC_value["A_phsB"] > abnormal_ovld && PhVAAng_phsB < 180.0)
    {
        m_voltage_time.PTOC_abnormal_Ovld_phsB_Tm_s++;
        m_voltage_time.Re_PTOC_abnormal_Ovld_phsB_Tm_s = 0;
    }
    else if((m_ADC_value["A_phsB"] > abnormal_ovld && PhVAAng_phsB > 180.0))
    {
        m_voltage_time.Re_PTOC_abnormal_Ovld_phsB_Tm_s ++;
        m_voltage_time.PTOC_abnormal_Ovld_phsB_Tm_s = 0;
    }
    else 
    {
        m_voltage_time.PTOC_abnormal_Ovld_phsB_Tm_s = 0;
        m_voltage_time.Re_PTOC_abnormal_Ovld_phsB_Tm_s = 0;
        //B相重度过载时间清零 
        m_voltage_time.PTOC_severe_Ovld_phsB_Tm_s = 0;
        m_voltage_time.Re_PTOC_severe_Ovld_phsB_Tm_s = 0;
    }
    //C 
    if (m_ADC_value["A_phsC"] > abnormal_ovld && PhVAAng_phsC < 180.0)
    {
        m_voltage_time.PTOC_abnormal_Ovld_phsC_Tm_s++;
        m_voltage_time.Re_PTOC_abnormal_Ovld_phsC_Tm_s = 0;
    }
    else if((m_ADC_value["A_phsC"] > abnormal_ovld && PhVAAng_phsC > 180.0))
    {
        m_voltage_time.Re_PTOC_abnormal_Ovld_phsC_Tm_s ++;
        m_voltage_time.PTOC_abnormal_Ovld_phsC_Tm_s = 0;
    }
    else 
    {
        m_voltage_time.PTOC_abnormal_Ovld_phsC_Tm_s = 0;
        m_voltage_time.Re_PTOC_abnormal_Ovld_phsC_Tm_s = 0;
        //C相重度过载时间清零 
        m_voltage_time.PTOC_severe_Ovld_phsC_Tm_s = 0;
        m_voltage_time.Re_PTOC_severe_Ovld_phsC_Tm_s = 0;
    }


    return 0;
}

//剩余电流超限
int CParamManager::current_yx_analysis_Res_Alm()
{
    //剩余电流超限
    if(m_ADC_value["ResA"] > m_Param_value["ResA_Lim"])
    {
        m_voltage_time.Res_Alm_Tm_s++; 
        m_voltage_time.Res_FG_Tm_s = 0;
    }
    else if((m_ADC_value["ResA"] < m_Param_value["ResA_Lim"]) && (m_voltage_time.Res_Alm_Tm_s > 0))
    {
        m_voltage_time.Res_FG_Tm_s ++;
        m_voltage_time.Res_Alm_Tm_s = 0;
    }

    return 0;
}

//功率因数越限
int CParamManager::PT_yx_analysis_PTUPF_Alm()
{
    //mskprintf("PhPF_phsA: %f,PhPF_phsB:%f, PhPF_phsC: %f,PTUPF_Lim:%f.",m_ADC_value["PhPF_phsA"],m_ADC_value["PhPF_phsB"],m_ADC_value["PhPF_phsC"],m_ADC_value["PTUPF_Lim"]);
    //A功率因数越限
    if(m_ADC_value["PhPF_phsA"] < m_Param_value["PTUPF_Lim"])
    {
        m_voltage_time.PTUPF_phsA_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUPF_phsA_Tm_s = 0;
    }
    
    //B功率因数越限
    if(m_ADC_value["PhPF_phsB"] < m_Param_value["PTUPF_Lim"])
    {
        m_voltage_time.PTUPF_phsB_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUPF_phsB_Tm_s = 0;
    }

    //C功率因数越限
    if(m_ADC_value["PhPF_phsC"] < m_Param_value["PTUPF_Lim"])
    {
        m_voltage_time.PTUPF_phsC_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUPF_phsC_Tm_s = 0;
    }


    //功率因数越限
    if(m_ADC_value["PhPF_phsA"] < m_Param_value["PTUPF_Lim"]
        ||m_ADC_value["PhPF_phsB"] < m_Param_value["PTUPF_Lim"]
        ||m_ADC_value["PhPF_phsC"] < m_Param_value["PTUPF_Lim"])
    {
        m_voltage_time.PTUPF_Alm_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUPF_Alm_Tm_s = 0;
    }

    return 0;
}

//遥信状态判断-电源断流
int CParamManager::current_yx_analysis_PTUC_Open_Alm()
{
    //A断流
    if(m_ADC_value["A_phsA"] < m_Param_value["A_Zero_Drift"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]))
    {
        m_voltage_time.PTUC_Open_Op_phsA_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUC_Open_Op_phsA_Tm_s = 0;
    }

     //b断流
    if(m_ADC_value["A_phsB"] < m_Param_value["A_Zero_Drift"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]))
    {
        m_voltage_time.PTUC_Open_Op_phsB_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUC_Open_Op_phsB_Tm_s = 0;
    }


    //c断流
    if(m_ADC_value["A_phsC"] < m_Param_value["A_Zero_Drift"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"]))
    {
        m_voltage_time.PTUC_Open_Op_phsC_Tm_s++;
    }
    else
    {
        m_voltage_time.PTUC_Open_Op_phsC_Tm_s = 0;
    }

    

    //断流：三相有压，任一相电流小于启动电流（电流零漂）
    if( m_ADC_value["PhV_phsA"] >= m_Param_value["PowerOn_V_Lim"]
        && m_ADC_value["PhV_phsB"] >= m_Param_value["PowerOn_V_Lim"]
        && m_ADC_value["PhV_phsC"] >= m_Param_value["PowerOn_V_Lim"]       
        &&(min(min(m_ADC_value["A_phsA"],m_ADC_value["A_phsB"]),m_ADC_value["A_phsC"]) < m_Param_value["A_Zero_Drift"]* (m_Param_value["RtgA"] *1000)/(m_Param_value["ARtg"]/m_Param_value["ARtgSnd"])))
    {
        m_voltage_time.PTUC_Open_Op_Alm_Tm_s++;
    }
    else  
    {
        m_voltage_time.PTUC_Open_Op_Alm_Tm_s = 0;
    }
    return 0;
}
//电压暂降
int CParamManager::current_yx_analysis_DIP_strVal_Alm()
{
    //暂升
    if(m_ADC_value["PhV_phsA"]>=(m_Param_value["SWL_strVal_Lim"]*220)&&m_ADC_value["PhV_phsB"]>=(m_Param_value["SWL_strVal_Lim"]*220)
    &&m_ADC_value["PhV_phsC"]>=(m_Param_value["SWL_strVal_Lim"]*220))
    {
        m_voltage_time.SWL_Alm_Tm_s++;

    }else{
        m_voltage_time.SWL_Alm_Tm_s = 0;
    }
    //暂降
    if(m_ADC_value["PhV_phsA"]<=(m_Param_value["DIP_strVal_Lim"]*220)&&m_ADC_value["PhV_phsB"]<=(m_Param_value["DIP_strVal_Lim"]*220)
    &&m_ADC_value["PhV_phsC"]<=(m_Param_value["DIP_strVal_Lim"]*220))
    {
        m_voltage_time.DIP_Alm_Tm_s++;

    }else{
        m_voltage_time.DIP_Alm_Tm_s = 0;
    }
    //中断
    if(m_ADC_value["PhV_phsA"]<=(m_Param_value["INTR_strVal_Lim"]*220)&&m_ADC_value["PhV_phsB"]<=(m_Param_value["INTR_strVal_Lim"]*220)
    &&m_ADC_value["PhV_phsC"]<=(m_Param_value["INTR_strVal_Lim"]*220))
    {
        m_voltage_time.INTR_Alm_Tm_s++;

    }else{
        m_voltage_time.INTR_Alm_Tm_s = 0;
    }
    //mskprintf("-----------INTR:%f INTR_Alm_Tm_s:%d \n",m_Param_value["INTR_strVal_Lim"],m_voltage_time.INTR_Alm_Tm_s);
    return 0;
}

//电压谐波越限
int CParamManager::current_yx_analysis_ThdPhV_Op_Alm()
{
    //谐波越限
    m_pdAnalyzer_value["ThdPhv_phsA"] =std::sqrt(m_ADC_value["HphV2-n_phsA"] * m_ADC_value["HphV2-n_phsA"])/m_ADC_value["HphV1_phsA"] * 100.0;
    m_pdAnalyzer_value["ThdPhv_phsB"] =std::sqrt(m_ADC_value["HphV2-n_phsB"] * m_ADC_value["HphV2-n_phsB"])/m_ADC_value["HphV1_phsB"] * 100.0;
    m_pdAnalyzer_value["ThdPhv_phsC"] =std::sqrt(m_ADC_value["HphV2-n_phsC"] * m_ADC_value["HphV2-n_phsC"])/m_ADC_value["HphV1_phsC"] * 100.0;

    //A相电压谐波率大于电压谐波总畸变率定值
    if(m_pdAnalyzer_value["ThdPhv_phsA"] > m_Param_value["ThdPhV_Op_V_Lim"] )
    {
        m_voltage_time.ThdPhV_Op_PhsA_Tm_s ++;
    }
    else if(m_pdAnalyzer_value["ThdPhv_phsA"] < m_Param_value["ThdPhV_Op_V_FG_Lim"])
    {
        m_voltage_time.ThdPhV_Op_PhsA_Tm_s = 0;
    }
    //B
    if(m_pdAnalyzer_value["ThdPhv_phsB"] > m_Param_value["ThdPhV_Op_V_Lim"] )
    {
        m_voltage_time.ThdPhV_Op_PhsB_Tm_s ++;
    }
    else if(m_pdAnalyzer_value["ThdPhv_phsB"] < m_Param_value["ThdPhV_Op_V_FG_Lim"])
    {
        m_voltage_time.ThdPhV_Op_PhsB_Tm_s = 0;
    }
    //C    
    if(m_pdAnalyzer_value["ThdPhv_phsC"] > m_Param_value["ThdPhV_Op_V_Lim"] )
    {
        m_voltage_time.ThdPhV_Op_PhsC_Tm_s ++;
    }
    else if(m_pdAnalyzer_value["ThdPhv_phsC"] < m_Param_value["ThdPhV_Op_V_FG_Lim"])
    {
        m_voltage_time.ThdPhV_Op_PhsC_Tm_s = 0;
    }
    //总
    if(m_pdAnalyzer_value["ThdPhv_phsA"] > m_Param_value["ThdPhV_Op_V_Lim"] || m_pdAnalyzer_value["ThdPhv_phsB"] > m_Param_value["ThdPhV_Op_V_Lim"] || m_pdAnalyzer_value["ThdPhv_phsC"] > m_Param_value["ThdPhV_Op_V_Lim"])
    {
        m_voltage_time.ThdPhV_Op_V_Tm_s ++;
    }
    else if(m_pdAnalyzer_value["ThdPhv_phsA"] < m_Param_value["ThdPhV_Op_V_FG_Lim"] && m_pdAnalyzer_value["ThdPhv_phsB"] < m_Param_value["ThdPhV_Op_V_FG_Lim"] && m_pdAnalyzer_value["ThdPhv_phsC"] < m_Param_value["ThdPhV_Op_V_FG_Lim"] )
    {
        m_voltage_time.ThdPhV_Op_V_Tm_s = 0;
    } 
    return 0;
}

//电流谐波越限
int CParamManager::current_yx_analysis_ThdA_Op_Alm()
{
    m_pdAnalyzer_value["ThdA_phsA"] =std::sqrt(m_ADC_value["HA2-n_phsA"] * m_ADC_value["HA2-n_phsA"])/m_ADC_value["HA1_phsA"] * 100.0;
    m_pdAnalyzer_value["ThdA_phsB"] =std::sqrt(m_ADC_value["HA2-n_phsB"] * m_ADC_value["HA2-n_phsB"])/m_ADC_value["HA1_phsB"] * 100.0;
    m_pdAnalyzer_value["ThdA_phsC"] =std::sqrt(m_ADC_value["HA2-n_phsC"] * m_ADC_value["HA2-n_phsC"])/m_ADC_value["HA1_phsC"] * 100.0;

    //A
    if(m_pdAnalyzer_value["ThdA_phsA"] > m_Param_value["ThdA_Op_V_Lim"] )
    {
        m_voltage_time.ThdA_Op_PhsA_Tm_s ++;
    }
    else if(m_pdAnalyzer_value["ThdA_phsA"] < m_Param_value["ThdA_Op_V_FG_Lim"])
    {
        m_voltage_time.ThdA_Op_PhsA_Tm_s = 0;
    }
    //B
    if(m_pdAnalyzer_value["ThdA_phsB"] > m_Param_value["ThdA_Op_V_Lim"] )
    {
        m_voltage_time.ThdA_Op_PhsB_Tm_s ++;
    }
    else if(m_pdAnalyzer_value["ThdA_phsB"] < m_Param_value["ThdA_Op_V_FG_Lim"])
    {
        m_voltage_time.ThdA_Op_PhsB_Tm_s = 0;
    }
    //C    
    if(m_pdAnalyzer_value["ThdA_phsC"] > m_Param_value["ThdA_Op_V_Lim"] )
    {
        m_voltage_time.ThdA_Op_PhsC_Tm_s ++;
    }
    else if(m_pdAnalyzer_value["ThdA_phsC"] < m_Param_value["ThdA_Op_V_FG_Lim"])
    {
        m_voltage_time.ThdA_Op_PhsC_Tm_s = 0;
    }
    //总
    if(m_pdAnalyzer_value["ThdA_phsA"] > m_Param_value["ThdA_Op_V_Lim"] || m_pdAnalyzer_value["ThdA_phsB"] > m_Param_value["ThdA_Op_V_Lim"] || m_pdAnalyzer_value["ThdA_phsC"] > m_Param_value["ThdA_Op_V_Lim"])
    {
        m_voltage_time.ThdA_Op_A_Tm_s ++;
    }
    else if(m_pdAnalyzer_value["ThdA_phsA"] < m_Param_value["ThdA_Op_V_FG_Lim"] && m_pdAnalyzer_value["ThdA_phsB"] < m_Param_value["ThdA_Op_V_FG_Lim"] && m_pdAnalyzer_value["ThdA_phsC"] < m_Param_value["ThdA_Op_V_FG_Lim"] )
    {
        m_voltage_time.ThdA_Op_A_Tm_s = 0;
    }
    return 0;
}

//频率越限
int  CParamManager::current_yx_analysis_TotHzOfs_Alm()
{
    //过频 
    //总
    if(m_pdAnalyzer_value["HzOfs_phsA"] >= m_Param_value["PTOF_Lim"] || m_pdAnalyzer_value["HzOfs_phsB"] >= m_Param_value["PTOF_Lim"] || m_pdAnalyzer_value["HzOfs_phsC"] >= m_Param_value["PTOF_Lim"])
    {
        m_voltage_time.PTOF_Op_Tm_s ++;
    }
    else if(m_pdAnalyzer_value["HzOfs_phsA"] < m_Param_value["RtgHz"] && m_pdAnalyzer_value["HzOfs_phsB"] < m_Param_value["RtgHz"] && m_pdAnalyzer_value["HzOfs_phsC"] >= m_Param_value["PTOF_Lim"])
    {
        m_voltage_time.PTOF_Op_Tm_s = 0;
    }
    //A
    if(m_pdAnalyzer_value["HzOfs_phsA"] >= m_Param_value["PTOF_Lim"]) //A相频率>频率过频定值(50.5HZ)
    {
        m_voltage_time.PTOF_Op_phsA_Tm_s++;
    }
    else if(m_pdAnalyzer_value["HzOfs_phsA"] < m_Param_value["RtgHz"] ) //A相频率<频率定值(50HZ) //A相过频复归事件
    {
        m_voltage_time.PTOF_Op_phsA_Tm_s = 0;
    }
    //B
    if(m_pdAnalyzer_value["HzOfs_phsB"] >= m_Param_value["PTOF_Lim"])
    {
        m_voltage_time.PTOF_Op_phsB_Tm_s++;
    }
    else if(m_pdAnalyzer_value["HzOfs_phsB"] < m_Param_value["RtgHz"] ) 
    {
        m_voltage_time.PTOF_Op_phsB_Tm_s = 0;
    }
    //C
    if(m_pdAnalyzer_value["HzOfs_phsC"] >= m_Param_value["PTOF_Lim"])
    {     
        m_voltage_time.PTOF_Op_phsC_Tm_s++;
    }
    else if(m_pdAnalyzer_value["HzOfs_phsC"] < m_Param_value["RtgHz"] ) 
    {
        m_voltage_time.PTOF_Op_phsC_Tm_s = 0;
    }

    //欠频
    //总
    if(m_pdAnalyzer_value["HzOfs_phsA"] <= m_Param_value["PTUF_Lim"] || m_pdAnalyzer_value["HzOfs_phsB"] <= m_Param_value["PTUF_Lim"] || m_pdAnalyzer_value["HzOfs_phsC"]  <= m_Param_value["PTUF_Lim"])
    {
        m_voltage_time.PTUF_Op_Tm_s++;
    }
    else if(m_pdAnalyzer_value["HzOfs_phsA"] > m_Param_value["RtgHz"] && m_pdAnalyzer_value["HzOfs_phsB"] > m_Param_value["RtgHz"] || m_pdAnalyzer_value["HzOfs_phsC"] > m_Param_value["RtgHz"])
    {
        m_voltage_time.PTUF_Op_Tm_s =0;
    }
    //A
    if(m_pdAnalyzer_value["HzOfs_phsA"] <= m_Param_value["PTUF_Lim"]) //A相频率<频率过频定值(49.5HZ)
    {
        m_voltage_time.PTUF_Op_phsA_Tm_s++;
    }
    else if(m_pdAnalyzer_value["HzOfs_phsA"] > m_Param_value["RtgHz"] ) //A相频率>频率定值(50HZ)
    {
        m_voltage_time.PTUF_Op_phsA_Tm_s = 0;
    }
    //B
    if(m_pdAnalyzer_value["HzOfs_phsB"] <= m_Param_value["PTUF_Lim"])
    {
        m_voltage_time.PTUF_Op_phsB_Tm_s++;
    }
    else if(m_pdAnalyzer_value["HzOfs_phsB"] > m_Param_value["RtgHz"])  
    {
        m_voltage_time.PTUF_Op_phsB_Tm_s = 0;
    }
    //C  
    if(m_pdAnalyzer_value["HzOfs_phsC"]  <= m_Param_value["PTUF_Lim"])
    {     
        m_voltage_time.PTUF_Op_phsC_Tm_s++;
    }
    else if(m_pdAnalyzer_value["HzOfs_phsC"] > m_Param_value["RtgHz"] ) 
    {
        m_voltage_time.PTUF_Op_phsC_Tm_s = 0;
    }
    return 0;
}

void CParamManager::judge_yx_status()
{
    //电压暂降
    if(m_Param_value["DIP_Enable"] > 0.5)
    {    
        // Alm: 过压时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.DIP_Alm_Tm_s >= m_Param_value["DIP_Dly"] && m_pdAnalyzer_value_YX["DipStr_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["DipStr_Alm"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("DipStr_Alm");
        }
        // Alm: 过压时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.DIP_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["DipStr_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["DipStr_Alm"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("DipStr_Alm");
        }     
    }
    //电压暂升
    if(m_Param_value["SWL_Enable"] > 0.5)
    {
        // Alm: 过压时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.SWL_Alm_Tm_s >= m_Param_value["SWL_Dly"] && m_pdAnalyzer_value_YX["SwlStr_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["SwlStr_Alm"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("SwlStr_Alm");
        }
        // Alm: 过压时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.SWL_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["SwlStr_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["SwlStr_Alm"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("SwlStr_Alm");
        }     
    }
    //电压中断
    //mskprintf("INTR_Enable:%f\n",m_Param_value["INTR_Enable"]);
    if(m_Param_value["INTR_Enable"] > 0.5)
    { 
        // mskprintf("--------time:%d\n",m_voltage_time.INTR_Alm_Tm_s);
        // mskprintf("INTR_Dly:%f\n",m_Param_value["INTR_Dly"]);
        // mskprintf("IntrStr_Alm:%d\n",m_pdAnalyzer_value_YX["IntrStr_Alm"]);
        // Alm: 过压时间大于定值 并且上一次事件为0  报遥信事件 
        //电压短时中断的时间内不报告警事件，电压恢复上电时立即上报告警事件
        if (m_voltage_time.INTR_Alm_Tm_s >= m_Param_value["INTR_Dly"] && m_pdAnalyzer_value_YX["IntrStr_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["IntrStr_Alm"] = 1;
            IEC_LOG_RECORD(eRunType,"中断 IntrStr_Alm :%d", m_pdAnalyzer_value_YX["IntrStr_Alm"]);
            //发送遥信
            //CTaskManager::CreateInstance().MqttPackNotificationData_2("IntrStr_Alm");
            
        }
        // Alm: 过压时间为0 并且上一次事件为1  报遥信事件 
        if(m_voltage_time.INTR_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["IntrStr_Alm"] == 1)
        {
            //m_pdAnalyzer_value_YX["I1ntrStr_Alm"] = 1;
            //发送遥信
            IEC_LOG_RECORD(eRunType,"中断 IntrStr_Alm :%d", m_pdAnalyzer_value_YX["IntrStr_Alm"]);
            CTaskManager::CreateInstance().MqttPackNotificationData_2("IntrStr_Alm");
            m_pdAnalyzer_value_YX["IntrStr_Alm"] = 0;
        }     
    }
   // mskprintf("PTOV_Enable : %f\n",m_Param_value["PTOV_Enable"]);
    //---------------------------------过压------------------------------------------------- 
    if(m_Param_value["PTOV_Enable"] > 0.5)//过压投入
    {
        IEC_LOG_RECORD(eRunType,"过压 Va: %f ,Va: %f,Va: %f,过压时间：%d",m_ADC_value["PhV_phsA"], m_ADC_value["PhV_phsB"], m_ADC_value["PhV_phsC"],m_voltage_time.PTOV_Alm_Tm_s);
        mskprintf("过压");
        mskprintf("pshA_cnt : %d\n",m_voltage_time.PTOV_Op_phsA_Tm_s);
        mskprintf("alm_cnt : %d\n",m_voltage_time.PTOV_Alm_Tm_s);
        //-----------------A
        // A: 过压时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTOV_Op_phsA_Tm_s >= m_Param_value["PTOV_Dly"] && m_pdAnalyzer_value_YX["PTOV_Op_phsA"] == 0)
        {

            m_pdAnalyzer_value_YX["PTOV_Op_phsA"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Op_phsA");
            IEC_LOG_RECORD(eRunType,"过压 PTOV_Op_phsA :%d", m_pdAnalyzer_value_YX["PTOV_Op_phsA"]);
        }
        // A: 过压时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTOV_Op_phsA_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOV_Op_phsA"] == 1)
        {

            m_pdAnalyzer_value_YX["PTOV_Op_phsA"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Op_phsA");
            IEC_LOG_RECORD(eRunType,"过压 PTOV_Op_phsA :%d", m_pdAnalyzer_value_YX["PTOV_Op_phsA"]);
        }     

        //-----------------B
        // B: 过压时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTOV_Op_phsB_Tm_s >= m_Param_value["PTOV_Dly"] && m_pdAnalyzer_value_YX["PTOV_Op_phsB"] == 0)
        {
            m_pdAnalyzer_value_YX["PTOV_Op_phsB"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Op_phsB");
            IEC_LOG_RECORD(eRunType,"过压 PTOV_Op_phsB :%d", m_pdAnalyzer_value_YX["PTOV_Op_phsB"]);
        }
        // B: 过压时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTOV_Op_phsB_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOV_Op_phsB"] == 1)
        {
            m_pdAnalyzer_value_YX["PTOV_Op_phsB"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Op_phsB");
            IEC_LOG_RECORD(eRunType,"过压 PTOV_Op_phsB :%d", m_pdAnalyzer_value_YX["PTOV_Op_phsB"]);
        }

        //-----------------C
        // C: 过压时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTOV_Op_phsC_Tm_s >= m_Param_value["PTOV_Dly"] && m_pdAnalyzer_value_YX["PTOV_Op_phsC"] == 0)
        {
            m_pdAnalyzer_value_YX["PTOV_Op_phsC"] = 1;
			IEC_LOG_RECORD(eRunType,"过压 PTOV_Op_phsC :%d", m_pdAnalyzer_value_YX["PTOV_Op_phsC"]);
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Op_phsC");
        }
        // C: 过压时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTOV_Op_phsC_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOV_Op_phsC"] == 1)
        {
            m_pdAnalyzer_value_YX["PTOV_Op_phsC"] = 0;
			IEC_LOG_RECORD(eRunType,"过压 PTOV_Op_phsC :%d", m_pdAnalyzer_value_YX["PTOV_Op_phsC"]);
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Op_phsC");
        }     

        //-----------------Alm
        // Alm: 过压时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTOV_Alm_Tm_s >= m_Param_value["PTOV_Dly"] && m_pdAnalyzer_value_YX["PTOV_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["PTOV_Alm"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Alm");
            IEC_LOG_RECORD(eRunType,"过压 PTOV_Alm :%d", m_pdAnalyzer_value_YX["PTOV_Alm"]);
        }
        // Alm: 过压时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTOV_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOV_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["PTOV_Alm"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOV_Alm");
            IEC_LOG_RECORD(eRunType,"过压 PTOV_Alm :%d", m_pdAnalyzer_value_YX["PTOV_Alm"]);
        }     
        
    }
    
    //---------------------------------零序过压-------------------------------------------------//+
    //mskprintf("Seqc0OV_Enable : %f\n",m_Param_value["Seqc0OV_Enable"]);
    if(m_Param_value["Seqc0OV_Enable"] > 0.5)//过压投入
    {
        IEC_LOG_RECORD(eRunType,"过压 Va: %f ,Va: %f,Va: %f,零序过压时间：%d",m_ADC_value["PhV_phsA"], m_ADC_value["PhV_phsB"], m_ADC_value["PhV_phsC"],m_voltage_time.SeqOV_Alm_Tm_s);
        mskprintf("SeqOV_Alm_Tm_s : %d, Seqc0OV_Dly:%d, SeqOV_Alm:%d \n",m_voltage_time.SeqOV_Alm_Tm_s , m_Param_value["Seqc0OV_Dly"] , m_pdAnalyzer_value_YX["SeqOV_Alm"]);
        // Alm: 过压时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.SeqOV_Alm_Tm_s >= m_Param_value["Seqc0OV_Dly"] && m_pdAnalyzer_value_YX["SeqOV_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["SeqOV_Alm"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("SeqOV_Alm");
            IEC_LOG_RECORD(eRunType,"零序过压 SeqOV_Alm :%d", m_pdAnalyzer_value_YX["SeqOV_Alm"]);
        }
        // Alm: 过压时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.SeqOV_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["SeqOV_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["SeqOV_Alm"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("SeqOV_Alm");
            IEC_LOG_RECORD(eRunType,"零序过压 SeqOV_Alm :%d", m_pdAnalyzer_value_YX["SeqOV_Alm"]);
        }     
        
    }

    //---------------------------------低压-------------------------------------------------//
    if(m_Param_value["PTUV_Enable"] > 0.5)//低压投入
    {
        IEC_LOG_RECORD(eRunType,"低压 Va: %f ,Va: %f,Va: %f,零序过压时间：%d",m_ADC_value["PhV_phsA"], m_ADC_value["PhV_phsB"], m_ADC_value["PhV_phsC"],m_voltage_time.PTUV_Alm_Tm_s);
        //-----------------A
        // A: 低压时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTUV_Op_phsA_Tm_s >= m_Param_value["PTUV_Dly"] && m_pdAnalyzer_value_YX["PTUV_Op_phsA"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUV_Op_phsA"] = 1;
			IEC_LOG_RECORD(eRunType,"低压 PTUV_Op_phsA :%d", m_pdAnalyzer_value_YX["PTUV_Op_phsA"]);//licong
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Op_phsA");
        }
        // A: 低压时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTUV_Op_phsA_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUV_Op_phsA"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUV_Op_phsA"] = 0;
			IEC_LOG_RECORD(eRunType,"低压 PTUV_Op_phsA :%d", m_pdAnalyzer_value_YX["PTUV_Op_phsA"]);//licong
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Op_phsA");
        }    
        //-----------------B
        // B: 低压时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTUV_Op_phsB_Tm_s >= m_Param_value["PTUV_Dly"] && m_pdAnalyzer_value_YX["PTUV_Op_phsB"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUV_Op_phsB"] = 1;
			IEC_LOG_RECORD(eRunType,"低压 PTUV_Op_phsB :%d", m_pdAnalyzer_value_YX["PTUV_Op_phsB"]);//licong
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Op_phsB");
        }
        // B: 低压时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTUV_Op_phsB_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUV_Op_phsB"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUV_Op_phsB"] = 0;
			IEC_LOG_RECORD(eRunType,"低压 PTUV_Op_phsB :%d", m_pdAnalyzer_value_YX["PTUV_Op_phsB"]);//licong
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Op_phsB");
        }    
        //-----------------C
        // C: 低压时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTUV_Op_phsC_Tm_s >= m_Param_value["PTUV_Dly"] && m_pdAnalyzer_value_YX["PTUV_Op_phsC"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUV_Op_phsC"] = 1;
			IEC_LOG_RECORD(eRunType,"低压 PTUV_Op_phsC :%d", m_pdAnalyzer_value_YX["PTUV_Op_phsC"]);//licong
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Op_phsC");
        }
        // C: 低压时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTUV_Op_phsC_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUV_Op_phsC"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUV_Op_phsC"] = 0;
			IEC_LOG_RECORD(eRunType,"低压 PTUV_Op_phsC :%d", m_pdAnalyzer_value_YX["PTUV_Op_phsC"]);//licong
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Op_phsC");
        } 

        //-----------------Alm
        // Alm: 低压时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTUV_Alm_Tm_s >= m_Param_value["PTUV_Dly"] && m_pdAnalyzer_value_YX["PTUV_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUV_Alm"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Alm");
            IEC_LOG_RECORD(eRunType,"低压 PTUV_Alm :%d", m_pdAnalyzer_value_YX["PTUV_Alm"]);
        }
        // Alm: 低压时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTUV_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUV_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUV_Alm"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Alm");
            IEC_LOG_RECORD(eRunType,"低压 PTUV_Alm :%d", m_pdAnalyzer_value_YX["PTUV_Alm"]);
        }     

    }

    //---------------------------------失压---缺相(二次回路断线)----------------------------------------------//
    if(m_Param_value["PTUV_Loss_Enable"] > 0.5)  //PowOff_Enable
    {
        //-----------------A
        // A: 矢压时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTUV_Loss_Op_phsA_Tm_s > m_Param_value["PTUV_Loss_Dly"] && m_pdAnalyzer_value_YX["PTUV_Loss_Op_phsA"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUV_Loss_Op_phsA"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Loss_Op_phsA");
            IEC_LOG_RECORD(eRunType,"失压 PTUV_Loss_Op_phsA :%d", m_pdAnalyzer_value_YX["PTUV_Loss_Op_phsA"]);
        }
        // A: 矢压时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTUV_Loss_Op_phsA_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUV_Loss_Op_phsA"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUV_Loss_Op_phsA"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Loss_Op_phsA");
            IEC_LOG_RECORD(eRunType,"失压 PTUV_Loss_Op_phsA :%d", m_pdAnalyzer_value_YX["PTUV_Loss_Op_phsA"]);
        }    
        
        //-----------------B
        // B: 矢压时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTUV_Loss_Op_phsB_Tm_s > m_Param_value["PTUV_Loss_Dly"] && m_pdAnalyzer_value_YX["PTUV_Loss_Op_phsB"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUV_Loss_Op_phsB"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Loss_Op_phsB");
            IEC_LOG_RECORD(eRunType,"失压 PTUV_Loss_Op_phsB :%d", m_pdAnalyzer_value_YX["PTUV_Loss_Op_phsB"]);
        }
        // B: 矢压时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTUV_Loss_Op_phsB_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUV_Loss_Op_phsB"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUV_Loss_Op_phsB"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Loss_Op_phsB");
            IEC_LOG_RECORD(eRunType,"失压 PTUV_Loss_Op_phsB :%d", m_pdAnalyzer_value_YX["PTUV_Loss_Op_phsB"]);
        }    

        //-----------------C
        // C: 矢压时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTUV_Loss_Op_phsC_Tm_s > m_Param_value["PTUV_Loss_Dly"] && m_pdAnalyzer_value_YX["PTUV_Loss_Op_phsC"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUV_Loss_Op_phsC"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Loss_Op_phsC");
            IEC_LOG_RECORD(eRunType,"失压 PTUV_Loss_Op_phsC :%d", m_pdAnalyzer_value_YX["PTUV_Loss_Op_phsC"]);
        }
        // C: 矢压时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTUV_Loss_Op_phsC_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUV_Loss_Op_phsC"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUV_Loss_Op_phsC"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Loss_Op_phsC");
            IEC_LOG_RECORD(eRunType,"失压 PTUV_Loss_Op_phsC :%d", m_pdAnalyzer_value_YX["PTUV_Loss_Op_phsC"]);
        }    

        //-----------------Alm
        // 矢压时间大于定值 并且上一次事件为0  报遥信事件  

        if (m_voltage_time.PTUV_Loss_Alm_Tm_s > m_Param_value["PTUV_Loss_Dly"] && m_pdAnalyzer_value_YX["PTUV_Loss_Alm"] == 0)
        {
            if(m_ADC_value["A_phsA"]>m_Param_value["PTUV_Loss_A_Lim"] &&  m_ADC_value["A_phsB"] >m_Param_value["PTUV_Loss_A_Lim"] ||m_ADC_value["A_phsC"] > m_Param_value["PTUV_Loss_A_Lim"])//正规该是>0.5%Ib
			{ 
				m_pdAnalyzer_value_YX["PTUV_Loss_Alm"] = 1;  
                //发送遥信-失压
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Loss_Alm");
                IEC_LOG_RECORD(eRunType,"失压 PTUV_Loss_Alm :%d ", m_pdAnalyzer_value_YX["PTUV_Loss_Alm"]);
            }
        }
        if(m_voltage_time.PTUV_Loss_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUV_Loss_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUV_Loss_Alm"] = 0;
            //发送遥信-失压
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Loss_Alm");
            IEC_LOG_RECORD(eRunType,"失压 PTUV_Loss_Alm :%d ", m_pdAnalyzer_value_YX["PTUV_Loss_Alm"]);
        }

        // if (m_voltage_time.PTUV_Loss_Alm_Tm_s > m_Param_value["PowerOff_Dly"] && m_pdAnalyzer_value_YX["PTUV_Loss_Alm"] == 0)
        // {
		// 	if(m_ADC_value["A_phsA"]>0.2||m_ADC_value["A_phsB"]>0.2||m_ADC_value["A_phsC"]>0.2)//正规该是>0.5%Ib
		// 	{ 
        //         if(m_pdAnalyzer_value_YX["PTUV_Loss_Alm"] == 0)
        //         {
		// 		    m_pdAnalyzer_value_YX["PTUV_Loss_Alm"] = 1;
        //             //发送遥信-失压
        //             CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Loss_Alm");
        //             IEC_LOG_RECORD(eRunType,"失压 PTUV_Loss_Alm :%d ", m_pdAnalyzer_value_YX["PTUV_Loss_Alm"]);
        //         }
        //         }
        //         else{
        //         //发送遥信-停电
        //         if(m_pdAnalyzer_value_YX["PowerOff_Alm"] == 0){
            
        //             m_pdAnalyzer_value_YX["PowerOff_Alm"] = 1;
        //             CTaskManager::CreateInstance().MqttPackNotificationData_2("PowerOff_Alm");
        //             IEC_LOG_RECORD(eRunType,"停电 PowerOff_Alm:%d",m_pdAnalyzer_value_YX["PowerOff_Alm"]);
        //         }
		// 	}
        // }
        // // 矢压时间为0 并且上一次事件为1  报遥信事件
        // if(m_voltage_time.PTUV_Loss_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUV_Loss_Alm"] == 1)
        // {
        //     m_pdAnalyzer_value_YX["PTUV_Loss_Alm"] = 0;
        //     //发送遥信-失压
        //     CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Loss_Alm");

        //     m_pdAnalyzer_value_YX["PowerOff_Alm"] = 0;
        //     //发送遥信-停电
        //     CTaskManager::CreateInstance().MqttPackNotificationData_2("PowerOff_Alm");
		// 	IEC_LOG_RECORD(eRunType,"失压 PTUV_Loss_Alm :%d 停电 PowerOff_Alm:%d",
		// 	 m_pdAnalyzer_value_YX["PTUV_Loss_Alm"],m_pdAnalyzer_value_YX["PowerOff_Alm"]);//licong
        // }    
    }
    //---------------------------------停电--------------------------------------------------//
    if(m_Param_value["PowOff_Enable"] >0.5)
    {
        //-----------------A
        // A: 停电时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PWR_Off_Op_phsA_Tm_s > m_Param_value["PowerOff_Dly"] &&  m_pdAnalyzer_value_YX["PWR_Off_Op_phsA"] == 0)
        {
            m_pdAnalyzer_value_YX["PWR_Off_Op_phsA"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PWR_Off_Op_phsA");
            IEC_LOG_RECORD(eRunType,"停电 PWR_Off_Op_phsA :%d", m_pdAnalyzer_value_YX["PWR_Off_Op_phsA"]);
        }
        // A: 停电时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PWR_Off_Op_phsA_Tm_s == 0 && m_pdAnalyzer_value_YX["PWR_Off_Op_phsA"] == 1)
        {
            m_pdAnalyzer_value_YX["PWR_Off_Op_phsA"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PWR_Off_Op_phsA");
            IEC_LOG_RECORD(eRunType,"停电 PWR_Off_Op_phsA :%d", m_pdAnalyzer_value_YX["PWR_Off_Op_phsA"]);
        }    
        
        //-----------------B
        // B: 停电时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PWR_Off_Op_phsB_Tm_s > m_Param_value["PowerOff_Dly"] && m_pdAnalyzer_value_YX["PWR_Off_Op_phsB"] == 0)
        {
            m_pdAnalyzer_value_YX["PWR_Off_Op_phsB"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PWR_Off_Op_phsB");
            IEC_LOG_RECORD(eRunType,"停电 PWR_Off_Op_phsB :%d", m_pdAnalyzer_value_YX["PWR_Off_Op_phsB"]);
        }
        // B: 停电时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PWR_Off_Op_phsB_Tm_s == 0 && m_pdAnalyzer_value_YX["PWR_Off_Op_phsB"] == 1)
        {
            m_pdAnalyzer_value_YX["PWR_Off_Op_phsB"] = 0; 
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PWR_Off_Op_phsB");  
            IEC_LOG_RECORD(eRunType,"停电 PWR_Off_Op_phsB :%d", m_pdAnalyzer_value_YX["PWR_Off_Op_phsB"]);
        }    

        //-----------------C
        // C: 停电时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PWR_Off_Op_phsC_Tm_s > m_Param_value["PowerOff_Dly"] && m_pdAnalyzer_value_YX["PWR_Off_Op_phsC"] == 0)
        {
            m_pdAnalyzer_value_YX["PWR_Off_Op_phsC"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PWR_Off_Op_phsC");
            IEC_LOG_RECORD(eRunType,"停电 PWR_Off_Op_phsC :%d", m_pdAnalyzer_value_YX["PWR_Off_Op_phsC"]);
        }
        // C: 停电时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PWR_Off_Op_phsC_Tm_s == 0 && m_pdAnalyzer_value_YX["PWR_Off_Op_phsC"] == 1)
        {
            m_pdAnalyzer_value_YX["PWR_Off_Op_phsC"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PWR_Off_Op_phsC");
            IEC_LOG_RECORD(eRunType,"停电 PWR_Off_Op_phsC :%d", m_pdAnalyzer_value_YX["PWR_Off_Op_phsC"]);
        }    

        //-----------------Alm  
        // 停电时间大于定值 并且上一次事件为0  报遥信事件

        if (m_voltage_time.PowerOff_Tm_s > m_Param_value["PowerOff_Dly"] && m_pdAnalyzer_value_YX["PowerOff_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["PowerOff_Alm"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PowerOff_Alm");
            IEC_LOG_RECORD(eRunType,"停电 PowerOff_Alm :%d ", m_pdAnalyzer_value_YX["PowerOff_Alm"]); 
        }
        if(m_voltage_time.PowerOff_Tm_s == 0 && m_pdAnalyzer_value_YX["PowerOff_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["PowerOff_Alm"] = 0;
            //发送遥信 
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PowerOff_Alm");
            IEC_LOG_RECORD(eRunType,"停电 PowerOff_Alm :%d ", m_pdAnalyzer_value_YX["PowerOff_Alm"]);
        }
    }
    //---------------------------------有压-------------------------------------------------//
    if(m_Param_value["PowOn_Enable"] > 0.5)
    {
        //-----------------A
        // A: 有压时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PowerOn_phsA_Tm_s > m_Param_value["PowerOn_Dly"] && m_pdAnalyzer_value_YX["PowerOn_phsA"] == 0)
        {
            m_pdAnalyzer_value_YX["PowerOn_phsA"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PowerOn_phsA");
            IEC_LOG_RECORD(eRunType,"有压 PowerOn_phsA :%d", m_pdAnalyzer_value_YX["PowerOn_phsA"]);
        }
        // A: 有压时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PowerOn_phsA_Tm_s == 0 && m_pdAnalyzer_value_YX["PowerOn_phsA"] == 1)
        {
            m_pdAnalyzer_value_YX["PowerOn_phsA"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PowerOn_phsA");
            IEC_LOG_RECORD(eRunType,"有压 PowerOn_phsA :%d", m_pdAnalyzer_value_YX["PowerOn_phsA"]);
        }    

        //-----------------B
        // B: 有压时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PowerOn_phsB_Tm_s > m_Param_value["PowerOn_Dly"] && m_pdAnalyzer_value_YX["PowerOn_phsB"] == 0)
        {
            m_pdAnalyzer_value_YX["PowerOn_phsB"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PowerOn_phsB");
            IEC_LOG_RECORD(eRunType,"有压 PowerOn_phsB :%d", m_pdAnalyzer_value_YX["PowerOn_phsB"]);
        }
        // B: 有压时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PowerOn_phsB_Tm_s == 0 && m_pdAnalyzer_value_YX["PowerOn_phsB"] == 1)
        {
            m_pdAnalyzer_value_YX["PowerOn_phsB"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PowerOn_phsB");
            IEC_LOG_RECORD(eRunType,"有压 PowerOn_phsB :%d", m_pdAnalyzer_value_YX["PowerOn_phsB"]);
        }   

        //-----------------C
        // C: 有压时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PowerOn_phsC_Tm_s > m_Param_value["PowerOn_Dly"] && m_pdAnalyzer_value_YX["PowerOn_phsC"] == 0)
        {
            m_pdAnalyzer_value_YX["PowerOn_phsC"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PowerOn_phsC");
            IEC_LOG_RECORD(eRunType,"有压 PowerOn_phsC :%d", m_pdAnalyzer_value_YX["PowerOn_phsC"]);
        }
        // C: 有压时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PowerOn_phsC_Tm_s == 0 && m_pdAnalyzer_value_YX["PowerOn_phsC"] == 1)
        {
            m_pdAnalyzer_value_YX["PowerOn_phsC"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PowerOn_phsC");
            IEC_LOG_RECORD(eRunType,"有压 PowerOn_phsC :%d", m_pdAnalyzer_value_YX["PowerOn_phsC"]);
        }    

        //-----------------Alm
        // 有压时间大于定值 并且上一次事件为0  报遥信事件
        
        if (m_voltage_time.PowerOn_Alm_Tm_s > m_Param_value["PowerOn_Dly"] && m_pdAnalyzer_value_YX["PowerOn_Alm"] == 0)
        {
            //发送遥信-来电
            m_pdAnalyzer_value_YX["PowerOn_Alm"] = 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PowerOn_Alm");
            IEC_LOG_RECORD(eRunType,"有压 PowerOn_phsC :%d", m_pdAnalyzer_value_YX["PowerOn_Alm"]);
        }
        // 有压时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PowerOn_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["PowerOn_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["PowerOn_Alm"] = 0;
            //发送遥信-来电
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PowerOn_Alm");
            IEC_LOG_RECORD(eRunType,"有压 PowerOn_phsC :%d", m_pdAnalyzer_value_YX["PowerOn_Alm"]);
        } 
    }

    //---------------------------------断相-------------------------------------------------//
    if(1)  
    {
        //-----------------A
        // A: 断相时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTUV_Open_phsA_Tm_s > m_Param_value["PTUV_Open_Dly"] && m_pdAnalyzer_value_YX["PTUV_Open_phsA"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUV_Open_phsA"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Open_phsA");
            IEC_LOG_RECORD(eRunType,"断相 PTUV_Open_phsA :%d", m_pdAnalyzer_value_YX["PTUV_Open_phsA"]);
        }
        // A: 断相时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTUV_Open_phsA_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUV_Open_phsA"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUV_Open_phsA"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Open_phsA");
            IEC_LOG_RECORD(eRunType,"断相 PTUV_Open_phsA :%d", m_pdAnalyzer_value_YX["PTUV_Open_phsA"]);
        }    

        //-----------------B
        // b: 断相时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTUV_Open_phsB_Tm_s > m_Param_value["PTUV_Open_Dly"] && m_pdAnalyzer_value_YX["PTUV_Open_phsB"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUV_Open_phsB"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Open_phsB");
            IEC_LOG_RECORD(eRunType,"断相 PTUV_Open_phsB :%d", m_pdAnalyzer_value_YX["PTUV_Open_phsB"]);
        }
        // B: 断相时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTUV_Open_phsB_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUV_Open_phsB"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUV_Open_phsB"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Open_phsB");
            IEC_LOG_RECORD(eRunType,"断相 PTUV_Open_phsB :%d", m_pdAnalyzer_value_YX["PTUV_Open_phsB"]);
        }    

        //-----------------C
        // c: 断相时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTUV_Open_phsC_Tm_s > m_Param_value["PTUV_Open_Dly"] && m_pdAnalyzer_value_YX["PTUV_Open_phsC"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUV_Open_phsC"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Open_phsC");
            IEC_LOG_RECORD(eRunType,"断相 PTUV_Open_phsC :%d", m_pdAnalyzer_value_YX["PTUV_Open_phsC"]);
        }
        // C: 断相时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTUV_Open_phsC_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUV_Open_phsC"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUV_Open_phsC"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Open_phsC");
            IEC_LOG_RECORD(eRunType,"断相 PTUV_Open_phsB :%d", m_pdAnalyzer_value_YX["PTUV_Open_phsB"]);
        }  

        //-----------------Alm
        // 断相时间大于定值 并且上一次事件为0  报遥信事件
        mskprintf("m_voltage_time.PTUV_Open_Alm_Tm_s = %d,PTUV_Open_Dly:  %d, PTUV_Open_Alm: %f\r\n", m_voltage_time.PTUV_Open_Alm_Tm_s,m_Param_value["PTUV_Open_Dly"],m_pdAnalyzer_value_YX["PTUV_Open_Alm"]);
        if (m_voltage_time.PTUV_Open_Alm_Tm_s > m_Param_value["PTUV_Open_Dly"] && m_pdAnalyzer_value_YX["PTUV_Open_Alm"] == 0)
        {
            //发送遥信-来电
            m_pdAnalyzer_value_YX["PTUV_Open_Alm"] = 1;
			IEC_LOG_RECORD(eRunType,"断相 PTUV_Open_Alm :%d",m_pdAnalyzer_value_YX["PTUV_Open_Alm"]);  
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Open_Alm");
        }
        // 断相时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTUV_Open_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUV_Open_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUV_Open_Alm"] = 0;
			IEC_LOG_RECORD(eRunType,"断相 PTUV_Open_Alm :%d",m_pdAnalyzer_value_YX["PTUV_Open_Alm"]);  
            //发送遥信-来电
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUV_Open_Alm");
        } 
    }

    //---------------------------------电压逆向序-------------------------------------------------//
    if(1)
    {
        // /* 3相电压大于启动电压逆向序判定阈值， */ 报遥信事件  
        if (m_voltage_time.PhsSeqV_Alm_Tm_s > 20 && m_pdAnalyzer_value_YX["PhsSeqV_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["PhsSeqV_Alm"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PhsSeqV_Alm");
        }
        
        if (m_voltage_time.PhsSeqV_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["PhsSeqV_Alm"] == 1)
        {
             m_pdAnalyzer_value_YX["PhsSeqV_Alm"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PhsSeqV_Alm");
        }
    }
    //---------------------------------失流-------------------------------------------------//
    
    if(m_Param_value["PTUA_Enable"] > 0.5)   //现在数据模型无PTUA_Eable ,PTUA_DLY
    {
        mskprintf("PTUA_Enable:%f", m_Param_value["PTUA_Enable"]);
        //-----------------A
        // A: 失流时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTUC_Op_phsA_Tm_s > m_Param_value["PTUA_Dly"] && m_pdAnalyzer_value_YX["PTUC_Op_phsA"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUC_Op_phsA"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Op_phsA");
        }
        // A: 失流时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTUC_Op_phsA_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUC_Op_phsA"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUC_Op_phsA"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Op_phsA");
        }    

        //-----------------BB
        // B: 失流时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTUC_Op_phsB_Tm_s > m_Param_value["PTUA_Dly"] && m_pdAnalyzer_value_YX["PTUC_Op_phsB"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUC_Op_phsB"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Op_phsB");
        }
        // B: 失流时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTUC_Op_phsB_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUC_Op_phsB"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUC_Op_phsB"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Op_phsB");
        }     

        //-----------------C
        // cC: 失流时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTUC_Op_phsC_Tm_s > m_Param_value["PTUA_Dly"] && m_pdAnalyzer_value_YX["PTUC_Op_phsC"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUC_Op_phsC"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Op_phsC");
        }
        // A: 失流时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTUC_Op_phsC_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUC_Op_phsC"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUC_Op_phsC"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Op_phsC");
        } 

        //-----------------Alm
        // 失流时间大于定值 并且上一次事件为0  报遥信事件
        mskprintf("PTUC_Op_Alm_Tm_s:%d, PTUA_Dly:%f, PTUC_Alm:%d\n",m_voltage_time.PTUC_Op_Alm_Tm_s, m_Param_value["PTUA_Dly"],m_pdAnalyzer_value_YX["PTUC_Alm"]);
        if (m_voltage_time.PTUC_Op_Alm_Tm_s > m_Param_value["PTUA_Dly"] && m_pdAnalyzer_value_YX["PTUC_Alm"] == 0)
        {
            //发送遥信-来电
            m_pdAnalyzer_value_YX["PTUC_Alm"] = 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Alm");
            IEC_LOG_RECORD(eRunType,"失流 PTUC_Alm :%d",m_pdAnalyzer_value_YX["PTUC_Alm"]);  //licong
        }
        // 失流时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTUC_Op_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUC_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUC_Alm"] = 0;
            //发送遥信-来电
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Alm");
             IEC_LOG_RECORD(eRunType,"失流 PTUC_Alm :%d",m_pdAnalyzer_value_YX["PTUC_Alm"]);  //licong
        } 
    }

    //---------------------------------断流-------------------------------------------------//
    if(1) 
    {
        //-----------------A
        // A: 断流时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTUC_Open_Op_phsA_Tm_s > m_Param_value["PTUA_Loss_Dly"] && m_pdAnalyzer_value_YX["PTUC_Open_Op_phsA"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUC_Open_Op_phsA"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Open_Op_phsA");
                  
        }
        // A: 断流时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTUC_Open_Op_phsA_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUC_Open_Op_phsA"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUC_Open_Op_phsA"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Open_Op_phsA");
        }    

        //-----------------BB
        // B: 断流时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTUC_Open_Op_phsB_Tm_s > m_Param_value["PTUA_Loss_Dly"] && m_pdAnalyzer_value_YX["PTUC_Open_Op_phsB"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUC_Open_Op_phsB"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Open_Op_phsB");
        }
        // B: 失流时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTUC_Open_Op_phsB_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUC_Open_Op_phsB"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUC_Open_Op_phsB"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Open_Op_phsB");
        }     

        //-----------------C
        // cC: 失流时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTUC_Open_Op_phsC_Tm_s > m_Param_value["PTUA_Loss_Dly"] && m_pdAnalyzer_value_YX["PTUC_Open_Op_phsC"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUC_Open_Op_phsC"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Open_Op_phsC");
        }
        // A: 失流时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTUC_Open_Op_phsC_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUC_Open_Op_phsC"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUC_Open_Op_phsC"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Open_Op_phsC");
        } 

        //-----------------Alm
        // 失流时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTUC_Open_Op_Alm_Tm_s > m_Param_value["PTUA_Loss_Dly"] && m_pdAnalyzer_value_YX["PTUC_Open_Alm"] == 0)
        {
            //发送遥信-来电
            m_pdAnalyzer_value_YX["PTUC_Open_Alm"] = 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Open_Alm");
            IEC_LOG_RECORD(eRunType,"断流 PTUC_Open_Alm :%d",m_pdAnalyzer_value_YX["PTUC_Open_Alm"]);  //licong
        }
        // 失流时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTUC_Open_Op_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUC_Open_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUC_Open_Alm"] = 0;
            //发送遥信-来电
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Open_Alm");
            IEC_LOG_RECORD(eRunType,"断流 PTUC_Open_Alm :%d",m_pdAnalyzer_value_YX["PTUC_Open_Alm"]);  //licong
        } 
    }

    //---------------------------------零序过流-------------------------------------------------//
    if(m_Param_value["Seqc0OC_Enable"] > 0.5)//过流投入
    {
        mskprintf("SeqOC_Alm_Tm_s : %d, Seqc0OC_Dly:%d, SeqOA_Alm:%d \n",m_voltage_time.SeqOC_Alm_Tm_s , m_Param_value["Seqc0OC_Dly"] , m_pdAnalyzer_value_YX["SeqOA_Alm"]);
        
        mskprintf("Seqc0OC_Dly:%d \n", m_Param_value["Seqc0OC_Dly"]);
        
        
        // Alm: 过压时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.SeqOC_Alm_Tm_s >= m_Param_value["Seqc0OC_Dly"] && m_pdAnalyzer_value_YX["SeqOA_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["SeqOA_Alm"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("SeqOA_Alm");
        }
        // Alm: 过压时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.SeqOC_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["SeqOA_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["SeqOA_Alm"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("SeqOA_Alm");
        } 
    }

    //---------------------------------负荷越线-------------------------------------------------//
    if(m_Param_value["Load_Enable"] > 0.5)//负荷越线投入
    {
        // Alm: 负荷越线时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.Ovld_Alm_Tm_s >= m_Param_value["Load_Dly"] && m_pdAnalyzer_value_YX["Ovld_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["Ovld_Alm"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("Ovld_Alm");
            IEC_LOG_RECORD(eRunType,"负荷越限 Ovld_Alm :%d",m_pdAnalyzer_value_YX["Ovld_Alm"]);  //licong
        }
        // Alm: 负荷越线时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.Ovld_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["Ovld_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["Ovld_Alm"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("Ovld_Alm");
            IEC_LOG_RECORD(eRunType,"负荷越限 Ovld_Alm :%d",m_pdAnalyzer_value_YX["Ovld_Alm"]);  //licong
        }     

    }
    //---------------------------------配变空载-------------------------------------------------//
    if(m_Param_value["PTUC_Unld_Enable"] > 0.5)//配变空载投入
    {
       // Alm: 负荷越线时间大于定值 并且上一次事件为0  报遥信事件
       if (m_voltage_time.PTUC_Unld_Alm_Tm_s >= m_Param_value["PTUC_Unld_Dly"] && m_pdAnalyzer_value_YX["PTUC_Unld_Alm"] == 0)
       {
           m_pdAnalyzer_value_YX["PTUC_Unld_Alm"] = 1;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Unld_Alm");
           IEC_LOG_RECORD(eRunType,"空载越限 PTUC_Unld_Alm :%d",m_pdAnalyzer_value_YX["PTUC_Unld_Alm"]);
       }
       // Alm: 负荷越线时间为0 并且上一次事件为1  报遥信事件
       if(m_voltage_time.PTUC_Unld_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUC_Unld_Alm"] == 1)
       {
           m_pdAnalyzer_value_YX["PTUC_Unld_Alm"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Unld_Alm");
           IEC_LOG_RECORD(eRunType,"空载复归 PTUC_Unld_Alm :%d",m_pdAnalyzer_value_YX["PTUC_Unld_Alm"]);
       } 
       //A
       if(m_voltage_time.PTOC_Unld_phsA_Tm_s >=  m_Param_value["PTUC_Unld_Dly"] && m_pdAnalyzer_value_YX["PTUC_Unld_Alm_phsA"] == 0)
       {
            m_pdAnalyzer_value_YX["PTUC_Unld_Alm_phsA"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Unld_Alm_phsA");
       }
       if(m_voltage_time.PTOC_Unld_phsA_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUC_Unld_Alm_phsA"] == 1)
       {
           m_pdAnalyzer_value_YX["PTUC_Unld_Alm_phsA"] = 0;  
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Unld_Alm_phsA");
       }
       //B
       if(m_voltage_time.PTOC_Unld_phsB_Tm_s >=  m_Param_value["PTUC_Unld_Dly"] && m_pdAnalyzer_value_YX["PTUC_Unld_Alm_phsB"] == 0)
       {
            m_pdAnalyzer_value_YX["PTUC_Unld_Alm_phsB"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Unld_Alm_phsB");
       }
       if(m_voltage_time.PTOC_Unld_phsB_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUC_Unld_Alm_phsB"] == 1)
       {
           m_pdAnalyzer_value_YX["PTUC_Unld_Alm_phsB"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Unld_Alm_phsB");
       }
       //C
       if(m_voltage_time.PTOC_Unld_phsC_Tm_s >=  m_Param_value["PTUC_Unld_Dly"] && m_pdAnalyzer_value_YX["PTUC_Unld_Alm_phsC"] == 0)
       {
            m_pdAnalyzer_value_YX["PTUC_Unld_Alm_phsC"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Unld_Alm_phsC");
       }
       if(m_voltage_time.PTOC_Unld_phsC_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUC_Unld_Alm_phsC"] == 1)
       {
           m_pdAnalyzer_value_YX["PTUC_Unld_Alm_phsC"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Unld_Alm_phsC");
       }
   }

    //---------------------------------配变轻载-------------------------------------------------//
    if(m_Param_value["PTUC_Lvld_Enable"] > 0.5)//配变轻载投入   
    {
       // Alm: 负荷越线时间大于定值 并且上一次事件为0  报遥信事件 
       if (m_voltage_time.PTUC_lvld_Alm_Tm_s >= m_Param_value["PTUC_Lvld_Dly"] && m_pdAnalyzer_value_YX["PTUC_Lvld_Alm"] == 0)
       {
           mskprintf("越限时间：%d,dly:%f\n",m_voltage_time.PTUC_lvld_Alm_Tm_s,m_Param_value["PTUC_Lvld_Dly"]);
           cout<<"越限时间："<<m_voltage_time.PTUC_lvld_Alm_Tm_s<<"dly:"<<m_Param_value["PTUC_Lvld_Dly"];
           m_pdAnalyzer_value_YX["PTUC_Lvld_Alm"] = 1;  
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Lvld_Alm");
           IEC_LOG_RECORD(eRunType,"轻载越限 PTUC_Lvld_Alm :%d",m_pdAnalyzer_value_YX["PTUC_Lvld_Alm"]);
       }
       // Alm: 负荷越线时间为0 并且上一次事件为1  报遥信事件
       if(m_voltage_time.PTUC_lvld_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUC_Lvld_Alm"] == 1)
       {
           m_pdAnalyzer_value_YX["PTUC_Lvld_Alm"] = 0;
           mskprintf("aaaa越限时间：%d\n",m_voltage_time.PTUC_lvld_Alm_Tm_s);
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Lvld_Alm");
           IEC_LOG_RECORD(eRunType,"轻载越限 PTUC_Lvld_Alm :%d",m_pdAnalyzer_value_YX["PTUC_Lvld_Alm"]);
       } 
       //A
       if(m_voltage_time.PTOC_lvld_phsA_Tm_s >=  m_Param_value["PTUC_Lvld_Dly"] && m_pdAnalyzer_value_YX["PTUC_Lvld_Alm_phsA"] == 0)
       {
            m_pdAnalyzer_value_YX["PTUC_Lvld_Alm_phsA"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Lvld_Alm_phsA");
            IEC_LOG_RECORD(eRunType,"轻载越限 PTUC_Lvld_Alm_phsA :%d",m_pdAnalyzer_value_YX["PTUC_Lvld_Alm_phsA"]);
       }
       if(m_voltage_time.PTOC_lvld_phsA_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUC_Lvld_Alm_phsA"] == 1)
       {
           m_pdAnalyzer_value_YX["PTUC_Lvld_Alm_phsA"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Lvld_Alm_phsA");
           IEC_LOG_RECORD(eRunType,"轻载越限 PTUC_Lvld_Alm_phsA :%d",m_pdAnalyzer_value_YX["PTUC_Lvld_Alm_phsA"]);
       }
       //B
       if(m_voltage_time.PTOC_lvld_phsB_Tm_s >=  m_Param_value["PTUC_Lvld_Dly"] && m_pdAnalyzer_value_YX["PTUC_Lvld_Alm_phsB"] == 0)
       {
            m_pdAnalyzer_value_YX["PTUC_Lvld_Alm_phsB"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Lvld_Alm_phsB");
            IEC_LOG_RECORD(eRunType,"轻载越限 PTUC_Lvld_Alm_phsB :%d",m_pdAnalyzer_value_YX["PTUC_Lvld_Alm_phsB"]);
       }
       if(m_voltage_time.PTOC_lvld_phsB_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUC_Lvld_Alm_phsB"] == 1)
       {
           m_pdAnalyzer_value_YX["PTUC_Lvld_Alm_phsB"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Lvld_Alm_phsB");
           IEC_LOG_RECORD(eRunType,"轻载越限 PTUC_Lvld_Alm_phsB :%d",m_pdAnalyzer_value_YX["PTUC_Lvld_Alm_phsB"]);
       }
       //C
       if(m_voltage_time.PTOC_lvld_phsC_Tm_s >=  m_Param_value["PTUC_Lvld_Dly"] && m_pdAnalyzer_value_YX["PTUC_Lvld_Alm_phsC"] == 0)
       {
            m_pdAnalyzer_value_YX["PTUC_Lvld_Alm_phsC"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Lvld_Alm_phsC");
            IEC_LOG_RECORD(eRunType,"轻载越限 PTUC_Lvld_Alm_phsC :%d",m_pdAnalyzer_value_YX["PTUC_Lvld_Alm_phsC"]);
       }
       if(m_voltage_time.PTOC_lvld_phsC_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUC_Lvld_Alm_phsC"] == 1)
       {
           m_pdAnalyzer_value_YX["PTUC_Lvld_Alm_phsC"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUC_Lvld_Alm_phsC");
           IEC_LOG_RECORD(eRunType,"轻载越限 PTUC_Lvld_Alm_phsC :%d",m_pdAnalyzer_value_YX["PTUC_Lvld_Alm_phsC"]);
       }
    } 

    //---------------------------------配变重载-------------------------------------------------//
    if(m_Param_value["PTOC_Hvld_Enable"] > 0.5)//配变重载投入
    {
        // Alm: 负荷越线时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTOC_Hvld_Alm_Tm_s >= m_Param_value["PTOC_Hvld_Dly"] && m_pdAnalyzer_value_YX["PTOC_Hvld_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["PTOC_Hvld_Alm"] = 1;
			IEC_LOG_RECORD(eRunType,"重载 PTOC_Hvld_Alm :%d",m_pdAnalyzer_value_YX["PTOC_Hvld_Alm"]);  //licong
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_Hvld_Alm");
        }
        // Alm: 负荷越线时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTOC_Hvld_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOC_Hvld_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["PTOC_Hvld_Alm"] = 0;
			IEC_LOG_RECORD(eRunType,"重载 PTOC_Hvld_Alm :%d",m_pdAnalyzer_value_YX["PTOC_Hvld_Alm"]);  //licong
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_Hvld_Alm");
        }     
    }
    //---------------------------------配变过载-------------------------------------------------//
    if(m_Param_value["PTOC_Ovld_Enable"] > 0.5)//配变重载投入
    {
        mskprintf("配变过载");
        // Alm: 负荷越线时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTOC_Ovld_Alm_Tm_s >= m_Param_value["PTOC_Ovld_Dly"] && m_pdAnalyzer_value_YX["PTOC_Ovld_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["PTOC_Ovld_Alm"] = 1;
			IEC_LOG_RECORD(eRunType,"过载 PTOC_Ovld_Alm :%d",m_pdAnalyzer_value_YX["PTOC_Ovld_Alm"]); 
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_Ovld_Alm");
        }
        // Alm: 负荷越线时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTOC_Ovld_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOC_Ovld_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["PTOC_Ovld_Alm"] = 0;
			IEC_LOG_RECORD(eRunType,"过载 PTOC_Ovld_Alm :%d",m_pdAnalyzer_value_YX["PTOC_Ovld_Alm"]);  
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_Ovld_Alm");   
        } 
    }

    //---------------------------------配变轻度过载-------------------------------------------------//
    if(m_Param_value["PTOC_mild_Ovld_Enable"] > 0.5)//配变轻度过载投入 
    {
       // 正向 Alm: 负荷越线时间大于定值 并且上一次事件为0  报遥信事件 
       if (m_voltage_time.PTOC_mild_Ovld_Alm_Tm_s >= m_Param_value["PTOC_mild_Ovld_Dly"] && m_pdAnalyzer_value_YX["PTOC_mild_Ovld_Alm"] == 0)
       {
           m_pdAnalyzer_value_YX["PTOC_mild_Ovld_Alm"] = 1;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_mild_Ovld_Alm");
       }
       // Alm: 负荷越线时间为0 并且上一次事件为1  报遥信事件
       if(m_voltage_time.PTOC_mild_Ovld_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOC_mild_Ovld_Alm"] == 1)
       {
           m_pdAnalyzer_value_YX["PTOC_mild_Ovld_Alm"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_mild_Ovld_Alm");
       } 
       //反向
       if (m_voltage_time.Re_PTOC_mild_Ovld_Alm_Tm_s >= m_Param_value["PTOC_mild_Ovld_Dly"] && m_pdAnalyzer_value_YX["Re_PTOC_mild_Ovld_Alm"] == 0)
       {
           m_pdAnalyzer_value_YX["Re_PTOC_mild_Ovld_Alm"] = 1;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_mild_Ovld_Alm");
       }
       if(m_voltage_time.Re_PTOC_mild_Ovld_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["Re_PTOC_mild_Ovld_Alm"] == 1)
       {
           m_pdAnalyzer_value_YX["Re_PTOC_mild_Ovld_Alm"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_mild_Ovld_Alm");
       } 
       //A
       if(m_voltage_time.PTOC_mild_Ovld_phsA_Tm_s >=  m_Param_value["PTOC_mild_Ovld_Dly"] && m_pdAnalyzer_value_YX["PTOC_mild_Ovld_Alm_phsA"] == 0)
       {
            m_pdAnalyzer_value_YX["PTOC_mild_Ovld_Alm_phsA"] = 1; 
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_mild_Ovld_Alm_phsA");
       }
       if(m_voltage_time.PTOC_mild_Ovld_phsA_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOC_mild_Ovld_Alm_phsA"] == 1)
       {
           m_pdAnalyzer_value_YX["PTOC_mild_Ovld_Alm_phsA"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_mild_Ovld_Alm_phsA");
       }
       if(m_voltage_time.Re_PTOC_mild_Ovld_phsA_Tm_s >=  m_Param_value["Re_PTOC_mild_Ovld_Dly"] && m_pdAnalyzer_value_YX["Re_PTOC_mild_Ovld_Alm_phsA"] == 0)
       {
            m_pdAnalyzer_value_YX["Re_PTOC_mild_Ovld_Alm_phsA"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_mild_Ovld_Alm_phsA");
       }
       if(m_voltage_time.PTOC_mild_Ovld_phsA_Tm_s == 0 && m_pdAnalyzer_value_YX["Re_PTOC_mild_Ovld_Alm_phsA"] == 1)
       {
           m_pdAnalyzer_value_YX["Re_PTOC_mild_Ovld_Alm_phsA"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_mild_Ovld_Alm_phsA");
       }
       //B
       if(m_voltage_time.PTOC_mild_Ovld_phsB_Tm_s >=  m_Param_value["PTOC_mild_Ovld_Dly"] && m_pdAnalyzer_value_YX["PTOC_mild_Ovld_Alm_phsB"] == 0)
       {
            m_pdAnalyzer_value_YX["PTOC_mild_Ovld_Alm_phsB"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_mild_Ovld_Alm_phsB");
       }
       if(m_voltage_time.PTOC_mild_Ovld_phsB_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOC_mild_Ovld_Alm_phsB"] == 1)
       {
           m_pdAnalyzer_value_YX["PTOC_mild_Ovld_Alm_phsB"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_mild_Ovld_Alm_phsB");
       }
       if(m_voltage_time.Re_PTOC_mild_Ovld_phsB_Tm_s >=  m_Param_value["Re_PTOC_mild_Ovld_Dly"] && m_pdAnalyzer_value_YX["Re_PTOC_mild_Ovld_Alm_phsB"] == 0)
       {
            m_pdAnalyzer_value_YX["Re_PTOC_mild_Ovld_Alm_phsB"] = 1;
            //发送遥信 
            CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_mild_Ovld_Alm_phsB");
       }
       if(m_voltage_time.PTOC_mild_Ovld_phsB_Tm_s == 0 && m_pdAnalyzer_value_YX["Re_PTOC_mild_Ovld_Alm_phsB"] == 1)
       {
           m_pdAnalyzer_value_YX["Re_PTOC_mild_Ovld_Alm_phsB"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_mild_Ovld_Alm_phsB");
       }
       //C
       if(m_voltage_time.PTOC_mild_Ovld_phsC_Tm_s >=  m_Param_value["PTOC_mild_Ovld_Dly"] && m_pdAnalyzer_value_YX["PTOC_mild_Ovld_Alm_phsC"] == 0)
       {
            m_pdAnalyzer_value_YX["PTOC_mild_Ovld_Alm_phsC"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_mild_Ovld_Alm_phsC");
       }
       if(m_voltage_time.PTOC_mild_Ovld_phsC_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOC_mild_Ovld_Alm_phsC"] == 1)
       {
           m_pdAnalyzer_value_YX["PTOC_mild_Ovld_Alm_phsC"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_mild_Ovld_Alm_phsC");
       }
       if(m_voltage_time.Re_PTOC_mild_Ovld_phsC_Tm_s >=  m_Param_value["Re_PTOC_mild_Ovld_Dly"] && m_pdAnalyzer_value_YX["Re_PTOC_mild_Ovld_Alm_phsC"] == 0)
       {
            m_pdAnalyzer_value_YX["Re_PTOC_mild_Ovld_Alm_phsC"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_mild_Ovld_Alm_phsC");
       }
       if(m_voltage_time.PTOC_mild_Ovld_phsC_Tm_s == 0 && m_pdAnalyzer_value_YX["Re_PTOC_mild_Ovld_Alm_phsC"] == 1)
       {
           m_pdAnalyzer_value_YX["Re_PTOC_mild_Ovld_Alm_phsC"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_mild_Ovld_Alm_phsC");
       }
   }

    //---------------------------------配变中度过载-------------------------------------------------//
    if(m_Param_value["PTOC_middle_Ovld_Enable"] > 0.5)//配变中度过载投入
    {
       // 正向 Alm: 负荷越线时间大于定值 并且上一次事件为0  报遥信事件
       if (m_voltage_time.PTOC_middle_Ovld_Alm_Tm_s >= m_Param_value["PTOC_middle_Ovld_Dly"] && m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm"] == 0)
       {
           //如果中度过载发生，判断重度过载是否发生，重度过载未发生，上报中度过载事件，重度过载发生，不上报中度过载事件
           if(m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm"] == 0)
           {
               m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm"] = 1;
               //发送遥信
               CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_middle_Ovld_Alm");
           }
       }
       //中度过载和重度过载同时发生，中度过载复归
       if(m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm"] == 1 && m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm"] == 1)
       {
           m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_middle_Ovld_Alm");
       }
       // Alm: 负荷越线时间为0 并且上一次事件为1  报遥信事件
       if(m_voltage_time.PTOC_middle_Ovld_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm"] == 1)
       {
           m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_middle_Ovld_Alm");
       }
       //反向
       if (m_voltage_time.Re_PTOC_middle_Ovld_Alm_Tm_s >= m_Param_value["PTOC_middle_Ovld_Dly"] && m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm"] == 0)
       {
           //如果反向中度过载发生，判断反向重度过载是否发生，反向重度过载未发生，上报反向中度过载事件，反向重度过载发生，不上报反向中度过载事件
           if(m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm"] == 0)
           {
               m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm"] = 1;
               //发送遥信
               CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_middle_Ovld_Alm");
           }
       }
       //反向中度过载和反向重度过载同时发生，反向中度过载复归
       if(m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm"] == 1 && m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm"] == 1)
       {
           m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_middle_Ovld_Alm");
       }
       if(m_voltage_time.Re_PTOC_middle_Ovld_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm"] == 1)
       {
           m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_middle_Ovld_Alm");
       }
       //A相
       if(m_voltage_time.PTOC_middle_Ovld_phsA_Tm_s >=  m_Param_value["PTOC_middle_Ovld_Dly"] && m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm_phsA"] == 0)
       {
           //如果A相中度过载发生，判断A相重度过载是否发生，A相重度过载未发生，上报A相中度过载事件，A相重度过载发生，不上报A相中度过载事件
           if(m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm_phsA"] == 0)
           {
               m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm_phsA"] = 1;
               //发送遥信
               CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_middle_Ovld_Alm_phsA");
           }
       }
       //A相中度过载和A相重度过载同时发生，A相中度过载复归
       if(m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm_phsA"] == 1 && m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm_phsA"] == 1)
       {
           m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm_phsA"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_middle_Ovld_Alm_phsA");
       }
       if(m_voltage_time.PTOC_middle_Ovld_phsA_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm_phsA"] == 1)
       {
           m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm_phsA"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_middle_Ovld_Alm_phsA");
       }
       //A相反向中度过载
       if(m_voltage_time.Re_PTOC_middle_Ovld_phsA_Tm_s >=  m_Param_value["Re_PTOC_middle_Ovld_Dly"] && m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm_phsA"] == 0)
       {
           //如果A相反向中度过载发生，判断A相反向重度过载是否发生，A相反向重度过载未发生，上报A相反向中度过载事件，A相反向重度过载发生，不上报A相反向中度过载事件
           if(m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm_phsA"] == 0)
           {
               m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm_phsA"] = 1;
               //发送遥信
               CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_middle_Ovld_Alm_phsA");
           }
       }
       //A相反向中度过载和A相反向重度过载同时发生，A相反向中度过载复归
       if(m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm_phsA"] == 1 && m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm_phsA"] == 1)
       {
           m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm_phsA"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_middle_Ovld_Alm_phsA");
       }
       if(m_voltage_time.Re_PTOC_middle_Ovld_phsA_Tm_s == 0 && m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm_phsA"] == 1)
       {
           m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm_phsA"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_middle_Ovld_Alm_phsA");
       }
       //B相
       if(m_voltage_time.PTOC_middle_Ovld_phsB_Tm_s >=  m_Param_value["PTOC_middle_Ovld_Dly"] && m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm_phsB"] == 0)
       {
           //如果B相中度过载发生，判断B相重度过载是否发生，B相重度过载未发生，上报B相中度过载事件，B相重度过载发生，不上报B相中度过载事件
           if(m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm_phsB"] == 0)
           {
               m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm_phsB"] = 1;
               //发送遥信
               CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_middle_Ovld_Alm_phsB");
           }
       }
       //B相中度过载和B相重度过载同时发生，B相中度过载复归
       if(m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm_phsB"] == 1 && m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm_phsB"] == 1)
       {
           m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm_phsB"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_middle_Ovld_Alm_phsB");
       }
       if(m_voltage_time.PTOC_middle_Ovld_phsB_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm_phsB"] == 1)
       {
           m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm_phsB"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_middle_Ovld_Alm_phsB");
       }
       //B相反向中度过载
       if(m_voltage_time.Re_PTOC_middle_Ovld_phsB_Tm_s >=  m_Param_value["Re_PTOC_middle_Ovld_Dly"] && m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm_phsB"] == 0)
       {
           //如果B相反向中度过载发生，判断B相反向重度过载是否发生，B相反向重度过载未发生，上报B相反向中度过载事件，B相反向重度过载发生，不上报B相反向中度过载事件
           if(m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm_phsB"] == 0)
           {
               m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm_phsB"] = 1;
               //发送遥信
               CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_middle_Ovld_Alm_phsB");
           }
       }
       //B相反向中度过载和B相反向重度过载同时发生，B相反向中度过载复归
       if(m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm_phsB"] == 1 && m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm_phsB"] == 1)
       {
           m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm_phsB"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_middle_Ovld_Alm_phsB");
       }
       if(m_voltage_time.Re_PTOC_middle_Ovld_phsB_Tm_s == 0 && m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm_phsB"] == 1)
       {
           m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm_phsB"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_middle_Ovld_Alm_phsB");
       }
       //C相
       if(m_voltage_time.PTOC_middle_Ovld_phsC_Tm_s >=  m_Param_value["PTOC_middle_Ovld_Dly"] && m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm_phsC"] == 0)
       {
           //如果C相中度过载发生，判断C相重度过载是否发生，C相重度过载未发生，上报C相中度过载事件，C相重度过载发生，不上报C相中度过载事件
           if(m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm_phsC"] == 0)
           {
               m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm_phsC"] = 1;
               //发送遥信
               CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_middle_Ovld_Alm_phsC");
           }
       }
       //C相中度过载和C相重度过载同时发生，C相中度过载复归
       if(m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm_phsC"] == 1 && m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm_phsC"] == 1)
       {
           m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm_phsC"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_middle_Ovld_Alm_phsC");
       }
       if(m_voltage_time.PTOC_middle_Ovld_phsC_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm_phsC"] == 1)
       {
           m_pdAnalyzer_value_YX["PTOC_middle_Ovld_Alm_phsC"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_middle_Ovld_Alm_phsC");
       }
       //C相反向中度过载
       if(m_voltage_time.Re_PTOC_middle_Ovld_phsC_Tm_s >=  m_Param_value["Re_PTOC_middle_Ovld_Dly"] && m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm_phsC"] == 0)
       {
           //如果C相反向中度过载发生，判断C相反向重度过载是否发生，C相反向重度过载未发生，上报C相反向中度过载事件，C相反向重度过载发生，不上报C相反向中度过载事件
           if(m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm_phsC"] == 0)
           {
               m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm_phsC"] = 1;
               //发送遥信
               CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_middle_Ovld_Alm_phsC");
           }
       }
       //C相反向中度过载和C相反向重度过载同时发生，C相反向中度过载复归
       if(m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm_phsC"] == 1 && m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm_phsC"] == 1)
       {
           m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm_phsC"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_middle_Ovld_Alm_phsC");
       }
       if(m_voltage_time.Re_PTOC_middle_Ovld_phsC_Tm_s == 0 && m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm_phsC"] == 1)
       {
           m_pdAnalyzer_value_YX["Re_PTOC_middle_Ovld_Alm_phsC"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_middle_Ovld_Alm_phsC");
       }
   }
   //
   
    //---------------------------------配变重度过载-------------------------------------------------//
    if(m_Param_value["PTOC_severe_Ovld_Enable"] > 0.5)//配变轻度过载投入 
    {
       // 正向 Alm: 负荷越线时间大于定值 并且上一次事件为0  报遥信事件 
       if (m_voltage_time.PTOC_severe_Ovld_Alm_Tm_s >= m_Param_value["PTOC_severe_Ovld_Dly"] && m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm"] == 0)
       {
            //如果重度发生，判断异常过载是否发生，异常过载未发生，上报重度过载事件，异常过载发生，不上报重度过载事件
            //重度过载和异常过载同时发生，重度过载复归
            //异常过载复归，重度过载时间清零 
           if(m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm"] == 0)
           {
                m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm"] = 1;
                //发送遥信
                CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_severe_Ovld_Alm");
           }
       }
       if(m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm"] == 1 && m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm"] == 1)
       {
            m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm"] = 0;
            //发送遥信 
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_severe_Ovld_Alm");
       }
       // Alm: 负荷越线时间为0 并且上一次事件为1  报遥信事件 
       if(m_voltage_time.PTOC_severe_Ovld_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm"] == 1)
       {
           m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_severe_Ovld_Alm");
       } 

       //反向 
       if (m_voltage_time.Re_PTOC_severe_Ovld_Alm_Tm_s >= m_Param_value["PTOC_severe_Ovld_Dly"] && m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm"] == 0  && m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm"] == 0)
       {
           m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm"] = 1;  
           //发送遥信 
           CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_severe_Ovld_Alm");
       }
       if((m_voltage_time.Re_PTOC_severe_Ovld_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm"] == 1) || (m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm"] == 1 && m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm"] == 1))
       {
           m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm"] = 0; 
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_severe_Ovld_Alm");
       } 
       
       //A
       if(m_voltage_time.PTOC_severe_Ovld_phsA_Tm_s >=  m_Param_value["PTOC_severe_Ovld_Dly"] && m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm_phsA"] == 0 && m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm_phsA"] == 0 )
       {
            m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm_phsA"] = 1; 
            //发送遥信 
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_severe_Ovld_Alm_phsA");
       }
       if((m_voltage_time.PTOC_severe_Ovld_phsA_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm_phsA"] == 1)||(m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm_phsA"] == 1 && m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm_phsA"] == 1))
       {
           m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm_phsA"] = 0; 
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_severe_Ovld_Alm_phsA");
       }
       if(m_voltage_time.Re_PTOC_severe_Ovld_phsA_Tm_s >=  m_Param_value["Re_PTOC_severe_Ovld_Dly"] && m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm_phsA"] == 0 && m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm_phsA"] == 0)
       {
            m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm_phsA"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_severe_Ovld_Alm_phsA");
       }
       if((m_voltage_time.Re_PTOC_severe_Ovld_phsA_Tm_s == 0 && m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm_phsA"] == 1) || (m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm_phsA"] == 1 && m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm_phsA"] == 1))
       {
           m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm_phsA"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_severe_Ovld_Alm_phsA");
       }

       //B相重度过载告警
       if(m_voltage_time.PTOC_severe_Ovld_phsB_Tm_s >=  m_Param_value["PTOC_severe_Ovld_Dly"] && m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm_phsB"] == 0 && m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm_phsB"] == 0 )
       {
            m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm_phsB"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_severe_Ovld_Alm_phsB");
       }
       if((m_voltage_time.PTOC_severe_Ovld_phsB_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm_phsB"] == 1)||(m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm_phsB"] == 1 && m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm_phsB"] == 1))
       {
           m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm_phsB"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_severe_Ovld_Alm_phsB");
       }
       //B相反向重度过载告警
       if(m_voltage_time.Re_PTOC_severe_Ovld_phsB_Tm_s >=  m_Param_value["Re_PTOC_severe_Ovld_Dly"] && m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm_phsB"] == 0 && m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm_phsB"] == 0)
       {
            m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm_phsB"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_severe_Ovld_Alm_phsB");
       }
       if((m_voltage_time.Re_PTOC_severe_Ovld_phsB_Tm_s == 0 && m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm_phsB"] == 1) || (m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm_phsB"] == 1 && m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm_phsB"] == 1))
       {
           m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm_phsB"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_severe_Ovld_Alm_phsB");
       }

       //C相重度过载告警
       if(m_voltage_time.PTOC_severe_Ovld_phsC_Tm_s >=  m_Param_value["PTOC_severe_Ovld_Dly"] && m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm_phsC"] == 0 && m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm_phsC"] == 0 )
       {
            m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm_phsC"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_severe_Ovld_Alm_phsC");
       }
       if((m_voltage_time.PTOC_severe_Ovld_phsC_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm_phsC"] == 1)||(m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm_phsC"] == 1 && m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm_phsC"] == 1))
       {
           m_pdAnalyzer_value_YX["PTOC_severe_Ovld_Alm_phsC"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_severe_Ovld_Alm_phsC");
       }
       //C相反向重度过载告警
       if(m_voltage_time.Re_PTOC_severe_Ovld_phsC_Tm_s >=  m_Param_value["Re_PTOC_severe_Ovld_Dly"] && m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm_phsC"] == 0 && m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm_phsC"] == 0)
       {
            m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm_phsC"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_severe_Ovld_Alm_phsC");
       }
       if((m_voltage_time.Re_PTOC_severe_Ovld_phsC_Tm_s == 0 && m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm_phsC"] == 1) || (m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm_phsC"] == 1 && m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm_phsC"] == 1))
       {
           m_pdAnalyzer_value_YX["Re_PTOC_severe_Ovld_Alm_phsC"] = 0;
           //发送遥信
           CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_severe_Ovld_Alm_phsC");
       }

    }
    
    //---------------------------------配变异常过载-------------------------------------------------//l
    if(m_Param_value["PTOC_abnormal_Ovld_Enable"] > 0.5)//配变轻度过载投入 
    {
        // 正向 Alm: 负荷越线时间大于定值 并且上一次事件为0  报遥信事件 
        if (m_voltage_time.PTOC_abnormal_Ovld_Alm_Tm_s >= m_Param_value["PTOC_abnormal_Ovld_Dly"] && m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm"] = 1;  
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_abnormal_Ovld_Alm");
        }
         // Alm: 负荷越线时间为0 并且上一次事件为1  报遥信事件 
        if(m_voltage_time.PTOC_abnormal_Ovld_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_abnormal_Ovld_Alm");
        } 
        //反向
        if (m_voltage_time.Re_PTOC_abnormal_Ovld_Alm_Tm_s >= m_Param_value["PTOC_abnormal_Ovld_Dly"] && m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_abnormal_Ovld_Alm");
        }
        if(m_voltage_time.Re_PTOC_abnormal_Ovld_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_abnormal_Ovld_Alm");
        } 
        //A
        if(m_voltage_time.PTOC_abnormal_Ovld_phsA_Tm_s >=  m_Param_value["PTOC_abnormal_Ovld_Dly"] && m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm_phsA"] == 0)
        {
            m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm_phsA"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_abnormal_Ovld_Alm_phsA");
        }
        if(m_voltage_time.PTOC_abnormal_Ovld_phsA_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm_phsA"] == 1)
        {
            m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm_phsA"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_abnormal_Ovld_Alm_phsA");
        }
        if(m_voltage_time.Re_PTOC_abnormal_Ovld_phsA_Tm_s >=  m_Param_value["Re_PTOC_abnormal_Ovld_Dly"] && m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm_phsA"] == 0)
        {
            m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm_phsA"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_abnormal_Ovld_Alm_phsA");
        }
        if(m_voltage_time.PTOC_abnormal_Ovld_phsA_Tm_s == 0 && m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm_phsA"] == 1)
        {
            m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm_phsA"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_abnormal_Ovld_Alm_phsA");
        }
        //B
        if(m_voltage_time.PTOC_abnormal_Ovld_phsB_Tm_s >=  m_Param_value["PTOC_abnormal_Ovld_Dly"] && m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm_phsB"] == 0)
        {
            m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm_phsB"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_abnormal_Ovld_Alm_phsB");
        }
        if(m_voltage_time.PTOC_abnormal_Ovld_phsB_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm_phsB"] == 1)
        {
            m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm_phsB"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_abnormal_Ovld_Alm_phsB");
        }
        if(m_voltage_time.Re_PTOC_abnormal_Ovld_phsB_Tm_s >=  m_Param_value["Re_PTOC_abnormal_Ovld_Dly"] && m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm_phsB"] == 0)
        {
            m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm_phsB"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_abnormal_Ovld_Alm_phsB");
        }
        if(m_voltage_time.PTOC_abnormal_Ovld_phsB_Tm_s == 0 && m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm_phsB"] == 1)
        {
            m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm_phsB"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_abnormal_Ovld_Alm_phsB");
        }
        //C
        if(m_voltage_time.PTOC_abnormal_Ovld_phsC_Tm_s >=  m_Param_value["PTOC_abnormal_Ovld_Dly"] && m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm_phsC"] == 0)
        {
            m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm_phsC"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_abnormal_Ovld_Alm_phsC");
        }
        if(m_voltage_time.PTOC_abnormal_Ovld_phsC_Tm_s == 0 && m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm_phsC"] == 1)
        {
            m_pdAnalyzer_value_YX["PTOC_abnormal_Ovld_Alm_phsC"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOC_abnormal_Ovld_Alm_phsC");
        }
        if(m_voltage_time.Re_PTOC_abnormal_Ovld_phsC_Tm_s >=  m_Param_value["Re_PTOC_abnormal_Ovld_Dly"] && m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm_phsC"] == 0)
        {
            m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm_phsC"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_abnormal_Ovld_Alm_phsC");
        }
        if(m_voltage_time.PTOC_abnormal_Ovld_phsC_Tm_s == 0 && m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm_phsC"] == 1)
        {
            m_pdAnalyzer_value_YX["Re_PTOC_abnormal_Ovld_Alm_phsC"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("Re_PTOC_abnormal_Ovld_Alm_phsC");
        }
    }
    //-------------------------------剩余电流超限------------------------------------------------//
    if(m_Param_value["ResA_Enable"] > 0.5)//剩余电流超限
    {

        mskprintf("Res_Alm_Tm_s : %d, ResA_Dly:%d, Res_Alm:%d \n",m_voltage_time.Res_Alm_Tm_s , m_Param_value["ResA_Dly"] , m_pdAnalyzer_value_YX["Res_Alm"]);
        
        // Alm: 剩余电流超限时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.Res_Alm_Tm_s > m_Param_value["ResA_Dly"] && m_pdAnalyzer_value_YX["Res_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["Res_Alm"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("Res_Alm");
        }
        // Alm: 剩余电流超限时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.Res_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["Res_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["Res_Alm"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("Res_Alm");
        } 
    }
    //-------------------------------功率因数越限------------------------------------------------//
    if(m_Param_value["PTUPF_Enable"] > 0.5)//功率因数越限
    {
        mskprintf("PTUPF_Alm_Tm_s:%d, PTUPF_Dly:%f,PTUPF_Alm:%d.",m_voltage_time.PTUPF_Alm_Tm_s , m_Param_value["PTUPF_Dly"] , m_pdAnalyzer_value_YX["PTUPF_Alm"]);
        // Alm: 功率因数越限时间大于定值 并且上一次事件为0  报遥信事件
        if (m_voltage_time.PTUPF_Alm_Tm_s >= m_Param_value["PTUPF_Dly"] && m_pdAnalyzer_value_YX["PTUPF_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUPF_Alm"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUPF_Alm");
        }
        // Alm: 功率因数越限时间为0 并且上一次事件为1  报遥信事件
        if(m_voltage_time.PTUPF_Alm_Tm_s == 0 && m_pdAnalyzer_value_YX["PTUPF_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUPF_Alm"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUPF_Alm");
        } 
        //A 
        mskprintf("PTUPF_phsA_Tm_s:%d, PTUPF_Dly:%f,PTUPF_Alm:%d.",m_voltage_time.PTUPF_phsA_Tm_s , m_Param_value["PTUPF_Dly"] , m_pdAnalyzer_value_YX["PTUPF_Alm"]);
        if(m_voltage_time.PTUPF_phsA_Tm_s >=  m_Param_value["PTUPF_Dly"] && m_pdAnalyzer_value_YX["PTUPF_Op_phsA"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUPF_Op_phsA"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUPF_Op_phsA");
        }
        else if(m_voltage_time.PTUPF_phsA_FG_Tm_s >= m_Param_value["PTUPF_FG_Dly"] && m_pdAnalyzer_value_YX["PTUPF_Op_phsA"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUPF_Op_phsA"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUPF_Op_phsA");
        }

        //B
        mskprintf("PTUPF_phsB_Tm_s:%d, PTUPF_Dly:%f,PTUPF_Alm:%d.",m_voltage_time.PTUPF_phsB_Tm_s , m_Param_value["PTUPF_Dly"] , m_pdAnalyzer_value_YX["PTUPF_Alm"]);
        if(m_voltage_time.PTUPF_phsB_Tm_s >=  m_Param_value["PTUPF_Dly"] && m_pdAnalyzer_value_YX["PTUPF_Op_phsB"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUPF_Op_phsB"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUPF_Op_phsB");
        }
        else if(m_voltage_time.PTUPF_phsB_FG_Tm_s >= m_Param_value["PTUPF_FG_Dly"] && m_pdAnalyzer_value_YX["PTUPF_Op_phsB"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUPF_Op_phsB"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUPF_Op_phsB");
        }

        //C
        mskprintf("PTUPF_phsC_Tm_s:%d, PTUPF_Dly:%f,PTUPF_Alm:%d.",m_voltage_time.PTUPF_phsC_Tm_s , m_Param_value["PTUPF_Dly"] , m_pdAnalyzer_value_YX["PTUPF_Alm"]);
        if(m_voltage_time.PTUPF_phsC_Tm_s >=  m_Param_value["PTUPF_Dly"] && m_pdAnalyzer_value_YX["PTUPF_Op_phsC"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUPF_Op_phsC"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUPF_Op_phsC");
        }
        else if(m_voltage_time.PTUPF_phsC_FG_Tm_s >= m_Param_value["PTUPF_FG_Dly"] && m_pdAnalyzer_value_YX["PTUPF_Op_phsC"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUPF_Op_phsC"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUPF_Op_phsC");
        }

        //第一象限功率因数越限
        mskprintf("PTUPF_Alm_Tm_s:%d, PTUPF_Dly:%f,PTUPF_Alm:%d.",m_voltage_time.PTUPF_Alm_Tm_s , m_Param_value["PTUPF_Dly"] , m_pdAnalyzer_value_YX["PTUPF_Alm"]);
        mskprintf("TotW:%f, TotVar:%f.",m_ADC_value["TotW"],m_ADC_value["TotVar"]);
        if(m_voltage_time.PTUPF_Alm_Tm_s >= m_Param_value["PTUPF_Dly"] && m_pdAnalyzer_value_YX["TotPF_Op_I_Alm"] == 0 && m_ADC_value["TotW"] > 0 && m_ADC_value["TotVar"] >0)
        {
            m_pdAnalyzer_value_YX["TotPF_Op_I_Alm"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("TotPF_Op_I_Alm");
        }
        else  if(m_voltage_time.PTUPF_Alm_Tm_s >= m_Param_value["PTUPF_FG_Dly"] && m_pdAnalyzer_value_YX["TotPF_Op_I_Alm"] == 0 && m_ADC_value["TotW"] > 0 && m_ADC_value["TotVar"] >0)
        {
            m_pdAnalyzer_value_YX["TotPF_Op_I_Alm"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("TotPF_Op_I_Alm");
        }
        //第二象限功率因素越限
        if(m_voltage_time.PTUPF_Alm_Tm_s >= m_Param_value["PTUPF_Dly"] && m_pdAnalyzer_value_YX["TotPF_Op_II_Alm"] == 0 && m_ADC_value["TotW"] < 0 && m_ADC_value["TotVar"] >0)
        {
            m_pdAnalyzer_value_YX["TotPF_Op_II_Alm"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("TotPF_Op_II_Alm");
        }
        else  if(m_voltage_time.PTUPF_Alm_Tm_s >= m_Param_value["PTUPF_FG_Dly"] && m_pdAnalyzer_value_YX["TotPF_Op_II_Alm"] == 0 && m_ADC_value["TotW"] < 0 && m_ADC_value["TotVar"] >0)
        {
            m_pdAnalyzer_value_YX["TotPF_Op_II_Alm"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("TotPF_Op_II_Alm");
        }
        //三
        if(m_voltage_time.PTUPF_Alm_Tm_s >= m_Param_value["PTUPF_Dly"] && m_pdAnalyzer_value_YX["TotPF_Op_III_Alm"] == 0 && m_ADC_value["TotW"] < 0 && m_ADC_value["TotVar"] <0)
        {
            m_pdAnalyzer_value_YX["TotPF_Op_III_Alm"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("TotPF_Op_III_Alm");
        }
        else  if(m_voltage_time.PTUPF_Alm_Tm_s >= m_Param_value["PTUPF_FG_Dly"] && m_pdAnalyzer_value_YX["TotPF_Op_III_Alm"] == 0 && m_ADC_value["TotW"] < 0 && m_ADC_value["TotVar"] <0)
        {
            m_pdAnalyzer_value_YX["TotPF_Op_III_Alm"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("TotPF_Op_III_Alm");
        }
        //四
        if(m_voltage_time.PTUPF_Alm_Tm_s >= m_Param_value["PTUPF_Dly"] && m_pdAnalyzer_value_YX["TotPF_Op_IV_Alm"] == 0 && m_ADC_value["TotW"] > 0 && m_ADC_value["TotVar"] <0)
        {
            m_pdAnalyzer_value_YX["TotPF_Op_IV_Alm"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("TotPF_Op_IV_Alm");
        }
        else  if(m_voltage_time.PTUPF_Alm_Tm_s >= m_Param_value["PTUPF_FG_Dly"] && m_pdAnalyzer_value_YX["TotPF_Op_IV_Alm"] == 0 && m_ADC_value["TotW"] > 0 && m_ADC_value["TotVar"] <0)
        {
            m_pdAnalyzer_value_YX["TotPF_Op_IV_Alm"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("TotPF_Op_IV_Alm");
        }
    }

    //-------------------------------谐波越限------------------------------------------------//
    if(m_Param_value["ThdPhV_Op_V_Enable"] > 0.5) //电压谐波越限投入 
    {
        mskprintf("ThdPhV_Op_V_Tm_s:%d,ThdPhV_Op_V_Dly:%d,ThdPhV_Op_Alm:%d.",m_voltage_time.ThdPhV_Op_V_Tm_s,m_Param_value["ThdPhV_Op_V_Dly"],m_pdAnalyzer_value_YX["ThdPhV_Op_Alm"]);
        // Alm: 越限时间大于定值 并且上一次事件为0  报遥信事件
        if(m_voltage_time.ThdPhV_Op_V_Tm_s >= m_Param_value["ThdPhV_Op_V_Dly"] && m_pdAnalyzer_value_YX["ThdPhV_Op_Alm"] == 0 )
        {
            m_pdAnalyzer_value_YX["ThdPhV_Op_Alm"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ThdPhV_Op_Alm");
        }
        else if(m_voltage_time.ThdPhV_Op_V_Tm_s == 0 && m_pdAnalyzer_value_YX["ThdPhV_Op_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["ThdPhV_Op_Alm"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ThdPhV_Op_Alm");
        }

        //A相电压谐波越限
        mskprintf("ThdPhV_Op_PhsA_Tm_s:%d,ThdPhV_Op_PhsA_Dly:%d,ThdPhV_Op_Alm:%d.",m_voltage_time.ThdPhV_Op_PhsA_Tm_s,m_Param_value["ThdPhV_Op_PhsA_Dly"],m_pdAnalyzer_value_YX["ThdPhV_Op_Alm"]);
        // Alm: 越限时间大于定值 并且上一次事件为0  报遥信事件
        if(m_voltage_time.ThdPhV_Op_PhsA_Tm_s >= m_Param_value["ThdPhV_Op_V_Dly"] && m_pdAnalyzer_value_YX["ThdPhV_Op_phsA_Alm"] == 0 )
        {
            m_pdAnalyzer_value_YX["ThdPhV_Op_phsA_Alm"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ThdPhV_Op_phsA_Alm");
            m_voltage_time.ThdPhV_Tm_Day++;
            m_voltage_time.ThdPhV_Tm_Mon++;
        }
        else if(m_voltage_time.ThdPhV_Op_PhsA_Tm_s == 0 && m_pdAnalyzer_value_YX["ThdPhV_Op_phsA_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["ThdPhV_Op_phsA_Alm"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ThdPhV_Op_phsA_Alm");
        }

        //B
        mskprintf("ThdPhV_Op_PhsB_Tm_s:%d,ThdPhV_Op_PhsB_Dly:%d,ThdPhV_Op_Alm:%d.",m_voltage_time.ThdPhV_Op_PhsB_Tm_s,m_Param_value["ThdPhV_Op_PhsB_Dly"],m_pdAnalyzer_value_YX["ThdPhV_Op_Alm"]);
        // Alm: 越限时间大于定值 并且上一次事件为0  报遥信事件
        if(m_voltage_time.ThdPhV_Op_PhsB_Tm_s >= m_Param_value["ThdPhV_Op_V_Dly"] && m_pdAnalyzer_value_YX["ThdPhV_Op_PhsB_Alm"] == 0 )
        {
            m_pdAnalyzer_value_YX["ThdPhV_Op_PhsB_Alm"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ThdPhV_Op_PhsB_Alm");
        }
        else if(m_voltage_time.ThdPhV_Op_PhsB_Tm_s == 0 && m_pdAnalyzer_value_YX["ThdPhV_Op_PhsB_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["ThdPhV_Op_PhsB_Alm"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ThdPhV_Op_PhsB_Alm");
        }

        //C
        mskprintf("ThdPhV_Op_PhsC_Tm_s:%d,ThdPhV_Op_PhsC_Dly:%d,ThdPhV_Op_Alm:%d.",m_voltage_time.ThdPhV_Op_PhsC_Tm_s,m_Param_value["ThdPhV_Op_PhsC_Dly"],m_pdAnalyzer_value_YX["ThdPhV_Op_Alm"]);
        // Alm: 越限时间大于定值 并且上一次事件为0  报遥信事件
        if(m_voltage_time.ThdPhV_Op_PhsC_Tm_s >= m_Param_value["ThdPhV_Op_V_Dly"] && m_pdAnalyzer_value_YX["ThdPhV_Op_PhsC_Alm"] == 0 )
        {
            m_pdAnalyzer_value_YX["ThdPhV_Op_PhsC_Alm"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ThdPhV_Op_PhsC_Alm");
        }
        else if(m_voltage_time.ThdPhV_Op_PhsC_Tm_s == 0 && m_pdAnalyzer_value_YX["ThdPhV_Op_PhsC_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["ThdPhV_Op_PhsC_Alm"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ThdPhV_Op_PhsC_Alm");
        }
    }
    if(m_Param_value["ThdA_Op_A_Enable"] > 0.5) //电流谐波越限投入 
    {
        mskprintf("ThdA_Op_A_Tm_s:%d,ThdA_Op_A_Dly:%d,ThdA_Op_Alm:%d.",m_voltage_time.ThdA_Op_A_Tm_s,m_Param_value["ThdA_Op_A_Dly"],m_pdAnalyzer_value_YX["ThdA_Op_Alm"]);
        // Alm: 电流谐波越限时间大于定值 并且上一次事件为0  报遥信事件
        if(m_voltage_time.ThdA_Op_A_Tm_s >= m_Param_value["ThdA_Op_A_Dly"] && m_pdAnalyzer_value_YX["ThdA_Op_Alm"] == 0 )
        {
            m_pdAnalyzer_value_YX["ThdA_Op_Alm"] = 1;
            //发送遥信 
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ThdA_Op_Alm");
            m_voltage_time.ThdA_Tm_Day++;
            m_voltage_time.ThdA_Tm_Mon++;
        }
        else if(m_voltage_time.ThdA_Op_A_Tm_s == 0 && m_pdAnalyzer_value_YX["ThdA_Op_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["ThdA_Op_Alm"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ThdA_Op_Alm");
        }

         //A相电流谐波越限
         mskprintf("ThdA_Op_PhsA_Tm_s:%d,ThdA_Op_PhsA_Dly:%d,ThdA_Op_Alm:%d.",m_voltage_time.ThdA_Op_PhsA_Tm_s,m_Param_value["ThdA_Op_PhsA_Dly"],m_pdAnalyzer_value_YX["ThdA_Op_Alm"]);
         // Alm: 越限时间大于定值 并且上一次事件为0  报遥信事件
         if(m_voltage_time.ThdA_Op_PhsA_Tm_s >= m_Param_value["ThdA_Op_V_Dly"] && m_pdAnalyzer_value_YX["ThdA_Op_phsA_Alm"] == 0 )
         {
             m_pdAnalyzer_value_YX["ThdA_Op_phsA_Alm"] = 1;
             //发送遥信
             CTaskManager::CreateInstance().MqttPackNotificationData_2("ThdA_Op_phsA_Alm");
         }
         else if(m_voltage_time.ThdA_Op_PhsA_Tm_s == 0 && m_pdAnalyzer_value_YX["ThdA_Op_phsA_Alm"] == 1)
         {
             m_pdAnalyzer_value_YX["ThdA_Op_phsA_Alm"] = 0;
             //发送遥信
             CTaskManager::CreateInstance().MqttPackNotificationData_2("ThdA_Op_phsA_Alm");
         }
 
         //B
         mskprintf("ThdA_Op_PhsB_Tm_s:%d,ThdA_Op_PhsB_Dly:%d,ThdA_Op_Alm:%d.",m_voltage_time.ThdA_Op_PhsB_Tm_s,m_Param_value["ThdA_Op_PhsB_Dly"],m_pdAnalyzer_value_YX["ThdA_Op_Alm"]);
         // Alm: 越限时间大于定值 并且上一次事件为0  报遥信事件
         if(m_voltage_time.ThdA_Op_PhsB_Tm_s >= m_Param_value["ThdA_Op_V_Dly"] && m_pdAnalyzer_value_YX["ThdA_Op_PhsB_Alm"] == 0 )
         {
             m_pdAnalyzer_value_YX["ThdA_Op_PhsB_Alm"] = 1;
             //发送遥信
             CTaskManager::CreateInstance().MqttPackNotificationData_2("ThdA_Op_PhsB_Alm");
         }
         else if(m_voltage_time.ThdA_Op_PhsB_Tm_s == 0 && m_pdAnalyzer_value_YX["ThdA_Op_PhsB_Alm"] == 1)
         {
             m_pdAnalyzer_value_YX["ThdA_Op_PhsB_Alm"] = 0;
             //发送遥信
             CTaskManager::CreateInstance().MqttPackNotificationData_2("ThdA_Op_PhsB_Alm");
         }
 
         //C
         mskprintf("ThdA_Op_PhsC_Tm_s:%d,ThdA_Op_PhsC_Dly:%d,ThdA_Op_Alm:%d.",m_voltage_time.ThdA_Op_PhsC_Tm_s,m_Param_value["ThdA_Op_PhsC_Dly"],m_pdAnalyzer_value_YX["ThdA_Op_Alm"]);
         // Alm: 越限时间大于定值 并且上一次事件为0  报遥信事件
         if(m_voltage_time.ThdA_Op_PhsC_Tm_s >= m_Param_value["ThdA_Op_V_Dly"] && m_pdAnalyzer_value_YX["ThdA_Op_PhsC_Alm"] == 0 )
         {
             m_pdAnalyzer_value_YX["ThdA_Op_PhsC_Alm"] = 1;
             //发送遥信
             CTaskManager::CreateInstance().MqttPackNotificationData_2("ThdA_Op_PhsC_Alm");
         }
         else if(m_voltage_time.ThdA_Op_PhsC_Tm_s == 0 && m_pdAnalyzer_value_YX["ThdA_Op_PhsC_Alm"] == 1)
         {
             m_pdAnalyzer_value_YX["ThdA_Op_PhsC_Alm"] = 0;
             //发送遥信
             CTaskManager::CreateInstance().MqttPackNotificationData_2("ThdA_Op_PhsC_Alm");
         }
    }
    //-------------------------------过频------------------------------------------------//
    if(m_Param_value["PTOF_Enable"] > 0.5) //过频投入
    {
        //A 
        if(m_voltage_time.PTOF_Op_phsA_Tm_s >= m_Param_value["PTOF_Dly"] &&  m_pdAnalyzer_value_YX["PTOF_Op_phsA"] == 0)
        {
            m_pdAnalyzer_value_YX["PTOF_Op_phsA"] == 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Op_phsA");
        }
        else if(m_voltage_time.PTOF_Op_phsA_Tm_s == 0 &&  m_pdAnalyzer_value_YX["PTOF_Op_phsA"] == 1)
        {
            m_pdAnalyzer_value_YX["PTOF_Op_phsA"] == 0;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Op_phsA");
        }
        //B
        if(m_voltage_time.PTOF_Op_phsB_Tm_s>= m_Param_value["PTOF_Dly"] &&  m_pdAnalyzer_value_YX["PTOF_Op_phsB"] == 0)
        {
            m_pdAnalyzer_value_YX["PTOF_Op_phsB"] == 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Op_phsB");
        }
        else if(m_voltage_time.PTOF_Op_phsB_Tm_s == 0 &&  m_pdAnalyzer_value_YX["PTOF_Op_phsB"] == 1)
        {
            m_pdAnalyzer_value_YX["PTOF_Op_phsB"] == 0;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Op_phsB");
        }
        //C
        if(m_voltage_time.PTOF_Op_phsC_Tm_s>= m_Param_value["PTOF_Dly"] &&  m_pdAnalyzer_value_YX["PTOF_Op_phsC"] == 0)
        {
            m_pdAnalyzer_value_YX["PTOF_Op_phsC"] == 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Op_phsC");
        }
        else if(m_voltage_time.PTOF_Op_phsC_Tm_s == 0 &&  m_pdAnalyzer_value_YX["PTOF_Op_phsC"] == 1)
        {
            m_pdAnalyzer_value_YX["PTOF_Op_phsC"] == 0;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Op_phsC");
        }
        //总
        if(m_voltage_time.PTOF_Op_Tm_s>= m_Param_value["PTOF_Dly"] &&  m_pdAnalyzer_value_YX["PTOF_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["PTOF_Alm"] == 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Alm");
        }
        else if(m_voltage_time.PTOF_Op_Tm_s == 0 &&  m_pdAnalyzer_value_YX["PTOF_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["PTOF_Alm"] == 0;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTOF_Alm");
        }
    }
    //-------------------------------欠频------------------------------------------------//
    if(m_Param_value["PTUF_Enable"] > 0.5) //欠频投入
    {
        //A 
        if(m_voltage_time.PTUF_Op_phsA_Tm_s >= m_Param_value["PTUF_Dly"] &&  m_pdAnalyzer_value_YX["PTUF_Op_phsA"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUF_Op_phsA"] == 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUF_Op_phsA");
        }
        else if(m_voltage_time.PTUF_Op_phsA_Tm_s == 0 &&  m_pdAnalyzer_value_YX["PTUF_Op_phsA"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUF_Op_phsA"] == 0;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUF_Op_phsA");
        }
        //B
        if(m_voltage_time.PTUF_Op_phsB_Tm_s>= m_Param_value["PTUF_Dly"] &&  m_pdAnalyzer_value_YX["PTUF_Op_phsB"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUF_Op_phsB"] == 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUF_Op_phsB");
        }
        else if(m_voltage_time.PTUF_Op_phsB_Tm_s == 0 &&  m_pdAnalyzer_value_YX["PTUF_Op_phsB"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUF_Op_phsB"] == 0;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUF_Op_phsB");
        }
        //C
        if(m_voltage_time.PTUF_Op_phsC_Tm_s>= m_Param_value["PTUF_Dly"] &&  m_pdAnalyzer_value_YX["PTUF_Op_phsC"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUF_Op_phsC"] == 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUF_Op_phsC");
        }
        else if(m_voltage_time.PTUF_Op_phsC_Tm_s == 0 &&  m_pdAnalyzer_value_YX["PTUF_Op_phsC"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUF_Op_phsC"] == 0;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUF_Op_phsC");
        }
        //总
        if(m_voltage_time.PTUF_Op_Tm_s>= m_Param_value["PTUF_Dly"] &&  m_pdAnalyzer_value_YX["PTUF_Alm"] == 0)
        {
            m_pdAnalyzer_value_YX["PTUF_Alm"] == 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUF_Alm");
        }
        else if(m_voltage_time.PTUF_Op_Tm_s == 0 &&  m_pdAnalyzer_value_YX["PTUF_Alm"] == 1)
        {
            m_pdAnalyzer_value_YX["PTUF_Alm"] == 0;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("PTUF_Alm");
        }
    }

}


int CParamManager::judge_m_TCalOvertime()
{
    //mskprintf("ImbNgLoad_Alm: %d\n",m_pdAnalyzer_value_YX["ImbNgLoad_Alm"]);
    //负荷不平衡度事件 
    if (m_T.CalOvertime() && m_T.IsStart())    
    {        
        mskprintf("m_T 超时\n");
        if(1)
        //if(m_Param_value["ImbA_Enable"])
        {
            m_pdAnalyzer_value_YX["ImbNgLoad_Alm"]=1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ImbNgLoad_Alm");
        }
        m_T.StopCount();
    }

    //相电流不平衡度事件
    if (m_T_Imb_YX.CalOvertime() && m_T_Imb_YX.IsStart())    
    {        
        mskprintf("m_T_Imb_YX 超时\n");
        //if(m_Param_value["ImbA_Enable"]) 
        if(1)
        {
            m_pdAnalyzer_value_YX["ImbNgA_Alm"]=1;
			IEC_LOG_RECORD(eRunType," 电流不平衡 ImbNgA_Alm :%d",m_pdAnalyzer_value_YX["ImbNgA_Alm"]);  
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ImbNgA_Alm");
        }        
        m_T_Imb_YX.StopCount();
    }
    //轻度电流不平衡度事件
    if(m_T_Imb_mild_YX.CalOvertime() && m_T_Imb_mild_YX.IsStart())
    {
        if(1)
        //if(m_Param_value["ImbA_mild_Enable"])
        {
            m_pdAnalyzer_value_YX["ImbNgA_mild_Alm"] = 1;
            IEC_LOG_RECORD(eRunType," 轻度电流不平衡 ImbNgA_Alm :%d",m_pdAnalyzer_value_YX["ImbNgA_mild_Alm"]); 
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ImbNgA_mild_Alm");
        }
        m_T_Imb_mild_YX.StopCount();
    }
    //中度电流不平衡事件
    if(m_T_Imb_middle_YX.CalOvertime() && m_T_Imb_middle_YX.IsStart())
    {        
        //if(m_Param_value["ImbA_middle_Enable"])
        if(1)
        {
            m_pdAnalyzer_value_YX["ImbNgA_middle_Alm"] = 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ImbNgA_middle_Alm");
            IEC_LOG_RECORD(eRunType," 中度电流不平衡 ImbNgA_Alm :%d",m_pdAnalyzer_value_YX["ImbNgA_middle_Alm"]); 
        }
        m_T_Imb_middle_YX.StopCount();
    }

    //重度电流不平衡事件
    if(m_T_Imb_severe_YX.CalOvertime() && m_T_Imb_severe_YX.IsStart())
    {
        //if(m_Param_value["ImbA_severe_Enable"])
        if(1)
        {
            m_pdAnalyzer_value_YX["ImbNgA_severe_Alm"] = 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ImbNgA_severe_Alm");
            IEC_LOG_RECORD(eRunType," 重度电流不平衡 ImbNgA_Alm :%d",m_pdAnalyzer_value_YX["ImbNgA_severe_Alm"]);
        }
        m_T_Imb_severe_YX.StopCount();
    }
    
    //零线电流不平衡度事件
    if (m_T_ZImb_YX.CalOvertime() && m_T_ZImb_YX.IsStart())    
    {        
        mskprintf("m_T_ZImb_YX 超时\n");
        
        //if(m_Param_value["ImbA_Enable"])
        if(1)
        {
            m_pdAnalyzer_value_YX["ImbNgA_Alm_phsN"]=1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("ImbNgA_Alm_phsN");
            IEC_LOG_RECORD(eRunType," 零线电流不平衡 ImbNgA_Alm :%d",m_pdAnalyzer_value_YX["ImbNgA_Alm_phsN"]);
        }
        m_T_ZImb_YX.StopCount();
    } 

    return 0;
}

float CParamManager::findMiddle(float a, float b, float c)
{
    if ((a < b && b < c) || (c < b && b < a)) 
    {
        return b;
    } 
    else if ((b < a && a < c) || (c < a && a < b)) 
    {
        return a;
    } 
    else
        return c;
}

void CParamManager::initYXData()
{
    //初始化 遥信统计时间 为0
    m_voltage_time.PTOV_Op_phsA_Tm_s = 0;
    m_voltage_time.PTOV_Op_phsB_Tm_s = 0;
    m_voltage_time.PTOV_Op_phsC_Tm_s = 0;
    m_voltage_time.PTOV_Alm_Tm_s = 0;

    m_voltage_time.SeqOV_Alm_Tm_s = 0;

    m_voltage_time.PTUV_Op_phsA_Tm_s = 0;
    m_voltage_time.PTUV_Op_phsB_Tm_s = 0;
    m_voltage_time.PTUV_Op_phsC_Tm_s = 0;
    m_voltage_time.PTUV_Alm_Tm_s = 0;

    m_voltage_time.PTUV_Loss_Op_phsA_Tm_s = 0;  //A相连续矢压时间，单位 秒
    m_voltage_time.PTUV_Loss_Op_phsB_Tm_s = 0;  //B相连续矢压时间，单位 秒
    m_voltage_time.PTUV_Loss_Op_phsC_Tm_s = 0;  //C相连续矢压时间，单位 秒
    m_voltage_time.PTUV_Loss_Alm_Tm_s = 0;      //矢压告警时间，单位 秒   A&B&C 全部矢压，即矢压。

    m_voltage_time.PowerOn_phsA_Tm_s = 0;
    m_voltage_time.PowerOn_phsB_Tm_s = 0;
    m_voltage_time.PowerOn_phsC_Tm_s = 0;
    m_voltage_time.PowerOn_Alm_Tm_s = 0;

    m_voltage_time.PTUV_Open_phsA_Tm_s = 0;
    m_voltage_time.PTUV_Open_phsB_Tm_s = 0;
    m_voltage_time.PTUV_Open_phsC_Tm_s = 0;
    m_voltage_time.PTUV_Open_Alm_Tm_s = 0;

    m_voltage_time.PhsSeqV_Alm_Tm_s = 0;

    m_voltage_time.PTUC_Op_phsA_Tm_s = 0;
    m_voltage_time.PTUC_Op_phsB_Tm_s = 0;                
    m_voltage_time.PTUC_Op_phsC_Tm_s = 0;
    m_voltage_time.PTUC_Op_Alm_Tm_s = 0;

    m_voltage_time.PTUC_Open_Op_phsA_Tm_s = 0;
    m_voltage_time.PTUC_Open_Op_phsB_Tm_s = 0;
    m_voltage_time.PTUC_Open_Op_phsC_Tm_s = 0;
    m_voltage_time.PTUC_Open_Op_Alm_Tm_s = 0;

    m_voltage_time.PTOA_Op_phsA_Tm_s = 0;
    m_voltage_time.PTOA_Op_phsB_Tm_s = 0;
    m_voltage_time.PTOA_Op_phsC_Tm_s = 0;
    m_voltage_time.PTOA_Alm_Tm_s = 0;

    m_voltage_time.SeqOC_Alm_Tm_s = 0;

    m_voltage_time.Ovld_phsA_Tm_s = 0;  
    m_voltage_time.Ovld_phsB_Tm_s = 0;
    m_voltage_time.Ovld_phsC_Tm_s = 0;
    m_voltage_time.Ovld_Alm_Tm_s = 0;

    m_voltage_time.PTOC_Hvld_phsA_Tm_s = 0;
    m_voltage_time.PTOC_Hvld_phsB_Tm_s = 0;
    m_voltage_time.PTOC_Hvld_phsC_Tm_s = 0;
    m_voltage_time.PTOC_Hvld_Alm_Tm_s = 0;

    m_voltage_time.PTOC_Ovld_phsA_Tm_s = 0;
    m_voltage_time.PTOC_Ovld_phsB_Tm_s = 0;
    m_voltage_time.PTOC_Ovld_phsC_Tm_s = 0;
    m_voltage_time.PTOC_Ovld_Alm_Tm_s = 0;

    m_voltage_time.Res_Alm_Tm_s = 0;

    m_voltage_time.PTUPF_phsA_Tm_s = 0;
    m_voltage_time.PTUPF_phsB_Tm_s = 0;
    m_voltage_time.PTUPF_phsC_Tm_s = 0;
    m_voltage_time.PTUPF_Alm_Tm_s = 0;

    m_voltage_time.PTUF_Op_phsA_Tm_s= 0.0;
    m_voltage_time.DIP_Alm_Tm_s = 0.0;
    
}

void CParamManager::initADCData()
{
    m_ADC_value["PhV_phsA"]=0.0;
    m_ADC_value["PhV_phsB"]=0.0;
    m_ADC_value["PhV_phsC"]=0.0;
    m_ADC_value["SeqV_c0"]=0.0;
    m_ADC_value["SeqV_c1"]=0.0;
     m_ADC_value["SeqV_c2"]=0.0;
    m_ADC_value["A_phsA"]=0.0;
    m_ADC_value["A_phsB"]=0.0;
    m_ADC_value["A_phsC"]=0.0;
    m_ADC_value["SeqA_c0"]=0.0;
    m_ADC_value["SeqA_c1"]=0.0;
    m_ADC_value["SeqA_c2"]=0.0;
    m_ADC_value["Hz"]=0.0;
    m_ADC_value["PhPF_phsA"]=0.0;
    m_ADC_value["PhPF_phsB"]=0.0;
    m_ADC_value["PhPF_phsC"]=0.0;
    m_ADC_value["TotPF"]=0.0;

    m_ADC_value["PhVA_phsA"]=0.0;
    m_ADC_value["PhVA_phsB"]=0.0;
    m_ADC_value["PhVA_phsC"]=0.0;
    m_ADC_value["TotVA"]=0.0;
}

// 初始化日数据
void CParamManager::initDayData()
{
    m_pdAnalyzer_value["PhVPassDay_phsA"] = 0.0;
    m_pdAnalyzer_value["PhVPassDay_phsB"] = 0.0;
    m_pdAnalyzer_value["PhVPassDay_phsC"] = 0.0;
    m_pdAnalyzer_value["PTOV_Rate_Day_phsA"] = 0.0;
    m_pdAnalyzer_value["PTOV_Rate_Day_phsB"] = 0.0;
    m_pdAnalyzer_value["PTOV_Rate_Day_phsC"] = 0.0;
    m_pdAnalyzer_value["PTUV_Rate_Day_phsA"] = 0.0;
    m_pdAnalyzer_value["PTUV_Rate_Day_phsB"] = 0.0;
    m_pdAnalyzer_value["PTUV_Rate_Day_phsC"] = 0.0;
    m_pdAnalyzer_value["PTOV_Tm_Day_phsA"] = 0.0;
    m_pdAnalyzer_value["PTOV_Tm_Day_phsB"] = 0.0;
    m_pdAnalyzer_value["PTOV_Tm_Day_phsC"] = 0.0;
    m_pdAnalyzer_value["PTUV_Tm_Day_phsA"] = 0.0;
    m_pdAnalyzer_value["PTUV_Tm_Day_phsB"] = 0.0;
    m_pdAnalyzer_value["PTUV_Tm_Day_phsC"] = 0.0;
    m_pdAnalyzer_value["PhVMaxDay_phsA"] = -9999.0;
    m_pdAnalyzer_value["PhVMaxDay_phsB"] = -9999.0;
    m_pdAnalyzer_value["PhVMaxDay_phsC"] = -9999.0;
    m_pdAnalyzer_value["PhVMinDay_phsA"] = 9999.0;
    m_pdAnalyzer_value["PhVMinDay_phsB"] = 9999.0;
    m_pdAnalyzer_value["PhVMinDay_phsC"] = 9999.0;
    m_pdAnalyzer_value["PhVAvDay_phsA"] = 0.0;
    m_pdAnalyzer_value["PhVAvDay_phsB"] = 0.0;
    m_pdAnalyzer_value["PhVAvDay_phsC"] = 0.0;

    m_pdAnalyzer_value["PhVOfsMaxDay_phsA"] = -9999.0;
    m_pdAnalyzer_value["PhVOfsMinDay_phsA"] = 9999.0;
    m_pdAnalyzer_value["PhVOfsAvDay_phsA"] = 0.0;
    m_pdAnalyzer_value["PhVOfsMaxDay_phsB"] = -9999.0;
    m_pdAnalyzer_value["PhVOfsMinDay_phsB"] = 9999.0;
    m_pdAnalyzer_value["PhVOfsAvDay_phsB"] = 0.0;
    m_pdAnalyzer_value["PhVOfsMaxDay_phsC"] = -9999.0;
    m_pdAnalyzer_value["PhVOfsMinDay_phsC"] = 9999.0;
    m_pdAnalyzer_value["PhVOfsAvDay_phsC"] = 0.0;

    m_pdAnalyzer_value["HzOfsMaxDay_phsA"] = -9999.0;
    m_pdAnalyzer_value["HzOfsMinDay_phsA"] = 9999.0;
    m_pdAnalyzer_value["HzOfsAvDay_phsA"] = 0.0;
    m_pdAnalyzer_value["HzOfsMaxDay_phsB"] = -9999.0;
    m_pdAnalyzer_value["HzOfsMinDay_phsB"] = 9999.0;
    m_pdAnalyzer_value["HzOfsAvDay_phsB"] = 0.0;
    m_pdAnalyzer_value["HzOfsMaxDay_phsC"] = -9999.0;
    m_pdAnalyzer_value["HzOfsMinDay_phsC"] = 9999.0;
    m_pdAnalyzer_value["HzOfsAvDay_phsC"] = 0.0;

    m_Tol_PhV_Day_phsA = 0.0;//计算平均电压
    m_Count_PhV_Day_phsA = 0;
    m_Tol_PhV_Day_phsB = 0.0;
    m_Count_PhV_Day_phsB = 0;
    m_Tol_PhV_Day_phsC = 0.0;
    m_Count_PhV_Day_phsC = 0;
   
    m_Tol_PhVOfs_Day_phsA = 0.0;//计算平均电压偏差
    m_Count_PhVOfs_Day_phsA = 0;
    m_Tol_PhVOfs_Day_phsB = 0.0;
    m_Count_PhVOfs_Day_phsB = 0;
    m_Tol_PhVOfs_Day_phsC = 0.0;
    m_Count_PhVOfs_Day_phsC = 0;

    m_Tol_HzOfs_Day_phsA = 0.0;//计算平均频率偏差
    m_Count_HzOfs_Day_phsA = 0;
    m_Tol_HzOfs_Day_phsB = 0.0;
    m_Count_HzOfs_Day_phsB = 0;
    m_Tol_HzOfs_Day_phsC = 0.0;
    m_Count_HzOfs_Day_phsC = 0;

    m_voltage_time.PhV_Tm_Day_phsA = 0.0; //当日A 相电压监测时间   单位分钟
    m_voltage_time.PTOV_Tm_Day_phsA = 0.0;//当日A 相电压超上限时间   单位分钟
    m_voltage_time.PTUV_Tm_Day_phsA = 0.0;//当日A 相电压超下限时间   单位分钟
    m_voltage_time.PASS_Tm_Day_phsA = 0.0;//当日A 相电压合格时间   单位分钟

    m_voltage_time.PhV_Tm_Day_phsB = 0.0; //当日B 相电压监测时间   单位分钟
    m_voltage_time.PTOV_Tm_Day_phsB = 0.0;//当日B 相电压超上限时间   单位分钟
    m_voltage_time.PTUV_Tm_Day_phsB = 0.0;//当日B 相电压超下限时间   单位分钟
    m_voltage_time.PASS_Tm_Day_phsB = 0.0;//当日B 相电压合格时间   单位分钟

    m_voltage_time.PhV_Tm_Day_phsC = 0.0; //当日C 相电压监测时间   单位分钟
    m_voltage_time.PTOV_Tm_Day_phsC = 0.0;//当日C 相电压超上限时间   单位分钟
    m_voltage_time.PTUV_Tm_Day_phsC = 0.0;//当日C 相电压超下限时间   单位分钟
    m_voltage_time.PASS_Tm_Day_phsC = 0.0;//当日C 相电压合格时间   单位分钟

    m_voltage_time.PTUC_lvld_Tm_Day = 0.0;
    m_voltage_time.PTOC_Hvld_Tm_Day = 0.0;
    m_voltage_time.Re_PTOC_Hvld_Tm_Day = 0.0;
    m_voltage_time.PTOC_mild_Ovld_Tm_Day = 0.0;
    m_voltage_time.Re_PTOC_mild_Ovld_Tm_Day = 0.0;
    m_voltage_time.PTOC_middle_Ovld_Tm_Day = 0.0;
    m_voltage_time.Re_PTOC_middle_Ovld_Tm_Day = 0.0;
    m_voltage_time.PTOC_severe_Ovld_Tm_Day = 0.0;
    m_voltage_time.Re_PTOC_severe_Ovld_Tm_Day = 0.0;
    m_voltage_time.PTOC_abnormal_Ovld_Tm_Day = 0.0;
    m_voltage_time.Re_PTOC_abnormal_Ovld_Tm_Day = 0.0;

    m_voltage_time.ImbNgA_Num_Day = 0.0;
    m_voltage_time.ImbNgA_Tm_Day = 0.0;
    m_voltage_time.ImbNgV_Num_Day = 0.0;
    m_voltage_time.ImbNgV_Tm_Day = 0.0;

    m_voltage_time.ThdA_Tm_Day = 0.0;
    m_voltage_time.ThdPhV_Tm_Day = 0.0;

    m_voltage_time.PF_Tm_Day = 0.0;

}

void CParamManager::initWeekData()
{
    m_pdAnalyzer_value["PhVOfsMaxWeek_phsA"] = -9999.0;
    m_pdAnalyzer_value["PhVOfsMinWeek_phsA"] = 9999.0;
    m_pdAnalyzer_value["PhVOfsAvWeek_phsA"] = 0.0;
    m_pdAnalyzer_value["PhVOfsMaxWeek_phsB"] = -9999.0;
    m_pdAnalyzer_value["PhVOfsMinWeek_phsB"] = 9999.0;
    m_pdAnalyzer_value["PhVOfsAvWeek_phsB"] = 0.0;
    m_pdAnalyzer_value["PhVOfsMaxWeek_phsC"] = -9999.0;
    m_pdAnalyzer_value["PhVOfsMinWeek_phsC"] = 9999.0;
    m_pdAnalyzer_value["PhVOfsAvWeek_phsC"] = 0.0;

    m_pdAnalyzer_value["HzOfsMaxWeek_phsA"] = -9999.0;
    m_pdAnalyzer_value["HzOfsMinWeek_phsA"] = 9999.0;
    m_pdAnalyzer_value["HzOfsAvWeek_phsA"] = 0.0;
    m_pdAnalyzer_value["HzOfsMaxWeek_phsB"] = -9999.0;
    m_pdAnalyzer_value["HzOfsMinWeek_phsB"] = 9999.0;
    m_pdAnalyzer_value["HzOfsAvWeek_phsB"] = 0.0;
    m_pdAnalyzer_value["HzOfsMaxWeek_phsC"] = -9999.0;
    m_pdAnalyzer_value["HzOfsMinWeek_phsC"] = 9999.0;
    m_pdAnalyzer_value["HzOfsAvWeek_phsC"] = 0.0;
   
    m_Tol_PhVOfs_Week_phsA = 0.0;//计算平均电压偏差
    m_Count_PhVOfs_Week_phsA = 0;
    m_Tol_PhVOfs_Week_phsB = 0.0;
    m_Count_PhVOfs_Week_phsB = 0;
    m_Tol_PhVOfs_Week_phsC = 0.0;
    m_Count_PhVOfs_Week_phsC = 0;

    m_Tol_HzOfs_Week_phsA = 0.0;//计算平均频率偏差
    m_Count_HzOfs_Week_phsA = 0;
    m_Tol_HzOfs_Week_phsB = 0.0;
    m_Count_HzOfs_Week_phsB = 0;
    m_Tol_HzOfs_Week_phsC = 0.0;
    m_Count_HzOfs_Week_phsC = 0;
}

//初始化月数据
void CParamManager::initMonData()
{
    m_pdAnalyzer_value["PhVPassMon_phsA"] = 0.0;
    m_pdAnalyzer_value["PhVPassMon_phsB"] = 0.0;
    m_pdAnalyzer_value["PhVPassMon_phsC"] = 0.0;
    m_pdAnalyzer_value["PTOV_Rate_Mon_phsA"] = 0.0;
    m_pdAnalyzer_value["PTOV_Rate_Mon_phsB"] = 0.0;
    m_pdAnalyzer_value["PTOV_Rate_Mon_phsC"] = 0.0;
    m_pdAnalyzer_value["PTUV_Rate_Mon_phsA"] = 0.0;
    m_pdAnalyzer_value["PTUV_Rate_Mon_phsB"] = 0.0;
    m_pdAnalyzer_value["PTUV_Rate_Mon_phsC"] = 0.0;
    m_pdAnalyzer_value["PTOV_Tm_Mon_phsA"] = 0.0;
    m_pdAnalyzer_value["PTOV_Tm_Mon_phsB"] = 0.0;
    m_pdAnalyzer_value["PTOV_Tm_Mon_phsC"] = 0.0;
    m_pdAnalyzer_value["PTUV_Tm_Mon_phsA"] = 0.0;
    m_pdAnalyzer_value["PTUV_Tm_Mon_phsB"] = 0.0;
    m_pdAnalyzer_value["PTUV_Tm_Mon_phsC"] = 0.0;
    m_pdAnalyzer_value["PhVMaxMon_phsA"] = -9999.0;
    m_pdAnalyzer_value["PhVMaxMon_phsB"] = -9999.0;
    m_pdAnalyzer_value["PhVMaxMon_phsC"] = -9999.0;
    m_pdAnalyzer_value["PhVMinMon_phsA"] = 9999.0;
    m_pdAnalyzer_value["PhVMinMon_phsB"] = 9999.0;
    m_pdAnalyzer_value["PhVMinMon_phsC"] = 9999.0;
    m_pdAnalyzer_value["PhVAvMon_phsA"] = 0.0;
    m_pdAnalyzer_value["PhVAvMon_phsB"] = 0.0;
    m_pdAnalyzer_value["PhVAvMon_phsC"] = 0.0;

    m_pdAnalyzer_value["PhVOfsMaxMon_phsA"] = -9999.0;
    m_pdAnalyzer_value["PhVOfsMinMon_phsA"] = 9999.0;
    m_pdAnalyzer_value["PhVOfsAvMon_phsA"] = 0.0;
    m_pdAnalyzer_value["PhVOfsMaxMon_phsB"] = -9999.0;
    m_pdAnalyzer_value["PhVOfsMinMon_phsB"] = 9999.0;
    m_pdAnalyzer_value["PhVOfsAvMon_phsB"] = 0.0;
    m_pdAnalyzer_value["PhVOfsMaxMon_phsC"] = -9999.0;
    m_pdAnalyzer_value["PhVOfsMinMon_phsC"] = 9999.0;
    m_pdAnalyzer_value["PhVOfsAvMon_phsC"] = 0.0;

    m_pdAnalyzer_value["HzOfsMaxMon_phsA"] = -9999.0;
    m_pdAnalyzer_value["HzOfsMinMon_phsA"] = 9999.0;
    m_pdAnalyzer_value["HzOfsAvMon_phsA"] = 0.0;
    m_pdAnalyzer_value["HzOfsMaxMon_phsB"] = -9999.0;
    m_pdAnalyzer_value["HzOfsMinMon_phsB"] = 9999.0;
    m_pdAnalyzer_value["HzOfsAvMon_phsB"] = 0.0;
    m_pdAnalyzer_value["HzOfsMaxMon_phsC"] = -9999.0;
    m_pdAnalyzer_value["HzOfsMinMon_phsC"] = 9999.0;
    m_pdAnalyzer_value["HzOfsAvMon_phsC"] = 0.0;

    m_Tol_PhV_Mon_phsA = 0.0;//计算平均电压
    m_Count_PhV_Mon_phsA = 0;
    m_Tol_PhV_Mon_phsB = 0.0;
    m_Count_PhV_Mon_phsB = 0;
    m_Tol_PhV_Mon_phsC = 0.0;
    m_Count_PhV_Mon_phsC = 0;
   
    m_Tol_PhVOfs_Mon_phsA = 0.0;//计算平均电压偏差
    m_Count_PhVOfs_Mon_phsA = 0;
    m_Tol_PhVOfs_Mon_phsB = 0.0;
    m_Count_PhVOfs_Mon_phsB = 0;
    m_Tol_PhVOfs_Mon_phsC = 0.0;
    m_Count_PhVOfs_Mon_phsC = 0;

    m_Tol_HzOfs_Mon_phsA = 0.0;//计算平均频率偏差
    m_Count_HzOfs_Mon_phsA = 0;
    m_Tol_HzOfs_Mon_phsB = 0.0;
    m_Count_HzOfs_Mon_phsB = 0;
    m_Tol_HzOfs_Mon_phsC = 0.0;
    m_Count_HzOfs_Mon_phsC = 0;

    m_voltage_time.PhV_Tm_Mon_phsA = 0.0; //当月A 相电压监测时间   单位分钟
    m_voltage_time.PTOV_Tm_Mon_phsA = 0.0;//当月A 相电压超上限时间   单位分钟
    m_voltage_time.PTUV_Tm_Mon_phsA = 0.0;//当月A 相电压超下限时间   单位分钟
    m_voltage_time.PASS_Tm_Mon_phsA = 0.0;//当月A 相电压合格时间   单位分钟

    m_voltage_time.PhV_Tm_Mon_phsB = 0.0; //当月B 相电压监测时间   单位分钟
    m_voltage_time.PTOV_Tm_Mon_phsB = 0.0;//当月B 相电压超上限时间   单位分钟
    m_voltage_time.PTUV_Tm_Mon_phsB = 0.0;//当月B 相电压超下限时间   单位分钟
    m_voltage_time.PASS_Tm_Mon_phsB = 0.0;//当月B 相电压合格时间   单位分钟

    m_voltage_time.PhV_Tm_Mon_phsC = 0.0; //当月C 相电压监测时间   单位分钟
    m_voltage_time.PTOV_Tm_Mon_phsC = 0.0;//当月C 相电压超上限时间   单位分钟
    m_voltage_time.PTUV_Tm_Mon_phsC = 0.0;//当月C 相电压超下限时间   单位分钟
    m_voltage_time.PASS_Tm_Mon_phsC = 0.0;//当月C 相电压合格时间   单位分钟


    m_voltage_time.ImbNgA_Num_Mon = 0.0;
    m_voltage_time.ImbNgA_Tm_Mon = 0.0;
    m_voltage_time.ImbNgV_Num_Mon = 0.0;
    m_voltage_time.ImbNgV_Tm_Mon = 0.0;

    m_voltage_time.ThdA_Tm_Mon = 0.0;
    m_voltage_time.ThdPhV_Tm_Mon = 0.0;

    m_voltage_time.PF_Tm_Mon = 0.0;

}

// 初始化可开放日数据
void CParamManager::initocData()
{
    m_LoadRate_sum = m_pre_LoadRate;
    m_LoadRate_phsA_sum = m_pre_LoadRate_phsA;
    m_LoadRate_phsB_sum = m_pre_LoadRate_phsB;
    m_LoadRate_phsC_sum = m_pre_LoadRate_phsC;

    m_LoadRate_num = 1;
    m_LoadRate_phsA_num = 1;
    m_LoadRate_phsB_num = 1;
    m_LoadRate_phsC_num = 1;

    m_LoadRate = 0.0;
    m_LoadRate_phsA = 0.0;
    m_LoadRate_phsB = 0.0;
    m_LoadRate_phsC = 0.0;

    m_MaxLoadRate = 0.0;
    m_MaxLoadRate_phsA = 0.0;
    m_MaxLoadRate_phsB = 0.0;
    m_MaxLoadRate_phsC = 0.0;
}

void CParamManager::initoc_Load_Data()
{
    m_MaxLoad = 0.0;         //周期内最大负荷
    m_MaxLoad_phsA = 0.0;    //周期内最大负荷a
    m_MaxLoad_phsB = 0.0;    //周期内最大负荷b
    m_MaxLoad_phsC = 0.0;    //周期内最大负荷c

    m_TotResLoad = 0.0;     //可开放容量
    m_ResLoad_phsA = 0.0; //可开放容量A
    m_ResLoad_phsB = 0.0; //可开放容量B
    m_ResLoad_phsC = 0.0; //可开放容量C
}

int CParamManager::exists(const char *path) 
{
    return access(path, F_OK) == 0;
}
// 轮转日志文件（核心逻辑）
void CParamManager::rotate_logs() 
{
    // 1. 删除最旧文件 (序号MAX_FILES)
    char old_file[256];
    snprintf(old_file, sizeof(old_file), "%s/%s.%02d.gz", 
             DEST_DIR, BASE_NAME, MAX_FILES);
    unlink(old_file);

    // 2. 从高序号向低序号重命名 (9→10, 8→9, ..., 1→2)
    for (int i = MAX_FILES - 1; i >= 1; i--) {
        char src[256], dest[256];
        snprintf(src, sizeof(src), "%s/%s.%02d.gz", DEST_DIR, BASE_NAME, i);
        snprintf(dest, sizeof(dest), "%s/%s.%02d.gz", DEST_DIR, BASE_NAME, i + 1);
        
        if (exists(src)) rename(src, dest);
    }
}
int CParamManager::compress_log()
{
    char dest[256];
    snprintf(dest, sizeof(dest), "%s/%s.01.gz", DEST_DIR, BASE_NAME);
    
    // 直接使用gzip命令压缩
    char cmd[512];
    snprintf(cmd, sizeof(cmd), "gzip -c '%s' > '%s'", SOURCE_FILE, dest);
    return system(cmd) == 0;
}

CParamManager::CParamManager()
{
    m_Thread.SetPackageAcquireManager(this);

    m_T.SetParam(1000*m_Param_value["ImbA_Dly"]); // 三相负荷不平衡告警时间
	m_T_Imb_YX.SetParam(1000*m_Param_value["ImbA_Dly"]); // 三相负荷不平衡告警时间 
    //m_T.StartCount();
    m_sendNum = 20;
 
    initYXData();
    initADCData();
    initDayData();
    initWeekData();
    initMonData();

    m_appName = CFG_APP_NAME;
    m_appVersion = 1001;
    m_appRealseDate = "20230722";
    mskprintf(":::1, m_Param_value[ImbA_Dly] %f\n",m_Param_value["ImbA_Dly"]);
}

CParamManager::~CParamManager()
{
}
