#include "transient.h"
#include "mqtt_pub_inter.h"
#include "param_json.h"
#include <iostream>
#include <cstring>
#include <chrono>
#include <cmath>
#include <algorithm>
#include <ctime>
#include <fstream>
#include <sstream>
#include <iomanip>

using namespace std;



const char* TransientConfig::CHANNEL_NAMES[CHANNEL_COUNT] = {
    "UA", "UB", "UC",           // 三相电压
    "IA", "IB", "IC"            // 三相电流
};

//=============================================================================
// CircularBuffer 类实现
//=============================================================================

CircularBuffer::CircularBuffer(size_t capacity)
    : m_capacity(capacity)
    , m_head(0)
    , m_tail(0)
    , m_size(0)
{
    if (capacity == 0) {
        throw std::invalid_argument("CircularBuffer capacity cannot be zero");
    }
    m_buffer.resize(capacity);
    IEC_LOG_RECORD(eRunType, "CircularBuffer created with capacity: %zu", capacity);
}

CircularBuffer::~CircularBuffer()
{
    Clear();
    IEC_LOG_RECORD(eRunType, "CircularBuffer destroyed");
}

void CircularBuffer::Push(const WaveformSample& sample)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    m_buffer[m_head] = sample;
    m_head = (m_head + 1) % m_capacity;

    if (m_size < m_capacity) {
        ++m_size;
    } else {
        // 缓冲区已满，移动尾指针
        m_tail = (m_tail + 1) % m_capacity;
    }
}

bool CircularBuffer::GetSamples(std::vector<WaveformSample>& samples, size_t count)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (count > m_size) {
        return false;
    }

    samples.clear();
    samples.reserve(count);

    size_t index = (m_tail + m_size - count) % m_capacity;
    for (size_t i = 0; i < count; ++i) {
        samples.push_back(m_buffer[index]);
        index = (index + 1) % m_capacity;
    }

    return true;
}

bool CircularBuffer::GetAllSamples(std::vector<WaveformSample>& samples)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    samples.clear();
    samples.reserve(m_size);

    size_t index = m_tail;
    for (size_t i = 0; i < m_size; ++i) {
        samples.push_back(m_buffer[index]);
        index = (index + 1) % m_capacity;
    }

    return true;
}

void CircularBuffer::Clear()
{
    std::lock_guard<std::mutex> lock(m_mutex);
    m_head = 0;
    m_tail = 0;
    m_size = 0;
}

size_t CircularBuffer::Size() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_size;
}

bool CircularBuffer::IsFull() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_size == m_capacity;
}

//=============================================================================
// CTransientThread 类实现
//=============================================================================

CTransientThread::CTransientThread()
    : m_pMng(nullptr)
{
}

CTransientThread::~CTransientThread()
{
}

void CTransientThread::SetTransientManager(CTransientManager* p)
{
    m_pMng = p;
}

void CTransientThread::run(void)
{
    if (m_pMng) {
        m_pMng->thread_prev();
        while (CSL_thread::IsEnableRun()) {
            m_pMng->thread_func();
        }
        m_pMng->thread_exit();
    }
}

std::string CTransientThread::ThreadName(void) const
{
    return std::string("暂态数据分析线程");
}

void CTransientThread::RecordLog(const std::string& sMsg)
{
    IEC_LOG_RECORD(eRunType, "%s", sMsg.c_str());
}

//=============================================================================
// CTransientManager 类实现
//=============================================================================

CTransientManager::CTransientManager()
    : m_pThread(nullptr)
    , m_pBuffer(nullptr)
    , m_bRunning(false)
    , m_bTriggerActive(false)
    , m_triggerTime(0)
    , m_postTriggerCount(0)
    , m_EventStartTime(0)
    , m_EventEndTime(0)
    , m_EventSampleCount(0)
{
    // 初始化录波数据缓冲区
    m_waveformBuffer.reserve(WAVEFORM_BUFFER_SIZE);

    // 初始化原始数据缓冲区
    m_rawVA_values.reserve(RAW_DATA_BUFFER_SIZE);
    m_rawVB_values.reserve(RAW_DATA_BUFFER_SIZE);
    m_rawVC_values.reserve(RAW_DATA_BUFFER_SIZE);
    m_rawIA_values.reserve(RAW_DATA_BUFFER_SIZE);
    m_rawIB_values.reserve(RAW_DATA_BUFFER_SIZE);
    m_rawIC_values.reserve(RAW_DATA_BUFFER_SIZE);
}

CTransientManager::~CTransientManager()
{
    Exit();
}

CTransientManager& CTransientManager::CreateInstance()
{
    static CTransientManager instance;
    return instance;
}

bool CTransientManager::Init(void)
{
    try {
        // 初始化环形缓冲区
        m_pBuffer = std::make_unique<CircularBuffer>(m_config.buffer_capacity);

        // 初始化线程
        m_pThread = std::make_unique<CTransientThread>();
        m_pThread->SetTransientManager(this);

        IEC_LOG_RECORD(eRunType, "Transient manager initialized successfully");
        return true;
    }
    catch (const std::exception& e) {
        IEC_LOG_RECORD(eErrType, "Transient manager init failed: %s", e.what());
        return false;
    }
}

void CTransientManager::Exit(void)
{
    Stop();

    if (m_pThread) {
        m_pThread.reset();
    }

    if (m_pBuffer) {
        m_pBuffer.reset();
    }
}

void CTransientManager::Start(void)
{
    if (!m_bRunning && m_pThread) {
        m_bRunning = true;
        m_pThread->start();
    }
}

void CTransientManager::Stop(void)
{
    if (m_bRunning && m_pThread) {
        m_bRunning = false;
        m_pThread->close();
    }
}

void CTransientManager::thread_prev(void)
{
    m_bTriggerActive = false;
    m_triggerTime = 0;
    m_postTriggerCount = 0;
}

void CTransientManager::thread_func(void)
{
    try {
        // 处理采样数据
        //ProcessTransientData();
        JudgeTansientEvent();
    }
    catch (const std::exception& e) {
        ii_sleep(100);  // 出错时休眠更长时间
    }
}

void CTransientManager::thread_exit(void)
{
    IEC_LOG_RECORD(eRunType, "Transient thread exiting...");
}

void CTransientManager::OnRun(void)
{
    while (m_bRunning) {
        thread_func();
    }
}

bool CTransientManager::ReceiveWaveformPacket(const uint8_t* packet_data, size_t packet_length)
{
    if(!packet_data || packet_length < 15) return false;
    if(packet_data[0] != 0x68 || packet_data[1] != 0x36) return false;
    if(packet_data[packet_length - 1] != 0x16) return false;

    // 解析长度域
    uint32_t data_length = packet_data[2] | (packet_data[3] << 8) | 
                          (packet_data[4] << 16) | (packet_data[5] << 24);
    
    if(packet_length != data_length + 6) return false;

    // 校验和检查
    // size_t checksum_length = packet_length - 5;
    // uint32_t calculated_checksum = CalculateChecksum(packet_data + 1, checksum_length);
    // uint32_t received_checksum = packet_data[packet_length - 5] | 
    //                             (packet_data[packet_length - 4] << 8) |
    //                             (packet_data[packet_length - 3] << 16) |
    //                             (packet_data[packet_length - 2] << 24);
    
    // if(calculated_checksum != received_checksum) return false;

    // 帧序号
    uint8_t frame_seq = packet_data[6];

    // 解析周波数据
    size_t data_start = 7;
    
    // 周波序号
    uint32_t cycle_num = packet_data[data_start] | 
                        (packet_data[data_start + 1] << 8) |
                        (packet_data[data_start + 2] << 16) |
                        (packet_data[data_start + 3] << 24);

    // 时间数据
    const uint8_t* time_data = packet_data + data_start + 4;
    std::string current_time = GetTimeamp(time_data);

    // 检查时间重复
    if(m_lastTimestamp == current_time) return false;
    m_lastTimestamp = current_time;

    printf("帧序号: %u, 周波序号: %u, 时间: %s\n", frame_seq, cycle_num, current_time.c_str());

    // 解析采样值（7个float）
    size_t samples_start = data_start + 14;
    const char* signal_names[] = {"A相电压", "B相电压", "C相电压", "A相电流", "B相电流", "C相电流", "零线电流"};

    // 创建波形采样数据结构
    WaveformSample sample;
    sample.timestamp = GetTimestampFromTimeString(current_time);  // 将时间字符串转换为微秒时间戳

    // 初始化所有通道数据为0
    memset(sample.channels, 0, sizeof(sample.channels));

    for(int i = 0; i < 7; i++) {
        size_t offset = samples_start + i * 4;
        if(offset + 4 > packet_length - 5) break;

        float raw_sample = ConvertToFloat(packet_data + offset);
        float value = raw_sample / 1000.0f;

        switch(i) {
            case 0:
                m_VA_value.push_back(value);  // A相电压（用于计算）
                // 存储原始数据到录波缓冲区
                m_rawVA_values.push_back(value);
                if (m_rawVA_values.size() > RAW_DATA_BUFFER_SIZE) {
                    m_rawVA_values.erase(m_rawVA_values.begin());
                }
                sample.channels[0] = value;
                break;
            case 1:
                m_VB_value.push_back(value);  // B相电压（用于计算）
                // 存储原始数据到录波缓冲区
                m_rawVB_values.push_back(value);
                if (m_rawVB_values.size() > RAW_DATA_BUFFER_SIZE) {
                    m_rawVB_values.erase(m_rawVB_values.begin());
                }
                sample.channels[1] = value;
                break;
            case 2:
                m_VC_value.push_back(value);  // C相电压（用于计算）
                // 存储原始数据到录波缓冲区
                m_rawVC_values.push_back(value);
                if (m_rawVC_values.size() > RAW_DATA_BUFFER_SIZE) {
                    m_rawVC_values.erase(m_rawVC_values.begin());
                }
                sample.channels[2] = value;
                break;
            case 3:
                // 存储原始A相电流数据到录波缓冲区
                m_rawIA_values.push_back(value);
                if (m_rawIA_values.size() > RAW_DATA_BUFFER_SIZE) {
                    m_rawIA_values.erase(m_rawIA_values.begin());
                }
                sample.channels[3] = value;  // A相电流
                break;
            case 4:
                // 存储原始B相电流数据到录波缓冲区
                m_rawIB_values.push_back(value);
                if (m_rawIB_values.size() > RAW_DATA_BUFFER_SIZE) {
                    m_rawIB_values.erase(m_rawIB_values.begin());
                }
                sample.channels[4] = value;  // B相电流
                break;
            case 5:
                // 存储原始C相电流数据到录波缓冲区
                m_rawIC_values.push_back(value);
                if (m_rawIC_values.size() > RAW_DATA_BUFFER_SIZE) {
                    m_rawIC_values.erase(m_rawIC_values.begin());
                }
                sample.channels[5] = value;  // C相电流
                break;
            default:
                break;  // 零线电流暂不存储到环形缓冲区
        }

        printf("%s: %.3f %s\n", signal_names[i], value, (i < 3) ? "V" : "A");
    }

    // 将完整的波形数据存入环形存储区
    if (m_pBuffer) {
        m_pBuffer->Push(sample);
    }

    return true;
}


std::string CTransientManager::GetTimeamp(const uint8_t *data)
{
    int year = 2000 + data[0];
    int month = data[1];
    int day = data[2];
    int hour = data[3];
    int minute = data[4];
    int second = data[5];
    uint32_t microsecond = data[6] | (data[7] << 8) | (data[8] << 16) | (data[9] << 24);

    char buffer[64];
    snprintf(buffer, sizeof(buffer), "%04d/%02d/%02d, %02d:%02d:%02d.%06u",
             year, month, day, hour, minute, second, microsecond);

    return std::string(buffer);
}

uint64_t CTransientManager::GetTimestampFromTimeString(const std::string& time_str)
{
    // 解析时间字符串格式: "2024/01/01, 12:30:45.123456"
    int year, month, day, hour, minute, second;
    uint32_t microsecond;

    int parsed = sscanf(time_str.c_str(), "%d/%d/%d, %d:%d:%d.%u",
                       &year, &month, &day, &hour, &minute, &second, &microsecond);

    if (parsed != 7) {
        // 解析失败，返回当前时间的微秒时间戳
        auto now = std::chrono::system_clock::now();
        auto duration = now.time_since_epoch();
        return std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    }

    // 转换为time_t
    struct tm tm_time = {};
    tm_time.tm_year = year - 1900;  // tm_year是从1900年开始的年数
    tm_time.tm_mon = month - 1;     // tm_mon是0-11
    tm_time.tm_mday = day;
    tm_time.tm_hour = hour;
    tm_time.tm_min = minute;
    tm_time.tm_sec = second;
    tm_time.tm_isdst = -1;          // 让系统决定是否为夏令时

    time_t timestamp = mktime(&tm_time);
    if (timestamp == -1) {
        // mktime失败，返回当前时间
        auto now = std::chrono::system_clock::now();
        auto duration = now.time_since_epoch();
        return std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    }

    // 转换为微秒时间戳
    uint64_t microsecond_timestamp = static_cast<uint64_t>(timestamp) * 1000000ULL + microsecond;

    return microsecond_timestamp;
}

float CTransientManager::ConvertToFloat(const uint8_t* data)
{
    // 将4个字节的数据转换为float（小端序）
    float value;
    std::memcpy(&value, data, 4);
    return value;
}

// 周波数据计算
float CTransientManager::CalculateUrms(const std::vector<float>& u, int k)
{
    int N = u.size();
    
    // 确定求和范围
    int start_index = (k - 1) * N / 2;
    int end_index = (k + 1) * N / 2;
    
    // 确保索引在有效范围内
    start_index = std::max(0, start_index);
    end_index = std::min(N - 1, end_index);
    
    // 计算平方和
    double sum_of_squares = 0.0;
    int count = 0;
    for (int i = start_index; i <= end_index; ++i) {
        sum_of_squares += u[i] * u[i];
        count++;
    }
    
    // 计算均方根
    if (count > 0) {
        return std::sqrt(sum_of_squares / count);
    } else {
        return 0.0; 
    }
}


//判断发生事件
bool CTransientManager::JudgeTansientEvent()
{
    // 计算各相电压的均方根值
    float va_urms = CalculateUrms(m_VA_value, 1);
    float vb_urms = CalculateUrms(m_VB_value, 1);
    float vc_urms = CalculateUrms(m_VC_value, 1);

    // 获取参数值
    float SwlStrVal_Lim = CParamManager::CreateInstance().m_Param_value["SWL_strVal_Lim"];    // 暂升定值
    float DipStrVal_Lim = CParamManager::CreateInstance().m_Param_value["DIP_strVal_Lim"];    // 暂降定值
    float IntrStrVal_Lim = CParamManager::CreateInstance().m_Param_value["INTR_strVal_Lim"];  // 短时中断定值
    float voltage = CParamManager::CreateInstance().m_Param_value["std_voltage"];
    float DalayV = CParamManager::CreateInstance().m_Param_value["DalayVoltage"];

    // 获取当前时间戳函数
    auto getCurrentTimeMillis = []() {
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()
        ).count();
    };

    在解析

    if (m_CurrentEvent.empty()) // 没有正在进行的事件
    {
        if (va_urms < IntrStrVal_Lim || vb_urms < IntrStrVal_Lim || vc_urms < IntrStrVal_Lim)  //满足告警定值
        {
            // 记录事件开始时间戳
            m_EventStartTime = getCurrentTimeMillis();
            m_CurrentEvent = "IntrStr_Alm";
            m_EventSampleCount = 5;
            m_EventSampleCount++;

            // 添加录波数据到缓冲区
            std::string current_timestamp = std::to_string(m_EventStartTime);
            AddWaveformToBuffer(current_timestamp, "IntrStr_Alm");
        }
        else if ((va_urms >= SwlStrVal_Lim && va_urms < 1.1f * voltage) || (vb_urms >= SwlStrVal_Lim && vb_urms < 1.1f * voltage) || (vc_urms >= SwlStrVal_Lim && vc_urms < 1.1f * voltage))
        {
            // 记录事件开始时间戳
            m_EventStartTime = getCurrentTimeMillis();
            m_CurrentEvent = "SwlStr_Alm";
            m_EventSampleCount = 5;
            m_EventSampleCount++;

            // 添加录波数据到缓冲区
            std::string current_timestamp = std::to_string(m_EventStartTime);
            AddWaveformToBuffer(current_timestamp, "SwlStr_Alm");
        }
        else if ((va_urms <= DipStrVal_Lim && va_urms > IntrStrVal_Lim) || (vb_urms <= DipStrVal_Lim && vb_urms > IntrStrVal_Lim) || (vc_urms <= DipStrVal_Lim && vc_urms > IntrStrVal_Lim))
        {
            // 记录事件开始时间
            m_EventStartTime = getCurrentTimeMillis();
            m_CurrentEvent = "DipStr_Alm";
            m_EventSampleCount = 5;
            m_EventSampleCount++;

            // 添加录波数据到缓冲区
            std::string current_timestamp = std::to_string(m_EventStartTime);
            AddWaveformToBuffer(current_timestamp, "DipStr_Alm");
        }
    }
    else // 有正在进行的事件
    {
        if (m_CurrentEvent == "IntrStr_Alm") // 短时中断事件处理
        {
            if (va_urms >= IntrStrVal_Lim + DalayV && vb_urms >= IntrStrVal_Lim + DalayV && vc_urms >= IntrStrVal_Lim + DalayV)  //满足复归条件
            {
                // 记录事件结束时间
                m_EventEndTime = getCurrentTimeMillis();
            }
        }
        else if (m_CurrentEvent == "SwlStr_Alm") // 暂升事件处理
        {
            if (va_urms < SwlStrVal_Lim && vb_urms < SwlStrVal_Lim && vc_urms < SwlStrVal_Lim)  //满足复归条件
            {
                // 记录事件结束时间
                m_EventEndTime = getCurrentTimeMillis();
            }
        }
        else if (m_CurrentEvent == "DipStr_Alm") // 暂降事件处理
        {
            if (va_urms > DipStrVal_Lim && vb_urms > DipStrVal_Lim && vc_urms > DipStrVal_Lim)  //满足复归条件
            {
                // 记录事件结束时间
                m_EventEndTime = getCurrentTimeMillis();
            }
        }
        // 继续记录波形数据
        if (m_EventSampleCount < 25) {
            // 每次都添加当前波形数据到缓冲区
            std::string current_timestamp = std::to_string(getCurrentTimeMillis());
            AddWaveformToBuffer(current_timestamp, m_CurrentEvent);
            m_EventSampleCount++;
        }
        else if (m_EventSampleCount == 25)
        {
            // 生成录波文件
            GenerateWaveformRecord();

            // 重置事件状态
            m_CurrentEvent.clear();
            m_EventStartTime = 0;
            m_EventEndTime = 0;
            m_EventSampleCount = 0;

            IEC_LOG_RECORD(eRunType, "Event completed and waveform record generated");
        }
    }

    return false;
}

// 添加波形数据到缓冲区
void CTransientManager::AddWaveformToBuffer(const std::string& timestamp, const std::string& event_type)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    WaveformRecord record;
    record.timestamp = timestamp;
    record.event_type = event_type;



    // 使用原始报文数据（包含多个三相电压和电流值）
    size_t min_data_count = std::min({
        m_rawVA_values.size(), m_rawVB_values.size(), m_rawVC_values.size(),
        m_rawIA_values.size(), m_rawIB_values.size(), m_rawIC_values.size()
    });

    // 确定要取的数据点数量（最多20个）
    size_t data_points = std::min(static_cast<size_t>(WAVEFORM_DATA_COUNT), min_data_count);
    size_t start_index = min_data_count >= data_points ? min_data_count - data_points : 0;

    record.waveform_data.clear();
    record.waveform_data.reserve(data_points * 6); // 三相电压 + 三相电流

    // 按顺序添加最新的数据点：VA, VB, VC, IA, IB, IC
    for (size_t i = start_index; i < start_index + data_points; ++i) {
        if (i < m_rawVA_values.size()) record.waveform_data.push_back(m_rawVA_values[i]); // A相电压
        if (i < m_rawVB_values.size()) record.waveform_data.push_back(m_rawVB_values[i]); // B相电压
        if (i < m_rawVC_values.size()) record.waveform_data.push_back(m_rawVC_values[i]); // C相电压
        if (i < m_rawIA_values.size()) record.waveform_data.push_back(m_rawIA_values[i]); // A相电流
        if (i < m_rawIB_values.size()) record.waveform_data.push_back(m_rawIB_values[i]); // B相电流
        if (i < m_rawIC_values.size()) record.waveform_data.push_back(m_rawIC_values[i]); // C相电流
    }

    // 如果缓冲区已满，移除最旧的记录
    if (m_waveformBuffer.size() >= WAVEFORM_BUFFER_SIZE) {
        m_waveformBuffer.erase(m_waveformBuffer.begin());
    }

    // 添加新记录
    m_waveformBuffer.push_back(record);

    IEC_LOG_RECORD(eRunType, "Added waveform record to buffer: timestamp=%s, event_type=%s, data_points=%zu, total_data=%zu, buffer_size=%zu",
                   timestamp.c_str(), event_type.c_str(), data_points, record.waveform_data.size(), m_waveformBuffer.size());
}

// 生成录波文件
void CTransientManager::GenerateWaveformRecord()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_waveformBuffer.empty()) {
        IEC_LOG_RECORD(eErrType, "No waveform records in buffer to generate file");
        return;
    }

    // 生成文件名（基于当前时间）
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;

    std::stringstream filename;
    filename << "waveform_" << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S")
             << "_" << std::setfill('0') << std::setw(3) << ms.count() << ".csv";

    std::string filepath = "/tmp/" + filename.str(); // 可以根据需要修改路径

    std::ofstream file(filepath);
    if (!file.is_open()) {
        IEC_LOG_RECORD(eErrType, "Failed to create waveform record file: %s", filepath.c_str());
        return;
    }

    // 写入CSV头部
    file << "Timestamp,Event_Type,Sample_Index,VA_Value,VB_Value,VC_Value,IA_Value,IB_Value,IC_Value\n";

    // 写入所有缓冲区中的数据
    for (const auto& record : m_waveformBuffer) {
        // 每个数据点包含6个通道数据（VA, VB, VC, IA, IB, IC）
        size_t data_points = record.waveform_data.size() / 6;
        for (size_t i = 0; i < data_points; ++i) {
            size_t base_index = i * 6;
            file << record.timestamp << ","
                 << record.event_type << ","
                 << i << ","
                 << record.waveform_data[base_index] << ","     // VA
                 << record.waveform_data[base_index + 1] << "," // VB
                 << record.waveform_data[base_index + 2] << "," // VC
                 << record.waveform_data[base_index + 3] << "," // IA
                 << record.waveform_data[base_index + 4] << "," // IB
                 << record.waveform_data[base_index + 5] << "\n"; // IC
        }
    }

    file.close();

    IEC_LOG_RECORD(eRunType, "Generated waveform record file: %s with %zu records",
                   filepath.c_str(), m_waveformBuffer.size());

    // 清空缓冲区
    m_waveformBuffer.clear();
}

// 清空计算用的临时数据
void CTransientManager::ClearCalculationData()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    // 清空计算用的临时数据
    m_VA_value.clear();
    m_VB_value.clear();
    m_VC_value.clear();

    IEC_LOG_RECORD(eRunType, "Cleared calculation data buffers");
}

bool CTransientManager::JudgeTansientEvent_YX()
{
    // 计算事件持续时间
    float event_duration = (m_EventEndTime - m_EventStartTime) / 1000.0f;  
    if(CParamManager::CreateInstance().m_Param_value["IntrStrVal_Enable"] > 0.5)
    {  
        if (event_duration >= 0.01f && event_duration <= 60.0f && CParamManager::CreateInstance().m_pdAnalyzer_value_YX["IntrStrVal"] == 0)
        {
            // 事件持续时间有效，发送遥信
            CParamManager::CreateInstance().m_pdAnalyzer_value_YX["IntrStrVal"] = 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("IntrStrVal");
            IEC_LOG_RECORD(eRunType,"短时中断 IntrStrVal :%d", CParamManager::CreateInstance().m_pdAnalyzer_value_YX["IntrStrVal"]);
            m_EventEndTime = 0;
            m_EventStartTime = 0;
        }
    }
    if(CParamManager::CreateInstance().m_Param_value["SwlStrVal_Enable"] > 0.5)
    {  
        if (event_duration >= 0.01f && event_duration <= 60.0f && CParamManager::CreateInstance().m_pdAnalyzer_value_YX["SwlStrVal"] == 0)
        {
            // 事件持续时间有效，发送遥信
            CParamManager::CreateInstance().m_pdAnalyzer_value_YX["SwlStrVal"] = 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("SwlStrVal");
            m_EventEndTime = 0;
            m_EventStartTime = 0;
        }
    }
    if(CParamManager::CreateInstance().m_Param_value["DipStrVal_Enable"] > 0.5)
    {  
        if (event_duration >= 0.01f && event_duration <= 60.0f && CParamManager::CreateInstance().m_pdAnalyzer_value_YX["DipStrVal"] == 0)
        {
            // 事件持续时间有效，发送遥信
            CParamManager::CreateInstance().m_pdAnalyzer_value_YX["DipStrVal"] = 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("DipStrVal");
            m_EventEndTime = 0;
            m_EventStartTime = 0;
        }
    }
    return false;
}


