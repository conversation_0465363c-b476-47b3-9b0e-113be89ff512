#include "transient.h"
#include "mqtt_pub_inter.h"
#include "param_json.h"
#include <iostream>
#include <cstring>
#include <chrono>
#include <cmath>
#include <algorithm>

using namespace std;

// 通道名称定义
const char* TransientConfig::CHANNEL_NAMES[CHANNEL_COUNT] = {
    "UA", "UB", "UC",           // 三相电压
    "IA", "IB", "IC"            // 三相电流
};




//=============================================================================
// CircularBuffer 类实现
//=============================================================================

CircularBuffer::CircularBuffer(size_t capacity)
    : m_capacity(capacity)
    , m_head(0)
    , m_tail(0)
    , m_size(0)
{
    if (capacity == 0) {
        throw std::invalid_argument("CircularBuffer capacity cannot be zero");
    }
    m_buffer.resize(capacity);
    IEC_LOG_RECORD(eRunType, "CircularBuffer created with capacity: %zu", capacity);
}

CircularBuffer::~CircularBuffer()
{
    Clear();
    IEC_LOG_RECORD(eRunType, "CircularBuffer destroyed");
}

void CircularBuffer::Push(const WaveformSample& sample)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    m_buffer[m_head] = sample;
    m_head = (m_head + 1) % m_capacity;

    if (m_size < m_capacity) {
        ++m_size;
    } else {
        // 缓冲区已满，移动尾指针
        m_tail = (m_tail + 1) % m_capacity;
    }
}

bool CircularBuffer::GetSamples(std::vector<WaveformSample>& samples, size_t count)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (count > m_size) {
        return false;
    }

    samples.clear();
    samples.reserve(count);

    size_t index = (m_tail + m_size - count) % m_capacity;
    for (size_t i = 0; i < count; ++i) {
        samples.push_back(m_buffer[index]);
        index = (index + 1) % m_capacity;
    }

    return true;
}

bool CircularBuffer::GetAllSamples(std::vector<WaveformSample>& samples)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    samples.clear();
    samples.reserve(m_size);

    size_t index = m_tail;
    for (size_t i = 0; i < m_size; ++i) {
        samples.push_back(m_buffer[index]);
        index = (index + 1) % m_capacity;
    }

    return true;
}

void CircularBuffer::Clear()
{
    std::lock_guard<std::mutex> lock(m_mutex);
    m_head = 0;
    m_tail = 0;
    m_size = 0;
}

size_t CircularBuffer::Size() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_size;
}

bool CircularBuffer::IsFull() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_size == m_capacity;
}

//=============================================================================
// CTransientThread 类实现
//=============================================================================

CTransientThread::CTransientThread()
    : m_pMng(nullptr)
{
}

CTransientThread::~CTransientThread()
{
}

void CTransientThread::SetTransientManager(CTransientManager* p)
{
    m_pMng = p;
}

void CTransientThread::run(void)
{
    if (m_pMng) {
        m_pMng->thread_prev();
        while (CSL_thread::IsEnableRun()) {
            m_pMng->thread_func();
        }
        m_pMng->thread_exit();
    }
}

std::string CTransientThread::ThreadName(void) const
{
    return std::string("暂态数据分析线程");
}

void CTransientThread::RecordLog(const std::string& sMsg)
{
    IEC_LOG_RECORD(eRunType, "%s", sMsg.c_str());
}

//=============================================================================
// CTransientManager 类实现
//=============================================================================

CTransientManager::CTransientManager()
    : m_pThread(nullptr)
    , m_pBuffer(nullptr)
    , m_bRunning(false)
    , m_bTriggerActive(false)
    , m_triggerTime(0)
    , m_postTriggerCount(0)
{
}

CTransientManager::~CTransientManager()
{
    Exit();
}

CTransientManager& CTransientManager::CreateInstance()
{
    static CTransientManager instance;
    return instance;
}

bool CTransientManager::Init(void)
{
    try {
        // 初始化环形缓冲区
        m_pBuffer = std::make_unique<CircularBuffer>(m_config.buffer_capacity);

        // 初始化线程
        m_pThread = std::make_unique<CTransientThread>();
        m_pThread->SetTransientManager(this);

        IEC_LOG_RECORD(eRunType, "Transient manager initialized successfully");
        return true;
    }
    catch (const std::exception& e) {
        IEC_LOG_RECORD(eErrType, "Transient manager init failed: %s", e.what());
        return false;
    }
}

void CTransientManager::Exit(void)
{
    Stop();

    if (m_pThread) {
        m_pThread.reset();
    }

    if (m_pBuffer) {
        m_pBuffer.reset();
    }
}

void CTransientManager::Start(void)
{
    if (!m_bRunning && m_pThread) {
        m_bRunning = true;
        m_pThread->start();
    }
}

void CTransientManager::Stop(void)
{
    if (m_bRunning && m_pThread) {
        m_bRunning = false;
        m_pThread->close();
    }
}

void CTransientManager::thread_prev(void)
{
    m_bTriggerActive = false;
    m_triggerTime = 0;
    m_postTriggerCount = 0;
}

void CTransientManager::thread_func(void)
{
    try {
        // 处理采样数据
        ProcessTransientData();

        // 处理事件队列
        TransientEvent event;
        while (m_eventQueue.GetHead(event)) {
            OnTransientEvent(event);
        }

        // 短暂休眠，避免CPU占用过高
        ii_sleep(1);  // 1ms
    }
    catch (const std::exception& e) {
        ii_sleep(100);  // 出错时休眠更长时间
    }
}

void CTransientManager::thread_exit(void)
{
    IEC_LOG_RECORD(eRunType, "Transient thread exiting...");
}

void CTransientManager::OnRun(void)
{
    // 主循环处理逻辑
    while (m_bRunning) {
        thread_func();
    }
}

void CTransientManager::PushSampleData(const WaveformSample& sample)
{
    if (m_pBuffer) {
        m_pBuffer->Push(sample);
    }

    // 也可以通过队列传递数据
    m_sampleQueue.AddTail(sample);
}

bool CTransientManager::ProcessTransientData(void)
{
    WaveformSample sample;
    bool hasData = false;

    // 处理队列中的采样数据
    while (m_sampleQueue.GetHead(sample)) {
        hasData = true;

    //     // 检测触发条件
    //     if (!m_bTriggerActive && DetectTrigger(sample)) {
    //         m_bTriggerActive = true;
    //         m_triggerTime = sample.timestamp;
    //         m_postTriggerCount = 0;
    //         IEC_LOG_RECORD(eRunType, "Transient trigger detected at timestamp: %llu", m_triggerTime);
    //     }

    //     // 如果触发激活，计数后触发采样点
    //     if (m_bTriggerActive) {
    //         m_postTriggerCount++;

    //         // 达到后触发采样点数，生成事件
    //         if (m_postTriggerCount >= m_config.post_trigger_samples) {
    //             TransientEvent event;
    //             event.trigger_time = m_triggerTime;

    //             // 获取触发前后的波形数据
    //             size_t totalSamples = m_config.pre_trigger_samples + m_config.post_trigger_samples;
    //             if (m_pBuffer->GetSamples(event.samples, totalSamples)) {
    //                 AnalyzeWaveform(event.samples, event);
    //                 m_eventQueue.AddTail(event);
    //             }

    //             m_bTriggerActive = false;
    //             m_postTriggerCount = 0;
    //         }
    //     }
    }

    return hasData;
}


void CTransientManager::OnTransientEvent(const TransientEvent& event)
{
    IEC_LOG_RECORD(eRunType, "Processing transient event: type=%s, trigger_time=%llu",
                   event.event_type.c_str(), event.trigger_time);

    // 发布事件数据
    PublishEventData(event);

    // 可以在这里添加其他事件处理逻辑
    // 例如：保存到数据库、发送告警等
}



bool CTransientManager::PushTimestampWaveformData(const TimestampWaveformData& waveform_data)
{
    if (waveform_data.ua_values.empty() || waveform_data.ub_values.empty() ||
        waveform_data.uc_values.empty() || waveform_data.ia_values.empty() ||
        waveform_data.ib_values.empty() || waveform_data.ic_values.empty()) {
        IEC_LOG_RECORD(eErrType, "Empty waveform data queues");
        return false;
    }

    // 检查所有队列长度是否一致
    size_t ua_size = waveform_data.ua_values.size();
    if (waveform_data.ub_values.size() != ua_size || waveform_data.uc_values.size() != ua_size ||
        waveform_data.ia_values.size() != ua_size || waveform_data.ib_values.size() != ua_size ||
        waveform_data.ic_values.size() != ua_size) {
        IEC_LOG_RECORD(eErrType, "Inconsistent queue sizes: UA=%zu, UB=%zu, UC=%zu, IA=%zu, IB=%zu, IC=%zu",
                       waveform_data.ua_values.size(), waveform_data.ub_values.size(), waveform_data.uc_values.size(),
                       waveform_data.ia_values.size(), waveform_data.ib_values.size(), waveform_data.ic_values.size());
        return false;
    }

    IEC_LOG_RECORD(eRunType, "Pushing timestamp waveform data: timestamp=%llu, data_count=%zu",
                   waveform_data.timestamp, ua_size);

    // 创建队列的副本用于处理（因为queue没有const迭代器）
    std::queue<double> ua_copy = waveform_data.ua_values;
    std::queue<double> ub_copy = waveform_data.ub_values;
    std::queue<double> uc_copy = waveform_data.uc_values;
    std::queue<double> ia_copy = waveform_data.ia_values;
    std::queue<double> ib_copy = waveform_data.ib_values;
    std::queue<double> ic_copy = waveform_data.ic_values;

    size_t processed_count = 0;

    // 处理队列中的所有数据
    while (!ua_copy.empty()) {
        WaveformSample sample;
        sample.timestamp = waveform_data.timestamp;
        sample.channels[0] = ua_copy.front(); ua_copy.pop();  // A相电压
        sample.channels[1] = ub_copy.front(); ub_copy.pop();  // B相电压
        sample.channels[2] = uc_copy.front(); uc_copy.pop();  // C相电压
        sample.channels[3] = ia_copy.front(); ia_copy.pop();  // A相电流
        sample.channels[4] = ib_copy.front(); ib_copy.pop();  // B相电流
        sample.channels[5] = ic_copy.front(); ic_copy.pop();  // C相电流

        // 放入环形缓存区
        if (m_pBuffer) {
            m_pBuffer->Push(sample);
        }

        // 同时放入采样队列用于暂态分析
        m_sampleQueue.AddTail(sample);

        processed_count++;
    }

    IEC_LOG_RECORD(eRunType, "Successfully pushed %zu waveform samples to circular buffer at timestamp %llu",
                   processed_count, waveform_data.timestamp);

    return true;
}

bool CTransientManager::ReceiveWaveformPacket(const uint8_t* packet_data, size_t packet_length)
{
    if(!packet_data || packet_length < 15) return false;
    if(packet_data[0] != 0x68 || packet_data[1] != 0x36) return false;
    if(packet_data[packet_length - 1] != 0x16) return false;

    // 解析长度域
    uint32_t data_length = packet_data[2] | (packet_data[3] << 8) | 
                          (packet_data[4] << 16) | (packet_data[5] << 24);
    
    if(packet_length != data_length + 6) return false;

    // 校验和检查
    // size_t checksum_length = packet_length - 5;
    // uint32_t calculated_checksum = CalculateChecksum(packet_data + 1, checksum_length);
    // uint32_t received_checksum = packet_data[packet_length - 5] | 
    //                             (packet_data[packet_length - 4] << 8) |
    //                             (packet_data[packet_length - 3] << 16) |
    //                             (packet_data[packet_length - 2] << 24);
    
    // if(calculated_checksum != received_checksum) return false;

    // 帧序号
    uint8_t frame_seq = packet_data[6];

    // 解析周波数据
    size_t data_start = 7;
    
    // 周波序号
    uint32_t cycle_num = packet_data[data_start] | 
                        (packet_data[data_start + 1] << 8) |
                        (packet_data[data_start + 2] << 16) |
                        (packet_data[data_start + 3] << 24);

    // 时间数据
    const uint8_t* time_data = packet_data + data_start + 4;
    std::string current_time = GetTimeamp(time_data);

    // 检查时间重复
    if(m_lastTimestamp == current_time) return false;
    m_lastTimestamp = current_time;

    printf("帧序号: %u, 周波序号: %u, 时间: %s\n", frame_seq, cycle_num, current_time.c_str());

    // 解析采样值（7个float）
    size_t samples_start = data_start + 14;
    const char* signal_names[] = {"A相电压", "B相电压", "C相电压", "A相电流", "B相电流", "C相电流", "零线电流"};
    
    for(int i = 0; i < 7; i++) {
        size_t offset = samples_start + i * 4;
        if(offset + 4 > packet_length - 5) break;
        
        float sample = ConvertToFloat(packet_data + offset);
        float value = sample / 1000.0f;
        switch(i) {
            case 0: m_VA_value.push(value); break;  // A相电压
            case 1: m_VB_value.push(value); break;  // B相电压
            case 2: m_VC_value.push(value); break;  // C相电压
            default: break;
        }

        printf("%s: %.3f %s\n", signal_names[i], value, (i < 3) ? "V" : "A");
    }

    return true;
}


std::string CTransientManager::GetTimeamp(uint8_t *data)
{
    int year = 2000 + data[0];
    int month = data[1];
    int day = data[2];
    int hour = data[3];
    int minute = data[4];
    int second = data[5];
    uint32_t microsecond = data[6] | (data[7] << 8) | (data[8] << 16) | (data[9] << 24);
    
    char buffer[64];
    snprintf(buffer, sizeof(buffer), "%04d/%02d/%02d, %02d:%02d:%02d.%06lu",
             year, month, day, hour, minute, second, microsecond);
    
    return std::string(buffer);
}

int32_t CTransientManager::littleEndianBytesToInt32(uint8_t bytes[4]) 
{
    int32_t value;
    std::memcpy(&value, bytes, 4);
    return value;
}

bool CTransientManager::PushWaveformData(uint64_t timestamp, const std::vector<double>& ua_values,
                                        const std::vector<double>& ub_values, const std::vector<double>& uc_values,
                                        const std::vector<double>& ia_values, const std::vector<double>& ib_values,
                                        const std::vector<double>& ic_values)
{
    // 验证输入数据
    if (ua_values.empty() || ub_values.empty() || uc_values.empty() ||
        ia_values.empty() || ib_values.empty() || ic_values.empty()) {
        IEC_LOG_RECORD(eErrType, "Empty voltage or current data vectors");
        return false;
    }

    // 检查所有向量长度是否一致
    size_t data_count = ua_values.size();
    if (ub_values.size() != data_count || uc_values.size() != data_count ||
        ia_values.size() != data_count || ib_values.size() != data_count ||
        ic_values.size() != data_count) {
        IEC_LOG_RECORD(eErrType, "Inconsistent data vector sizes: UA=%zu, UB=%zu, UC=%zu, IA=%zu, IB=%zu, IC=%zu",
                       ua_values.size(), ub_values.size(), uc_values.size(),
                       ia_values.size(), ib_values.size(), ic_values.size());
        return false;
    }

    IEC_LOG_RECORD(eRunType, "Pushing %zu waveform data points to buffer at timestamp %llu",
                   data_count, timestamp);

    // 将每组数据转换为WaveformSample并放入环形缓存区
    for (size_t i = 0; i < data_count; ++i) {
        WaveformSample sample;
        sample.timestamp = timestamp;
        sample.channels[0] = ua_values[i];  // A相电压
        sample.channels[1] = ub_values[i];  // B相电压
        sample.channels[2] = uc_values[i];  // C相电压
        sample.channels[3] = ia_values[i];  // A相电流
        sample.channels[4] = ib_values[i];  // B相电流
        sample.channels[5] = ic_values[i];  // C相电流

        // 放入环形缓存区
        if (m_pBuffer) {
            m_pBuffer->Push(sample);
        }

        // 同时放入采样队列用于暂态分析
        m_sampleQueue.AddTail(sample);
    }

    return true;
}

bool CTransientManager::PushWaveformDataPoints(const std::vector<WaveformDataPoint>& data_points)
{
    if (data_points.empty()) {
        return false;
    }


    // 将每个数据点转换为WaveformSample并放入环形缓存区
    for (const auto& point : data_points) {
        WaveformSample sample;
        sample.timestamp = point.timestamp;
        sample.channels[0] = point.ua;  // A相电压
        sample.channels[1] = point.ub;  // B相电压
        sample.channels[2] = point.uc;  // C相电压
        sample.channels[3] = point.ia;  // A相电流
        sample.channels[4] = point.ib;  // B相电流
        sample.channels[5] = point.ic;  // C相电流

        // 放入环形缓存区
        if (m_pBuffer) {
            m_pBuffer->Push(sample);
        }

        // 同时放入采样队列用于暂态分析
        m_sampleQueue.AddTail(sample);
    }

    return true;
}

//=============================================================================
// 一个周波数据计算函数（仅声明，空实现）
//=============================================================================

void CTransientManager::CalculateOneCycleRMS(const std::vector<WaveformSample>& cycle_data)
{
    // 空实现 - 计算一个周波的有效值
    // 实际实现时需要：
    // 1. 对每个通道计算RMS值
    // 2. 存储计算结果
}

void CTransientManager::CalculateOneCyclePeak(const std::vector<WaveformSample>& cycle_data)
{
    // 空实现 - 计算一个周波的峰值
    // 实际实现时需要：
    // 1. 对每个通道计算正峰值和负峰值
    // 2. 存储计算结果
}

void CTransientManager::CalculateOneCycleFrequency(const std::vector<WaveformSample>& cycle_data)
{
    // 空实现 - 计算一个周波的频率
    // 实际实现时需要：
    // 1. 通过过零点检测计算频率
    // 2. 存储计算结果
}

void CTransientManager::CalculateOneCyclePhase(const std::vector<WaveformSample>& cycle_data)
{
    // 空实现 - 计算一个周波的相位
    // 实际实现时需要：
    // 1. 计算各相之间的相位差
    // 2. 存储计算结果
}

void CTransientManager::CalculateOneCycleTHD(const std::vector<WaveformSample>& cycle_data)
{
    // 空实现 - 计算一个周波的总谐波失真
    // 实际实现时需要：
    // 1. 进行FFT分析
    // 2. 计算THD值
    // 3. 存储计算结果
}

void CTransientManager::CalculateOneCyclePower(const std::vector<WaveformSample>& cycle_data)
{
    // 空实现 - 计算一个周波的功率
    // 实际实现时需要：
    // 1. 计算有功功率、无功功率、视在功率
    // 2. 计算功率因数
    // 3. 存储计算结果
}

