#include "transient.h"
#include "mqtt_pub_inter.h"
#include "param_json.h"
#include <iostream>
#include <cstring>
#include <chrono>
#include <cmath>
#include <algorithm>
#include <ctime>

using namespace std;



const char* TransientConfig::CHANNEL_NAMES[CHANNEL_COUNT] = {
    "UA", "UB", "UC",           // 三相电压
    "IA", "IB", "IC"            // 三相电流
};

//=============================================================================
// CircularBuffer 类实现
//=============================================================================

CircularBuffer::CircularBuffer(size_t capacity)
    : m_capacity(capacity)
    , m_head(0)
    , m_tail(0)
    , m_size(0)
{
    if (capacity == 0) {
        throw std::invalid_argument("CircularBuffer capacity cannot be zero");
    }
    m_buffer.resize(capacity);
    IEC_LOG_RECORD(eRunType, "CircularBuffer created with capacity: %zu", capacity);
}

CircularBuffer::~CircularBuffer()
{
    Clear();
    IEC_LOG_RECORD(eRunType, "CircularBuffer destroyed");
}

void CircularBuffer::Push(const WaveformSample& sample)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    m_buffer[m_head] = sample;
    m_head = (m_head + 1) % m_capacity;

    if (m_size < m_capacity) {
        ++m_size;
    } else {
        // 缓冲区已满，移动尾指针
        m_tail = (m_tail + 1) % m_capacity;
    }
}

bool CircularBuffer::GetSamples(std::vector<WaveformSample>& samples, size_t count)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (count > m_size) {
        return false;
    }

    samples.clear();
    samples.reserve(count);

    size_t index = (m_tail + m_size - count) % m_capacity;
    for (size_t i = 0; i < count; ++i) {
        samples.push_back(m_buffer[index]);
        index = (index + 1) % m_capacity;
    }

    return true;
}

bool CircularBuffer::GetAllSamples(std::vector<WaveformSample>& samples)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    samples.clear();
    samples.reserve(m_size);

    size_t index = m_tail;
    for (size_t i = 0; i < m_size; ++i) {
        samples.push_back(m_buffer[index]);
        index = (index + 1) % m_capacity;
    }

    return true;
}

void CircularBuffer::Clear()
{
    std::lock_guard<std::mutex> lock(m_mutex);
    m_head = 0;
    m_tail = 0;
    m_size = 0;
}

size_t CircularBuffer::Size() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_size;
}

bool CircularBuffer::IsFull() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_size == m_capacity;
}

void CircularBuffer::SetCapacity(size_t new_capacity)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (new_capacity == m_capacity) {
        return; // 容量没有变化
    }

    // 创建新的缓冲区
    std::vector<WaveformSample> new_buffer(new_capacity);

    // 复制现有数据到新缓冲区
    size_t copy_size = std::min(m_size, new_capacity);
    size_t new_head = 0;

    if (copy_size > 0) {
        // 从尾部开始复制最新的数据
        size_t start_index = (m_tail + m_size - copy_size) % m_capacity;
        for (size_t i = 0; i < copy_size; ++i) {
            new_buffer[new_head] = m_buffer[(start_index + i) % m_capacity];
            new_head = (new_head + 1) % new_capacity;
        }
    }

    // 更新缓冲区
    m_buffer = std::move(new_buffer);
    m_capacity = new_capacity;
    m_head = new_head;
    m_tail = 0;
    m_size = copy_size;

    IEC_LOG_RECORD(eRunType, "CircularBuffer capacity changed to %zu, current size: %zu",
                   new_capacity, m_size);
}

//=============================================================================
// CTransientThread 类实现
//=============================================================================

CTransientThread::CTransientThread()
    : m_pMng(nullptr)
{
}

CTransientThread::~CTransientThread()
{
}

void CTransientThread::SetTransientManager(CTransientManager* p)
{
    m_pMng = p;
}

void CTransientThread::run(void)
{
    if (m_pMng) {
        m_pMng->thread_prev();
        while (CSL_thread::IsEnableRun()) {
            m_pMng->thread_func();
        }
        m_pMng->thread_exit();
    }
}

std::string CTransientThread::ThreadName(void) const
{
    return std::string("暂态数据分析线程");
}

void CTransientThread::RecordLog(const std::string& sMsg)
{
    IEC_LOG_RECORD(eRunType, "%s", sMsg.c_str());
}

//=============================================================================
// CTransientManager 类实现
//=============================================================================

CTransientManager::CTransientManager()
    : m_pThread(nullptr)
    , m_pBuffer(nullptr)
    , m_bRunning(false)
    , m_bTriggerActive(false)
    , m_triggerTime(0)
    , m_postTriggerCount(0)
{
}

CTransientManager::~CTransientManager()
{
    Exit();
}

CTransientManager& CTransientManager::CreateInstance()
{
    static CTransientManager instance;
    return instance;
}

bool CTransientManager::Init(void)
{
    try {
        // 初始化环形缓冲区
        m_pBuffer = std::make_unique<CircularBuffer>(m_config.buffer_capacity);

        // 初始化线程
        m_pThread = std::make_unique<CTransientThread>();
        m_pThread->SetTransientManager(this);

        IEC_LOG_RECORD(eRunType, "Transient manager initialized successfully");
        return true;
    }
    catch (const std::exception& e) {
        IEC_LOG_RECORD(eErrType, "Transient manager init failed: %s", e.what());
        return false;
    }
}

void CTransientManager::Exit(void)
{
    Stop();

    if (m_pThread) {
        m_pThread.reset();
    }

    if (m_pBuffer) {
        m_pBuffer.reset();
    }
}

void CTransientManager::Start(void)
{
    if (!m_bRunning && m_pThread) {
        m_bRunning = true;
        m_pThread->start();
    }
}

void CTransientManager::Stop(void)
{
    if (m_bRunning && m_pThread) {
        m_bRunning = false;
        m_pThread->close();
    }
}

void CTransientManager::thread_prev(void)
{
    m_bTriggerActive = false;
    m_triggerTime = 0;
    m_postTriggerCount = 0;
}

void CTransientManager::thread_func(void)
{
    try {
        // 处理采样数据
        //ProcessTransientData();
        JudgeTansientEvent();
    }
    catch (const std::exception& e) {
        ii_sleep(100);  // 出错时休眠更长时间
    }
}

void CTransientManager::thread_exit(void)
{
    IEC_LOG_RECORD(eRunType, "Transient thread exiting...");
}

void CTransientManager::OnRun(void)
{
    while (m_bRunning) {
        thread_func();
    }
}

bool CTransientManager::ReceiveWaveformPacket(const uint8_t* packet_data, size_t packet_length)
{
    cout<<"收到数据报文"<<endl;
    if(!packet_data || packet_length < 15) return false;
    cout<<"1"<<endl;
    if(packet_data[0] != 0x68 || packet_data[1] != 0x36) return false;
    cout<<"2"<<endl;
    if(packet_data[packet_length - 1] != 0x16) return false;
    cout<<"开始解析"<<endl;

    // 解析长度域
    // uint32_t data_length = packet_data[2] | (packet_data[3] << 8) | 
    //                       (packet_data[4] << 16) | (packet_data[5] << 24);
    
    // if(packet_length != data_length + 6) return false;
    cout<<"3"<<endl;

    // 校验和检查
    // size_t checksum_length = packet_length - 5;
    // uint32_t calculated_checksum = CalculateChecksum(packet_data + 1, checksum_length);
    // uint32_t received_checksum = packet_data[packet_length - 5] | 
    //                             (packet_data[packet_length - 4] << 8) |
    //                             (packet_data[packet_length - 3] << 16) |
    //                             (packet_data[packet_length - 2] << 24);
    
    // if(calculated_checksum != received_checksum) return false;

    // 帧序号
    uint8_t frame_seq = packet_data[6];

    // 解析周波数据
    size_t data_start = 7;
    
    // 周波序号
    uint32_t cycle_num = packet_data[data_start] | 
                        (packet_data[data_start + 1] << 8) |
                        (packet_data[data_start + 2] << 16) |
                        (packet_data[data_start + 3] << 24);

    // 时间数据
    const uint8_t* time_data = packet_data + data_start + 4;
    std::string current_time = GetTimeamp(time_data);

    // 检查时间重复
    if(m_lastTimestamp == current_time) return false;
    m_lastTimestamp = current_time;

    printf("帧序号: %u, 周波序号: %u, 时间: %s\n", frame_seq, cycle_num, current_time.c_str());

    // 解析采样值（7个float）
    // 数据偏移：帧头(2) + 长度(4) + 帧序号(1) + 周波序号(4) + 时间(6) + 微秒(4) = 21
    size_t samples_start = 21;
    const char* signal_names[] = {"A相电压", "B相电压", "C相电压", "A相电流", "B相电流", "C相电流", "零线电流"};

    // 创建波形采样数据结构
    WaveformSample sample;
    sample.timestamp = GetTimestampFromTimeString(current_time);  // 将时间字符串转换为微秒时间戳

    // 计算可以处理的数据组数
    size_t available_data = packet_length - samples_start - 5; // 减去校验和+帧尾
    size_t num_samples = available_data / 28; // 每组28字节（7个int32）

    printf("可处理数据组数: %zu\n", num_samples);

    // 处理每组数据
    for(size_t sample_idx = 0; sample_idx < num_samples; sample_idx++) {
        printf("\n=== 第%zu组采样数据 ===\n", sample_idx + 1);

        // 创建当前组的波形采样数据结构
        WaveformSample current_sample;
        current_sample.timestamp = GetTimestampFromTimeString(current_time);
        memset(current_sample.channels, 0, sizeof(current_sample.channels));

        size_t group_offset = samples_start + sample_idx * 28;

        for(int i = 0; i < 7; i++) {
            size_t offset = group_offset + i * 4;
            if(offset + 4 > packet_length - 5) break;

            // 调试信息：打印原始字节数据
            printf("Channel %d offset=%zu: 0x%02x 0x%02x 0x%02x 0x%02x\n",
                   i, offset, packet_data[offset], packet_data[offset+1],
                   packet_data[offset+2], packet_data[offset+3]);

            int32_t raw_sample = ConvertToInt32(packet_data + offset);
            float value = raw_sample / 1000.0f;

            // 调试信息：打印转换结果
            printf("Channel %d: raw_sample=%d, value=%.3f\n", i, raw_sample, value);

            switch(i) {
                case 0:
                    m_VA_value.push_back(value);  // A相电压
                    current_sample.channels[0] = value;
                    break;
                case 1:
                    m_VB_value.push_back(value);  // B相电压
                    current_sample.channels[1] = value;
                    break;
                case 2:
                    m_VC_value.push_back(value);  // C相电压
                    current_sample.channels[2] = value;
                    break;
                case 3:
                    current_sample.channels[3] = value;  // A相电流
                    break;
                case 4:
                    current_sample.channels[4] = value;  // B相电流
                    break;
                case 5:
                    current_sample.channels[5] = value;  // C相电流
                    break;
                default:
                    break;  // 零线电流暂不存储到环形缓冲区
            }

            printf("%s: %.3f %s\n", signal_names[i], value, (i < 3) ? "V" : "A");
        }

        // 将当前组数据存入环形存储区
        if (m_pBuffer) {
            m_pBuffer->Push(current_sample);
        }
    }

    // 管理环形存储区数据大小
    // 判断当前是否有事件发生
    bool event_active = !m_CurrentEvent.empty();

    // 正常情况5个，事件发生时25个
    size_t max_capacity = event_active ? 25 : 5;

    // 动态调整CircularBuffer容量
    if (m_pBuffer) {
        m_pBuffer->SetCapacity(max_capacity);
        printf("Buffer capacity set to: %zu, current size: %zu, Event: %s\n",
               max_capacity, m_pBuffer->Size(),
               event_active ? m_CurrentEvent.c_str() : "None");
    }
    JudgeTansientEvent();
    return true;

}


std::string CTransientManager::GetTimeamp(const uint8_t *data)
{
    int year = 2000 + data[0];
    int month = data[1];
    int day = data[2];
    int hour = data[3];
    int minute = data[4];
    int second = data[5];
    uint32_t microsecond = data[6] | (data[7] << 8) | (data[8] << 16) | (data[9] << 24);

    char buffer[64];
    snprintf(buffer, sizeof(buffer), "%04d/%02d/%02d, %02d:%02d:%02d.%06u",
             year, month, day, hour, minute, second, microsecond);

    return std::string(buffer);
}

uint64_t CTransientManager::GetTimestampFromTimeString(const std::string& time_str)
{
    // 解析时间字符串格式: "2024/01/01, 12:30:45.123456"
    int year, month, day, hour, minute, second;
    uint32_t microsecond;

    int parsed = sscanf(time_str.c_str(), "%d/%d/%d, %d:%d:%d.%u",
                       &year, &month, &day, &hour, &minute, &second, &microsecond);

    if (parsed != 7) {
        // 解析失败，返回当前时间的微秒时间戳
        auto now = std::chrono::system_clock::now();
        auto duration = now.time_since_epoch();
        return std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    }

    // 转换为time_t
    struct tm tm_time = {};
    tm_time.tm_year = year - 1900;  // tm_year是从1900年开始的年数
    tm_time.tm_mon = month - 1;     // tm_mon是0-11
    tm_time.tm_mday = day;
    tm_time.tm_hour = hour;
    tm_time.tm_min = minute;
    tm_time.tm_sec = second;
    tm_time.tm_isdst = -1;          // 让系统决定是否为夏令时

    time_t timestamp = mktime(&tm_time);
    if (timestamp == -1) {
        // mktime失败，返回当前时间
        auto now = std::chrono::system_clock::now();
        auto duration = now.time_since_epoch();
        return std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    }

    // 转换为微秒时间戳
    uint64_t microsecond_timestamp = static_cast<uint64_t>(timestamp) * 1000000ULL + microsecond;

    return microsecond_timestamp;
}

int32_t CTransientManager::ConvertToInt32(const uint8_t* data)
{
    // 将4个字节的数据转换为32位有符号整数（小端序）
    int32_t value = data[0] | (data[1] << 8) | (data[2] << 16) | (data[3] << 24);
    return value;
}

float CTransientManager::ConvertToFloat(const uint8_t* data)
{
    // 将4个字节的数据转换为float（小端序）
    float value;
    std::memcpy(&value, data, 4);

    // 检查是否为有效的浮点数
    if (std::isnan(value) || std::isinf(value)) {
        printf("Warning: Invalid float value detected, raw bytes: 0x%02x%02x%02x%02x\n",
               data[0], data[1], data[2], data[3]);
        return 0.0f;
    }

    return value;
}

// 周波数据计算
float CTransientManager::CalculateUrms(const std::vector<float>& u, int k)
{
    int N = u.size();
    
    // 确定求和范围
    int start_index = (k - 1) * N / 2;
    int end_index = (k + 1) * N / 2;
    
    // 确保索引在有效范围内
    start_index = std::max(0, start_index);
    end_index = std::min(N - 1, end_index);
    
    // 计算平方和
    double sum_of_squares = 0.0;
    int count = 0;
    for (int i = start_index; i <= end_index; ++i) {
        sum_of_squares += u[i] * u[i];
        count++;
    }
    
    // 计算均方根
    if (count > 0) {
        return std::sqrt(sum_of_squares / count);
    } else {
        return 0.0; 
    }
}


//判断发生事件
bool CTransientManager::JudgeTansientEvent()
{
    // 计算各相电压的均方根值
    float va_urms = CalculateUrms(m_VA_value, 1);
    float vb_urms = CalculateUrms(m_VB_value, 1);
    float vc_urms = CalculateUrms(m_VC_value, 1);

    cout<<"A相方均根值"<<va_urms<<endl;
    cout<<"B相方均根值"<<vb_urms<<endl;
    cout<<"C相方均根值"<<vc_urms<<endl;

    // 获取参数值
    float SwlStrVal_Lim = CParamManager::CreateInstance().m_Param_value["SwlStrVal_Lim"];    // 暂升定值
    float DipStrVal_Lim = CParamManager::CreateInstance().m_Param_value["DipStrVal_Lim"];    // 暂降定值
    float IntrStrVal_Lim = CParamManager::CreateInstance().m_Param_value["IntrStrVal_Lim"];  // 短时中断定值
    float voltage = CParamManager::CreateInstance().m_Param_value["std_voltage"];
    //float DalayV = CParamManager::CreateInstance().m_Param_value["DalayVoltage"];
    float DalayV = 0.0f;
    cout<<"短时中断定值"<<IntrStrVal_Lim<<endl;
    cout<<"暂升定值"<<SwlStrVal_Lim<<endl;
    cout<<"暂降定值"<<DipStrVal_Lim<<endl;
    cout<<"标准电压"<<voltage<<endl;
    cout<<"电压越限值"<<DalayV<<endl;

    // 获取当前时间戳函数
    auto getCurrentTimeMillis = []() {
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()
        ).count();
    };

    if (m_CurrentEvent.empty()) // 没有正在进行的事件
    {
        if (va_urms < IntrStrVal_Lim || vb_urms < IntrStrVal_Lim || vc_urms < IntrStrVal_Lim)  //满足告警定值
        {
            // 记录事件开始时间戳
            m_EventStartTime = getCurrentTimeMillis();
            m_CurrentEvent = "IntrStr_Alm";
            m_EventSampleCount = 5;
            m_EventSampleCount++;
            cout<<"短时中断事件"<<endl;
            cout<<m_EventSampleCount<<endl;
        }
        else if ((va_urms >= SwlStrVal_Lim && va_urms < 1.1f * voltage) || (vb_urms >= SwlStrVal_Lim && vb_urms < 1.1f * voltage) || (vc_urms >= SwlStrVal_Lim && vc_urms < 1.1f * voltage))
        {
            // 记录事件开始时间戳 
            m_EventStartTime = getCurrentTimeMillis();
            m_CurrentEvent = "SwlStr_Alm";
            m_EventSampleCount = 5;
            m_EventSampleCount++;
            cout<<"暂升事件"<<endl;
            cout<<m_EventSampleCount<<endl;
        }
        else if ((va_urms <= DipStrVal_Lim && va_urms > IntrStrVal_Lim) || (vb_urms <= DipStrVal_Lim && vb_urms > IntrStrVal_Lim) || (vc_urms <= DipStrVal_Lim && vc_urms > IntrStrVal_Lim))
        {
            // 记录事件开始时间
            m_EventStartTime = getCurrentTimeMillis();
            m_CurrentEvent = "DipStr_Alm";
            m_EventSampleCount = 5; 
            m_EventSampleCount++;
            cout<<"暂降事件"<<endl;
            cout<<m_EventSampleCount<<endl;
        }
    }
    else // 有正在进行的事件
    {
        cout<<"事件结束"<<endl;
        m_EventEndTime = getCurrentTimeMillis();
        cout<<m_EventEndTime<<endl;
        // if (m_CurrentEvent == "IntrStr_Alm") // 短时中断事件处理
        // {
        //     if (va_urms >= IntrStrVal_Lim + DalayV && vb_urms >= IntrStrVal_Lim + DalayV && vc_urms >= IntrStrVal_Lim + DalayV)  //满足复归条件
        //     {
        //         // 记录事件结束时间
        //         m_EventEndTime = getCurrentTimeMillis();
        //     }
        // }
        // else if (m_CurrentEvent == "SwlStr_Alm") // 暂升事件处理
        // {
        //     if (va_urms < SwlStrVal_Lim && vb_urms < SwlStrVal_Lim && vc_urms < SwlStrVal_Lim)  //满足复归条件
        //     {
        //         // 记录事件结束时间
        //         m_EventEndTime = getCurrentTimeMillis();
        //     }
        // }
        // else if (m_CurrentEvent == "DipStr_Alm") // 暂降事件处理
        // {
        //     if (va_urms > DipStrVal_Lim && vb_urms > DipStrVal_Lim && vc_urms > DipStrVal_Lim)  //满足复归条件
        //     {
        //         // 记录事件结束时间
        //         m_EventEndTime = getCurrentTimeMillis();
        //     }
        // }
    }
    JudgeTansientEvent_YX();
    return true;
}

bool CTransientManager::JudgeTansientEvent_YX()
{
    // 计算事件持续时间
    float event_duration = (m_EventEndTime - m_EventStartTime) / 1000.0f +;  
    if(CParamManager::CreateInstance().m_Param_value["IntrStrVal_Enable"] > 0.5)
    {  
        if (event_duration >= 0.01f && event_duration <= 60.0f && CParamManager::CreateInstance().m_pdAnalyzer_value_YX["IntrStrVal"] == 0)
        {
            // 事件持续时间有效，发送遥信
            CParamManager::CreateInstance().m_pdAnalyzer_value_YX["IntrStrVal"] = 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("IntrStrVal");
            IEC_LOG_RECORD(eRunType,"短时中断 IntrStrVal :%d", CParamManager::CreateInstance().m_pdAnalyzer_value_YX["IntrStrVal"]);
            m_EventEndTime = 0;
            m_EventStartTime = 0;
        }
    }
    if(CParamManager::CreateInstance().m_Param_value["SwlStrVal_Enable"] > 0.5)
    {  
        if (event_duration >= 0.01f && event_duration <= 60.0f && CParamManager::CreateInstance().m_pdAnalyzer_value_YX["SwlStrVal"] == 0)
        {
            // 事件持续时间有效，发送遥信
            CParamManager::CreateInstance().m_pdAnalyzer_value_YX["SwlStrVal"] = 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("SwlStrVal");
            m_EventEndTime = 0;
            m_EventStartTime = 0;
        }
    }
    if(CParamManager::CreateInstance().m_Param_value["DipStrVal_Enable"] > 0.5)
    {  
        if (event_duration >= 0.01f && event_duration <= 60.0f && CParamManager::CreateInstance().m_pdAnalyzer_value_YX["DipStrVal"] == 0)
        {
            // 事件持续时间有效，发送遥信
            CParamManager::CreateInstance().m_pdAnalyzer_value_YX["DipStrVal"] = 1;
            CTaskManager::CreateInstance().MqttPackNotificationData_2("DipStrVal");
            m_EventEndTime = 0;
            m_EventStartTime = 0;
        }
    }
    return true;
}


