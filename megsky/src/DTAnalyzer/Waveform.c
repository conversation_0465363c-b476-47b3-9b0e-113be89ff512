// #include <stdio.h>
// #include <stdlib.h>
// #include <unistd.h>
// #include <stdint.h>
// #include <stdbool.h>
// #include <pthread.h>	
// //#include "hal.h"
// #include "halWaveform.h"

// static WAVEFORM_SAMPLER_DEVICE_T *wavedev;
// static volatile bool running = true;

// static void key_exit(int signum)
// {
//     printf("ctrl+c!\n");
//     running = false;

//     return;
// }


// int main(int argc, char *argv[])
// {
//     int ret = 0;


//     signal(SIGINT, key_exit);

//     ret = hal_init();
//     if (ret) {
//         printf("hal init failed, ret = %d\n", ret);
//         return ret;
//     }

//     wavedev = (WAVEFORM_SAMPLER_DEVICE_T *)hal_device_get(tag_WAVEFORM_SAMPLER_DEVICE);
//     if (!wavedev) {
//         printf("cannot find %s\n", tag_WAVEFORM_SAMPLER_DEVICE);
//     }

//     while (running) {
//         ret = wavedev->hal_device_get(wavedev,255,1);
//         ret = keydev->key_value_get(keydev, key_value, 2);
//         if (ret == -1) {	//没有按键按下
//             usleep(200000);
//             continue;
//         }

//         for (int i = 0; i < 1; i++) {
//             printf("key_code:%d-key_state:%d\n",key_value[i].key, key_value[i].state);
//         }

//     }


//     return ret;
// }

