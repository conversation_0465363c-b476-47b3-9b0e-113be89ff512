
#ifndef TRANSIENT_H
#define TRANSIENT_H

#include "public_struct.h"
#include "pub_string.h"
#include "pub_thread.h"
#include "pub_logfile.h"
#include "queue_model.h"
#include "CJsonObject.h"

#include <vector>
#include <memory>
#include <mutex>
#include <chrono>
#include <cmath>
#include <queue>

using namespace std;

// 暂态数据配置参数
struct TransientConfig
{
    static const int CHANNEL_COUNT = 6;                    // 通道数量（三相电压+三相电流）
    static const char* CHANNEL_NAMES[CHANNEL_COUNT];       // 通道名称

    double sampling_rate;           // 采样率 Hz
    size_t buffer_capacity;         // 环形缓冲区容量
    double trigger_threshold;       // 触发阈值
    size_t pre_trigger_samples;     // 触发前采样点数
    size_t post_trigger_samples;    // 触发后采样点数

    TransientConfig()
        : sampling_rate(12800.0)
        , buffer_capacity(10000)
        , trigger_threshold(1.2)
        , pre_trigger_samples(1000)
        , post_trigger_samples(3000)
    {}
};

// 波形采样数据结构
struct WaveformSample
{
    uint64_t timestamp;                                     // 时间戳（微秒）
    double channels[TransientConfig::CHANNEL_COUNT];        // 各通道数据

    WaveformSample() : timestamp(0) {
        memset(channels, 0, sizeof(channels));
    }
};



// 简化的波形数据点结构
// 注意：一个时间戳可能对应多个数据点（多个传感器或测量位置）
struct WaveformDataPoint
{
    uint64_t timestamp;     // 时间戳（微秒）
    double ua, ub, uc;      // 三相电压
    double ia, ib, ic;      // 三相电流

    WaveformDataPoint() : timestamp(0), ua(0), ub(0), uc(0), ia(0), ib(0), ic(0) {}
};

// 一个时间戳对应的所有波形数据
struct TimestampWaveformData
{
    uint64_t timestamp;                 // 时间戳（微秒）
    std::queue<double> ua_values;       // A相电压所有值
    std::queue<double> ub_values;       // B相电压所有值
    std::queue<double> uc_values;       // C相电压所有值
    std::queue<double> ia_values;       // A相电流所有值
    std::queue<double> ib_values;       // B相电流所有值
    std::queue<double> ic_values;       // C相电流所有值

    TimestampWaveformData() : timestamp(0) {}

    TimestampWaveformData(uint64_t ts) : timestamp(ts) {}
};



// 暂态事件数据结构
struct TransientEvent
{
    uint64_t trigger_time;                                  // 触发时间
    std::vector<WaveformSample> samples;                    // 波形数据
    double rms_values[TransientConfig::CHANNEL_COUNT];      // 各通道有效值
    double peak_values[TransientConfig::CHANNEL_COUNT];     // 各通道峰值
    std::string event_type;                                 // 事件类型

    TransientEvent() : trigger_time(0) {
        memset(rms_values, 0, sizeof(rms_values));
        memset(peak_values, 0, sizeof(peak_values));
    }
};



//====================================================================================
// 环形缓冲区类
//====================================================================================
class CircularBuffer
{
public:
    CircularBuffer(size_t capacity);
    ~CircularBuffer();

    void Push(const WaveformSample& sample);
    bool GetSamples(std::vector<WaveformSample>& samples, size_t count);
    bool GetAllSamples(std::vector<WaveformSample>& samples);
    void Clear();
    size_t Size() const;
    bool IsFull() const;

private:
    std::vector<WaveformSample> m_buffer;
    size_t m_capacity;
    size_t m_head;
    size_t m_tail;
    size_t m_size;
    mutable std::mutex m_mutex;
};

//====================================================================================
// 暂态数据分析线程
//====================================================================================
class CTransientManager;
class CTransientThread : public CSL_thread
{
public:
    CTransientThread();
    virtual ~CTransientThread();
    void SetTransientManager(CTransientManager* p);

    virtual void run(void);
    virtual std::string ThreadName(void) const;
    virtual void RecordLog(const std::string& sMsg);

private:
    CTransientManager* m_pMng;
};

//====================================================================================
// 暂态数据分析管理器
//====================================================================================
class CTransientManager
{
public:
    static CTransientManager& CreateInstance();

    bool Init(void);
    void Exit(void);

    void thread_prev(void);
    void thread_func(void);
    void thread_exit(void);

    void OnRun(void);
    void Start(void);
    void Stop(void);

    // 数据处理接口
    void PushSampleData(const WaveformSample& sample);
    bool ProcessTransientData(void);
    void AnalyzeWaveform(const std::vector<WaveformSample>& samples, TransientEvent& event);

    // 波形数据报文接收接口（空实现）
    bool ReceiveWaveformPacket(const uint8_t* packet_data, size_t packet_length);

    // 波形数据放入环形缓存区接口
    bool PushTimestampWaveformData(const TimestampWaveformData& waveform_data);

    bool PushWaveformData(uint64_t timestamp, const std::vector<double>& ua_values,
                         const std::vector<double>& ub_values, const std::vector<double>& uc_values,
                         const std::vector<double>& ia_values, const std::vector<double>& ib_values,
                         const std::vector<double>& ic_values);

    bool PushWaveformDataPoints(const std::vector<WaveformDataPoint>& data_points);

    // 一个周波数据计算接口（仅声明，不实现）
    void CalculateOneCycleRMS(const std::vector<WaveformSample>& cycle_data);
    void CalculateOneCyclePeak(const std::vector<WaveformSample>& cycle_data);
    void CalculateOneCycleFrequency(const std::vector<WaveformSample>& cycle_data);
    void CalculateOneCyclePhase(const std::vector<WaveformSample>& cycle_data);
    void CalculateOneCycleTHD(const std::vector<WaveformSample>& cycle_data);
    void CalculateOneCyclePower(const std::vector<WaveformSample>& cycle_data);

    // 配置接口
    void SetConfig(const TransientConfig& config);
    TransientConfig GetConfig() const;

    // 事件处理接口
    void OnTransientEvent(const TransientEvent& event);
    void PublishEventData(const TransientEvent& event);

private:
    CTransientManager();
    ~CTransientManager();
    CTransientManager(const CTransientManager&) = delete;
    CTransientManager& operator=(const CTransientManager&) = delete;

    std::string  GetTimeamp(const uint8_t* data);
    int32_t littleEndianBytesToInt32(uint8_t bytes[4]);
    uint64_t GetTimestampFromTimeString(const std::string& time_str);
    float ConvertToFloat(const uint8_t* data);



private:
    std::unique_ptr<CTransientThread> m_pThread;
    std::unique_ptr<CircularBuffer> m_pBuffer;

    TransientConfig m_config;

    bool m_bRunning;
    bool m_bTriggerActive;
    uint64_t m_triggerTime;
    size_t m_postTriggerCount;

    std::string m_lastTimestamp;

    mutable std::mutex m_mutex;

    std::queue<float> m_VA_value;
    std::queue<float> m_VB_value;
    std::queue<float> m_VC_value;

    // 队列模型用于数据传输
    CpacketQueue<WaveformSample> m_sampleQueue;
    CpacketQueue<TransientEvent> m_eventQueue;
};

#endif // TRANSIENT_H