/*=====================================================================
 * 文件：test_compile.cpp
 *
 * 描述：编译测试 - 验证修复后的代码
 *
 * 作者：系统生成			2024年12月19日
 * 
 * 修改记录：
 =====================================================================*/

#include "transient.h"
#include "public_struct.h"
#include <iostream>

using namespace std;

int main()
{
    cout << "编译测试 - 验证修复后的代码" << endl;
    
    // 测试基本初始化
    if (!CTransientManager::CreateInstance().Init()) {
        cout << "初始化失败" << endl;
        return 1;
    }
    
    cout << "✓ 暂态分析管理器初始化成功" << endl;
    
    // 测试报文接收函数（空实现）
    const char* test_data = "test_packet_data";
    bool result = CTransientManager::CreateInstance().ReceiveWaveformPacket(
        reinterpret_cast<const uint8_t*>(test_data), strlen(test_data));
    
    cout << "✓ 报文接收函数调用成功：" << (result ? "true" : "false") << endl;
    
    // 测试数据推送
    TimestampWaveformData waveform_data(1640995200000000ULL);
    waveform_data.ua_values.push(311.0);
    waveform_data.ub_values.push(-155.5);
    waveform_data.uc_values.push(-155.5);
    waveform_data.ia_values.push(14.14);
    waveform_data.ib_values.push(-7.07);
    waveform_data.ic_values.push(-7.07);
    
    bool push_result = CTransientManager::CreateInstance().PushTimestampWaveformData(waveform_data);
    cout << "✓ 数据推送成功：" << (push_result ? "true" : "false") << endl;
    
    // 测试一个周波计算函数（空实现）
    std::vector<WaveformSample> cycle_data;
    WaveformSample sample;
    sample.timestamp = 1640995200000000ULL;
    sample.channels[0] = 311.0;
    sample.channels[1] = -155.5;
    sample.channels[2] = -155.5;
    sample.channels[3] = 14.14;
    sample.channels[4] = -7.07;
    sample.channels[5] = -7.07;
    cycle_data.push_back(sample);
    
    CTransientManager::CreateInstance().CalculateOneCycleRMS(cycle_data);
    CTransientManager::CreateInstance().CalculateOneCyclePeak(cycle_data);
    CTransientManager::CreateInstance().CalculateOneCycleFrequency(cycle_data);
    CTransientManager::CreateInstance().CalculateOneCyclePhase(cycle_data);
    CTransientManager::CreateInstance().CalculateOneCycleTHD(cycle_data);
    CTransientManager::CreateInstance().CalculateOneCyclePower(cycle_data);
    
    cout << "✓ 一个周波计算函数调用成功" << endl;
    
    // 清理
    CTransientManager::CreateInstance().Exit();
    
    cout << "✓ 编译测试完成，所有接口正常工作" << endl;
    return 0;
}

/*
编译运行：
cd megsky/src/DTAnalyzer
g++ -std=c++11 -I../../src/include -I../../src/include/tzc_std -I../../src/include/meg_pub \
    -I../../src/third_party/CJsonObject test_compile.cpp transient.cpp \
    -o test_compile -lpthread
./test_compile
*/
