#include <openssl/md5.h>
#include "mqtt_pub_inter.h"
#include "param_json.h"
#include "task_thread.h"
#include "iota_cfg.h"
#include "iota_init.h"
#include "iota_device_type.h"
#include "iota_hub.h"
#include "iota_login.h"
#include "iota_datatrans.h"
#include "JsonUtil.h"
#include "cJSON.h"
#include <iostream>
#include <algorithm>
// #include <mutex>
uint8_t AC_flag = 0;
uint8_t commandYC = 0;
std::string AC_deviceid;
std::string topic_dev;
std::string equipment_login;
std::string SCU_ESN;
std::list<enroll_type_s> m_enroll_type;
std::list<Registration_Information_s> m_deviceId_storage;
std::string topic_nodid;
std::list<topic_cunchu_s> topic_zhuancun;
extern gateway_connect_s m_lianwang;
char m_esn_sub[128];
uint8_t esn_flag = 1;
std::list<nodID_jishu_s> m_nodID_flag;
data_storage_s data;
Juint32 commandcmd;
// std::mutex commandcmdMtx;

std::string GetDeviceId()
{
    std::string dataString;

    auto ret = std::find_if(m_deviceId_storage.begin(), m_deviceId_storage.end(), [=, &dataString](Registration_Information_s const &para) -> bool
                            {
        if (para.dev.compare(topic_nodid) == 0) {
            dataString += ",\"deviceId\": \"";
            dataString += para.Deviceid + "\"";
            return true;
        } else {
            return false;
        } });
    if (ret == m_deviceId_storage.end())
    {
        dataString += ",\"deviceId\": \"";
        dataString += std::string("1") + "\"";
    }
    return dataString;
}

CDataObj::CDataObj(void)
{
    m_startFlag = false;
}

CDataObj::~CDataObj()
{
}

CTaskThread::CTaskThread(void)
    : m_pMng(NULL)
{
}

CTaskThread::~CTaskThread()
{
}

void CTaskThread::SetPackageAcquireManager(CTaskManager *p)
{
    m_pMng = p;
}

void CTaskThread::run(void)
{
    m_pMng->thread_prev();
    while (CSL_thread::IsEnableRun())
    {
        m_pMng->thread_func();
    }
    m_pMng->thread_exit();
}

std::string CTaskThread::ThreadName(void) const
{
    return std::string("MQTT 内部Broker线程");
}

void CTaskThread::RecordLog(const std::string &sMsg)
{
}

void HandleLoginSuccess(void *context, int messageId, int code, char *message)
{
    IEC_LOG_RECORD(eRunType, "login success.");
    CTaskManager::CreateInstance().login_flag = 1;
    CTaskManager::CreateInstance().Test_SubDev_Add();
    mskprintf(" HandleLoginSuccess(), login success\n");
}

void HandleLoginFailure(void *context, int messageId, int code, char *message)
{
    IOTA_Login();
    mskprintf(" ++++++++++++++++++++++++++++++++++++++++HandleLoginFailure(), login fiand\n");
}

void HandleConnectionLost(void *context, int messageId, int code, char *message)
{
    IOTA_Login();
    mskprintf(" Network disconnection and reconnection...\n");
}
void HandleLogoutSuccess(void *context, int messageId, int code, char *message)
{
    mskprintf(" HandleLogoutSuccess(), login fiand\n");
}
void HandleLogoutFailure(void *context, int messageId, int code, char *message)
{
    mskprintf(" HandleLogoutFailure(), login fiand\n");
}
void HandleSubscribeSuccess(void *context, int messageId, int code, char *message)
{
    mskprintf(" HandleSubscribeSuccess(), login fiand\n");
}
void HandleSubscribeFailure(void *context, int messageId, int code, char *message)
{
    mskprintf(" HandleSubscribeFailure(), login fiand\n");
}
void HandlePublishSuccess(void *context, int messageId, int code, char *message)
{
    mskprintf(" HandlePublishSuccess(), login fiand\n");
}
void HandlePublishFailure(void *context, int messageId, int code, char *message)
{
    mskprintf(" HandlePublishFailure(), login fiand\n");
}

void commandYCsummon(neb::CJsonObject obj, std::string deviceId)
{
    neb::CJsonObject oJson;
    mqtt_header_s jsheader = {0};
    std::string deciceid;
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        mskprintf("\nJson head fill failed---------.\n");
        return;
    }
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.AddEmptySubArray("body");
    neb::CJsonObject body;
    std::list<Registration_Information_s>::iterator it = m_deviceId_storage.begin();
    for (; it != m_deviceId_storage.end(); it++)
    {
        Registration_Information_s old_Information = *it;
        if (old_Information.Deviceid == deviceId)
        {
            deciceid = old_Information.dev;
            mskprintf("Information.Device:%s.\n", deciceid.c_str());
            // repeat_flag=1;
            break;
        }
    }
    body.Add("dev", deciceid);
    body.Add("totalcall", "0");
    body.AddEmptySubArray("body");
    neb::CJsonObject body_obj;
    body_obj["body"].Add(obj);
    oJson["body"].Add(body);
    const char *pub_topic = "MQTTIoT/dataCenter/JSON/get/request/realtime";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
    commandYC = 1;
}

void commandparameterSet(Juint32 mid, neb::CJsonObject paras, std::string nodid, std::string deviceId)
{
    neb::CJsonObject object;
    object.Add("msgType", "deviceRsp");
    object.Add("mid", mid);
    object.Add("errcode", 0);
    object.Add("deviceId", deviceId);
    object.Add("body", paras);
    IOTA_ServiceCommandRespense_Qos(mid, HW_SUCCESS, const_cast<char *>((object.ToString() + GetDeviceId()).c_str()), 0);
}
void commandparameterActivate(Juint32 mid, neb::CJsonObject obj)
{
    // char *data = NULL;
    neb::CJsonObject paras;
    obj.Get("paras", paras);
    std::string data_m;
    data_m = paras.ToString();
    // data = const_cast<char*>(data_m.c_str());
}
void unpackfilehf(Juint32 mid, neb::CJsonObject obj)
{
    std::string shuchu;
    std::string fileTransfer;
    uint8_t write_flag = 0;
    neb::CJsonObject paras;
    obj.Get("fileTransfer", paras);

    shuchu = paras.ToString();
    mskprintf("fileTransfer.c_str():%s.\n", shuchu.c_str());
    FILE *file = fopen("/data/app/SgseiriswitchCollect/configFile/SgseiriswitchCollectparam.json", "w");
    if (file != NULL)
    {
        fprintf(file, "%s", shuchu.c_str());
        fclose(file);
        write_flag = 1;
    }
    else
    {
        write_flag = 0;
    }
    neb::CJsonObject HF_json_obj;
    HF_json_obj.Add("msgType", "deviceRsp");
    HF_json_obj.Add("mid", mid);
    if (write_flag == 1)
    {
        HF_json_obj.Add("errcode", 0);
        char const *ptr = "{\"errcode\":\"0\"}";
        IOTA_ServiceCommandRespense_Qos(123, HW_SUCCESS, const_cast<char *>(ptr), 1);
    }
    else
    {
        HF_json_obj.Add("errcode", 1);
        char const *ptr = "{\"errcode\":\"1\"}";
        IOTA_ServiceCommandRespense_Qos(123, HW_SUCCESS, const_cast<char *>(ptr), 1);
    }

    std::string mssm = HF_json_obj.ToString();
    mskprintf("mssm:%s.\n", mssm.c_str());
    C256String pub_topic1;
    pub_topic1.Format("MQTTIoT/SgseiriswitchCollect/command/Reboot");
    // mssm ="";
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic1.ToChar(), 256, pub_topic1.Length());
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}
void commandtimeCheck(Juint32 mid, neb::CJsonObject obj)
{
    // char *data = NULL;
    std::string data_m;
    std::string timeCheck;
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        mskprintf("\nJson head fill failed---------.\n");
        return;
    }
    obj.Get("timeCheck", timeCheck);
    std::string newDate = timeCheck.substr(0, 10);
    newDate.append("T");
    newDate.append(timeCheck.substr(11));
    newDate.append(".000");
    // std::string makeaa =
    neb::CJsonObject oJson;
    oJson.Add("token", 123);
    oJson.Add("timestamp", jsheader.timestamp);
    neb::CJsonObject body;
    body.Add("setTime", newDate);
    body.Add("reqTime", newDate);
    oJson.Add("body", body);
    const char *pub_topic = "MQTTIoT/OS-system/JSON/request/setTime";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
        mskprintf("\nJasdasdasdasdsadsa-------%s.\n", mssm.c_str());
    }
}
void commandtimeRead(Juint32 mid, neb::CJsonObject obj)
{
    // char *data = NULL;
    std::string data_m;
    std::string timeRead;
    obj.Get("timeRead", timeRead);
    neb::CJsonObject oJson;

    struct timeval tv;
    struct timezone tz;
    struct tm *t;

    gettimeofday(&tv, &tz);
    t = localtime(&tv.tv_sec);

    char buffer[64] = {0};
    // snprintf(buffer, sizeof(buffer),"%02d:%02d:%02d",  t->tm_hour, t->tm_min,t->tm_sec);
    snprintf(buffer, sizeof(buffer), "%04d-%02d-%02d %02d:%02d:%02d",
             1900 + t->tm_year, 1 + t->tm_mon, t->tm_mday, t->tm_hour, t->tm_min, t->tm_sec);

    oJson.Add("timeRead", buffer);
    data_m = oJson.ToString();
    // data = const_cast<char*>(data_m.c_str());

    // IOTA_ServiceCommandRespense_Qos(mid, HW_SUCCESS, data, 1);
}
void HandleCommandArrived(void *context, int messageId, int code, char *message)
{
    std::string nodid;
    neb::CJsonObject oJson;
    oJson.Parse(message);
    command_issued_s command;
    if (oJson.Get("msgType", command.msgType))
    {
        mskprintf("msgType = %s.\n\r", command.msgType.c_str());
    }
    if (oJson.Get("mid", command.mid))
    {
        mskprintf("mid = %d.\n\r", command.mid);
    }
    if (oJson.Get("cmd", command.cmd))
    {
        mskprintf("cmd = %s.\n\r", command.cmd.c_str());
    }
    neb::CJsonObject obj_paras;
    if (oJson.Get("paras", obj_paras))
    {
        command.paras = obj_paras.ToString();
    }
    mskprintf("serviceProperties = %s.\n\r", command.paras.c_str());

    if (oJson.Get("serviceId", command.serviceId))
    {
        mskprintf("serviceId = %s.\n\r", command.serviceId.c_str());
    }
    if (oJson.Get("deviceId", command.deviceId))
    {
        mskprintf("deviceId = %s.\n\r", command.deviceId.c_str());
    }
    // commandcmdMtx.lock();
    commandcmd = command.mid;
    // commandcmdMtx.lock();

    if (command.cmd.compare("fileTransfer") == 0)
    {
        unpackfilehf(command.mid, obj_paras);
    }

    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        mskprintf("\nJson head fill failed---------.\n");
        return;
    }
    neb::CJsonObject pJson;

    pJson.Add("token", jsheader.token);
    pJson.Add("timestamp", jsheader.timestamp);
    pJson.AddEmptySubObject("body");
    neb::CJsonObject &body = pJson["body"];
    body.Add("requestId", "123");
    body.Add("serviceId", command.serviceId);
    body.Add("method", command.cmd);
    body.Add("cmdContent", obj_paras);

    std::list<Registration_Information_s>::iterator it = m_deviceId_storage.begin();
    for (; it != m_deviceId_storage.end(); it++)
    {
        Registration_Information_s device_old = *it;
        if (device_old.Deviceid.compare(command.deviceId) == 0)
        {
            nodid = device_old.dev;
            break;
        }
        else
        {
            nodid = command.deviceId;
        }
    }

    std::string mssm = pJson.ToString();
    C256String pub_topic;
    mskprintf("nodid:%s.\n", nodid.c_str());
    pub_topic.Format("MQTTIoT/MQTTTrData/JSON/action/request/terminalCmd/%s", nodid.c_str());
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
        item.msg_send_lenth = mssm.size();
        item.retained = 1;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}
void HandleSubDeviceAddResult(void *context, int messageId, int code, char *message)
{
    IEC_LOG_RECORD(eRunType, " equipment add success.");
    char *deviceId;
    uint8_t repeat_flag = 0;
    neb::CJsonObject oJson;
    oJson.Parse(message);
    std::string deviceId_linshi;
    neb::CJsonObject dataArray;
    oJson.Get("data", dataArray);
    if (dataArray.IsArray())
    {
        int size = dataArray.GetArraySize();
        for (int i = 0; i < size; ++i)
        {
            neb::CJsonObject dataObject;
            dataArray.Get(i, dataObject);

            // 解析 "deviceInfo" 对象
            neb::CJsonObject deviceInfoObject;
            dataObject.Get("deviceInfo", deviceInfoObject);
            std::string manufacturerId;
            std::string nodeId;
            deviceInfoObject.Get("manufacturerId", manufacturerId);
            std::string name = deviceInfoObject["name"].ToString();
            std::string model = deviceInfoObject["model"].ToString();
            deviceInfoObject.Get("name", name);
            deviceInfoObject.Get("model", model);
            deviceInfoObject.Get("nodeId", nodeId);
            deviceInfoObject.Get("deviceId", deviceId_linshi);
            if (model.compare("TTU") == 0)
            {
                AC_deviceid = deviceId_linshi.c_str();
            }
            mskprintf("model:%s.\n", model.c_str());
            mskprintf("AC_deviceid==AC_deviceid==%s.\n", AC_deviceid.c_str());
            Registration_Information_s Information;
            Information.nodid = nodeId.c_str();
            Information.Deviceid = deviceId_linshi.c_str();
            for (const auto &item : topic_zhuancun)
            {
                topic_cunchu_s linshi = item;
                if (linshi.name == name)
                {
                    Information.dev = linshi.dev_topic;
                }
            }
            // Information.dev = topic_dev;
            if (m_deviceId_storage.empty())
            {
                mskprintf("dev-------------------%s\n", Information.dev.c_str());
                mskprintf("dev-------------------%s\n", Information.Deviceid.c_str());
                mskprintf("dev-------------------%s\n", Information.nodid.c_str());
                m_deviceId_storage.push_back(Information);
            }
            std::list<Registration_Information_s>::iterator it = m_deviceId_storage.begin();
            for (; it != m_deviceId_storage.end(); it++)
            {
                Registration_Information_s old_Information = *it;
                if (old_Information.nodid == Information.nodid)
                {
                    mskprintf("Information.Device:%s.\n", Information.Deviceid.c_str());
                    repeat_flag = 1;
                    break;
                }
            }
            if (repeat_flag == 0)
            {
                mskprintf("dev-------------------%s\n", Information.dev.c_str());
                mskprintf("dev-------------------%s\n", Information.Deviceid.c_str());
                mskprintf("dev-------------------%s\n", Information.nodid.c_str());
                m_deviceId_storage.push_back(Information);
                mskprintf("Information:%s.\n", Information.Deviceid.c_str());
            }
        }
    }

    deviceId = const_cast<char *>(deviceId_linshi.c_str());
    IEC_LOG_RECORD(eRunType, "get deviceId:%s.\n", deviceId_linshi.c_str());
    char const *ptr = "ONLINE";
    IOTA_DeviceStatusUpdate(1003, deviceId, const_cast<char *>(ptr));

    equipment_login.clear();
}
void HandleSubDeviceDeleteResult(void *context, int messageId, int code, char *message)
{
    mskprintf(" HandleSubDeviceDeleteResult(), login fiand\n");
}
void HandleSubDeviceUpdateResult(void *context, int messageId, int code, char *message)
{
    mskprintf(" HandleSubDeviceUpdateResult(), login fiand\n");
}
void HandleDeviceQueryResult(void *context, int messageId, int code, char *message)
{
    mskprintf(" HandleDeviceQueryResult(), login fiand\n");
}
//***************************************************************
// 报文获取管理�???
//***************************************************************
CTaskManager &CTaskManager::CreateInstance()
{
    static CTaskManager Mng;
    return Mng;
}

bool CTaskManager::Init(void)
{
    bool bRet(false);
    char *addr;
    uint32_t port;
    // uint8_t fangshi;
    char *device;
    char *paawd;
    char *keepAlivetime;
    addr = const_cast<char *>(m_lianwang.g_gatewayAddr.c_str());
    port = m_lianwang.g_gatewayPort;
    device = const_cast<char *>(m_lianwang.g_gatewayDeviceId.c_str());
    paawd = const_cast<char *>(m_lianwang.g_gatewayPaawd.c_str());
    keepAlivetime = const_cast<char *>(m_lianwang.g_keepAlivetime.c_str());

    char const *ptr = ".";
    IOTA_Init(const_cast<char *>(ptr), NULL);

    IOTA_ConfigSetStr(EN_IOTA_CFG_MQTT_ADDR, addr);
    IOTA_ConfigSetUint(EN_IOTA_CFG_MQTT_PORT, port);
    IOTA_ConfigSetStr(EN_IOTA_CFG_DEVICEID, device);
    IOTA_ConfigSetStr(EN_IOTA_CFG_DEVICESECRET, paawd);

    /**
     * It is not suitable to use MQTT heartbeat for scenes that cannot respond to heartbeat response in time;
     * Please realize the heartbeat function by yourself, This value is set to 0.
     */
    IOTA_ConfigSetStr(EN_IOTA_CFG_KEEP_ALIVE_TIME, keepAlivetime);

    /**
     * EN_IOTA_CFG_URL_PREFIX_TCP : TCP Channel , 1883
     * EN_IOTA_CFG_URL_PREFIX_SSL : SSL Channel , 8443
     * The default is TCP, which can be configured according to the actual situation
     */
    mskprintf("port=%d.\n", port);
    if (port == 8443)
    {
        mskprintf("EN_IOTA_CFG_URL_PREFIX_SSL.\n");
        IOTA_ConfigSetUint(EN_IOTA_CFG_MQTT_URL_PREFIX, EN_IOTA_CFG_URL_PREFIX_SSL);
    }
    else
    {
        mskprintf("EN_IOTA_CFG_URL_PREFIX_TCP.\n");
        IOTA_ConfigSetUint(EN_IOTA_CFG_MQTT_URL_PREFIX, EN_IOTA_CFG_URL_PREFIX_TCP);
    }

    IOTA_ConfigSetUint(EN_IOTA_CFG_AUTH_MODE, EN_IOTA_CFG_AUTH_MODE_NODE_ID);
    IOTA_ConfigSetUint(EN_IOTA_CFG_QOS, 0);

    // 设置回调函数
    IOTA_SetCallback(EN_IOTA_CALLBACK_CONNECT_SUCCESS, HandleLoginSuccess);
    IOTA_SetCallback(EN_IOTA_CALLBACK_CONNECT_FAILURE, HandleLoginFailure);
    IOTA_SetCallback(EN_IOTA_CALLBACK_CONNECTION_LOST, HandleConnectionLost);

    IOTA_SetCallback(EN_IOTA_CALLBACK_DISCONNECT_SUCCESS, HandleLogoutSuccess);
    IOTA_SetCallback(EN_IOTA_CALLBACK_DISCONNECT_FAILURE, HandleLogoutFailure);

    IOTA_SetCallback(EN_IOTA_CALLBACK_SUBSCRIBE_SUCCESS, HandleSubscribeSuccess);
    IOTA_SetCallback(EN_IOTA_CALLBACK_SUBSCRIBE_FAILURE, HandleSubscribeFailure);

    IOTA_SetCallback(EN_IOTA_CALLBACK_PUBLISH_SUCCESS, HandlePublishSuccess);
    IOTA_SetCallback(EN_IOTA_CALLBACK_PUBLISH_FAILURE, HandlePublishFailure);

    IOTA_SetCallback(EN_IOTA_CALLBACK_COMMAND_ARRIVED, HandleCommandArrived);

    IOTA_SetCallback(EN_IOTA_CALLBACK_DEVICE_ADDITION_RESULT, HandleSubDeviceAddResult);
    IOTA_SetCallback(EN_IOTA_CALLBACK_DEVICE_DELETION_RESULT, HandleSubDeviceDeleteResult);
    IOTA_SetCallback(EN_IOTA_CALLBACK_DEVICE_UPDATE_RESULT, HandleSubDeviceUpdateResult);
    IOTA_SetCallback(EN_IOTA_CALLBACK_DEVICE_QUERY_RESULT, HandleDeviceQueryResult);

    int ret = IOTA_Login();
    mskprintf("ret num: %d\n", ret);
    if (ret != 0)
    {
        mskprintf("login failed, result %d\n", ret);
    }

    bRet = m_Thread.start();
    return bRet;
}

void CTaskManager::Exit(void)
{

    m_Thread.close();
    m_Thread.wait_for_end();
}

void CTaskManager::thread_prev(void)
{
}

void CTaskManager::thread_func(void)
{
    OnRun();
}

void CTaskManager::thread_exit(void)
{
}

void CTaskManager::OnRun(void)
{
    // char *deviceId;
    ii_sleep(10000);
    if (m_AC_T.CalOvertime() == true)
    {
        AC_flag = 1;
        m_AC_T.StopCount();
    }
    if (AC_flag == 1)
    {
        AC_flag = 0;
        // login_flag = 0;
        // deviceId = const_cast<char*>(AC_deviceid.c_str());
        mskprintf("AC_deviceid:%s.", AC_deviceid.c_str());
        // IOTA_DeviceStatusUpdate(1003,deviceId,"OFFLINE");
    }
    if (CTaskManager::login_flag == 1)
    {
        int stat = IOTA_IsConnected();
        mskprintf(" client connect status: %d [1: connected 0: disconnected]\n", stat);
    }
    char const *deviceIDPtr = "megsky";
    char const *serviceIDPtr = "TEST";
    char const *servicePtr = "{\"aa\":120}";
    IOTA_ServiceDataReport_Qos(const_cast<char *>(deviceIDPtr), const_cast<char *>(serviceIDPtr), const_cast<char *>(servicePtr), 1);
    neb::CJsonObject ojson;
    ojson.Add("start", 0);
    std::string mssm = ojson.ToString();
    mskprintf("mssm:%s.\n", mssm.c_str());
    C256String pub_topic;
    pub_topic.Format("/v1/devices/%s/mg/heart", m_lianwang.g_gatewayDeviceId.c_str());
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
        item.msg_send_lenth = mssm.size();
        item.retained = 1;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}
void CTaskManager::Test_GW_CustomTopic_Rerort(char *deviceId)
{
    // int qos = 0;
    int messageId = 0;

    char *topic_ver = const_cast<char *>("v1");
    char *topic = const_cast<char *>("customtopic-send");

    ST_IOTA_BatchReportProperties dev_info = {0};
    dev_info.p_deviceid = deviceId;
    dev_info.p_serviceid = const_cast<char *>("TEST");
    dev_info.p_payload = const_cast<char *>("{\"aa\":120}");

    messageId = IOTA_ServiceCustomTopicReport(&dev_info, topic_ver, topic);

    mskprintf("agentlite_demo.c: Test_CustomTopicReport(), report data with messageId %d \n", messageId);
}

void CTaskManager::Test_SubDev_Add(void)
{
}

void CTaskManager::Start(void)
{
    std::list<CDataObj *>::iterator itInit = m_dataObj.begin();
    for (; itInit != m_dataObj.end(); itInit++)
    {
        CDataObj *objd = *itInit;
        if (objd->m_startFlag)
        {
            MqttPackOnline(*itInit); // 一直发上线就可以了
            IEC_LOG_RECORD(eRunType, "guid (%s)online.", objd->m_guid.guid.c_str());
            ii_sleep(1000);
        }
    }
}

void CTaskManager::Stop(void)
{
}

void CalMachinecode(char *str, char *currentCode)
{
    int RecvBuff[4096];
    memset(RecvBuff, 0, sizeof(RecvBuff));
    int nCount = 0;

    // 将机器码字�?�串�???�???为整数数�???
    for (int i = 0; i < (int)strlen(str); i += 2)
    {
        char str2[3] = {str[i], str[i + 1], '\0'};
        int strtxt = (int)strtol(str2, NULL, 16);
        RecvBuff[nCount] = strtxt;
        nCount++;
    }

    // 根据机器码�?�算注册�???
    int m;
    for (int i = 0; i < nCount; i++)
    {
        int n = RecvBuff[i];
        if (n == 0x10)
        {
            m = 0xFF;
            char *descPtr = currentCode;
            sprintf(descPtr, "%s%02X%s", currentCode, m, "FF");
        }
        else if (n % 5 == 3)
        {
            m = n + 2;
            char *descPtr = currentCode;
            sprintf(descPtr, "%s%02X%s", currentCode, m, "MEG");
        }
        else
        {
            m = n - 1;
            if (m < 0)
            {
                m = 0;
                char *descPtr = currentCode;
                sprintf(descPtr, "%s%02X%s", currentCode, m, "10");
            }
            else if (m % 3 == 1)
            {
                char *descPtr = currentCode;
                sprintf(descPtr, "%s%02X%s", currentCode, m, "A#");
            }
            else
            {
                char *descPtr = currentCode;
                sprintf(descPtr, "%s%02X%s", currentCode, m, "C$");
            }
        }
    }
}
void app_enroll_OS()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        mskprintf("\nJson head fill failed---------.\n");
        return;
    }
    neb::CJsonObject oJson;
    oJson.Add("token", 321);
    oJson.Add("timestamp", jsheader.timestamp);
    neb::CJsonObject body;
    body.Add("name", "MQTTIoT");
    body.Add("version", 1);
    body.Add("releaseDate", "2023.08.30");
    oJson.Add("body", body);
    std::string mssm = oJson.ToString();
    mskprintf("+++++++++++++++++++++++:%s.\n", mssm.c_str());
    C256String pub_topic;
    pub_topic.Format("MQTTIoT/OS-system/JSON/request/register");
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
        item.msg_send_lenth = mssm.size();
        item.retained = 1;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

void unpackYChf(neb::CJsonObject obj)
{
    // char *data_sc = NULL;
    std::string data_string;
    neb::CJsonObject data_comd;
    neb::CJsonObject obj_body;
    obj.Get("body", obj_body);
    if (!obj_body.IsArray())
    {
        IEC_LOG_RECORD(eErrType, "file:" IEC_MQTTFILE_FILEPATH "read body object failed. not array.");
        return;
    }
    int len = obj_body.GetArraySize();
    if (len != 0)
    {
        for (int i = 0; i < len; i++)
        {
            neb::CJsonObject data_body;
            if (!obj_body.Get(i, data_body))
            {
                mskprintf("array(%d) get failed \n", i);
                return;
            }
            neb::CJsonObject o_body;
            data_body.Get("body", o_body);
            int asize = o_body.GetArraySize();
            for (int i = 0; i < asize; i++)
            {
                neb::CJsonObject value_body;
                if (!o_body.Get(i, value_body))
                {
                    mskprintf("array(%d) get failed \n", i);
                    return;
                }
                value_body.Get("name", data.name);
                value_body.Get("val", data.val);
                if (data.val.empty())
                {
                    data.val = "0";
                }
                data_comd.Add(data.name, data.val);
            }
        }
        data_string = data_comd.ToString();
        // data_sc = const_cast<char*>(data_string.c_str());
        // IOTA_ServiceCommandRespense_Qos(commandcmd, HW_SUCCESS, data_sc, 1);
    }
    else
    {
        neb::CJsonObject json;
        json.Add("PowerOff_Alm", "0");
        json.Add("PowerOn_Alm", "1");
        json.Add("PhV_phsA", "220");
        json.Add("PhV_phsB", "222");
        json.Add("PhV_phsC", "224");
        data_string = json.ToString();
        // data_sc = const_cast<char*>(data_string.c_str());
        // IOTA_ServiceCommandRespense_Qos(commandcmd, HW_SUCCESS, data_sc, 1);
    }
}
void Replyparsing(neb::CJsonObject obj)
{
    mskprintf("realdata*******************%s*********************\n", obj.ToString().c_str());
    neb::CJsonObject json;
    obj.Get("body", json);
    std::string xxx = json.ToString();
    mskprintf("\x1b[32mobj:%s.\x1b[0m\n", xxx.c_str());
    // char *data_sc = NULL;
    std::string data_string;
    std::string serviceid11;
    neb::CJsonObject serviceProperties;
    json.Get("serviceId", serviceid11);
    json.Get("serviceProperties", serviceProperties);
    mskprintf("serviceId:%s.\n", serviceid11.c_str());
    mskprintf("serviceid11.compare(timeRead):%d\n", serviceid11.compare("timeRead"));

    if (serviceid11.compare("timeRead") == 0)
    {
        IOTA_ServiceCommandRespense_Qos(commandcmd, HW_SUCCESS, const_cast<char *>((serviceProperties.ToString() + GetDeviceId()).c_str()), 1);
    }
    mskprintf("serviceId:%s.\n", serviceid11.c_str());
    if (serviceid11.compare("analog_Get") == 0)
    {
        IOTA_ServiceCommandRespense_Qos(commandcmd, HW_SUCCESS, const_cast<char *>((serviceProperties.ToString() + GetDeviceId()).c_str()), 1);
    }
    mskprintf("serviceId:%s.\n", serviceid11.c_str());
    if (serviceid11.compare("discrete_Get") == 0)
    {
        if ((serviceProperties.IsEmpty()) || (serviceProperties.GetArraySize() < 2))
        {
            serviceProperties.Add("PowerOff_Alm", "0");
            serviceProperties.Add("PowerOn_Alm", "1");
        }
        IOTA_ServiceCommandRespense_Qos(commandcmd, HW_SUCCESS, const_cast<char *>((serviceProperties.ToString() + GetDeviceId()).c_str()), 1);
    }
    mskprintf("serviceId:%s.\n", serviceid11.c_str());
    if (serviceid11.compare("timeCheck") == 0)
    {
        IOTA_ServiceCommandRespense_Qos(commandcmd, HW_SUCCESS, const_cast<char *>((serviceProperties.ToString() + GetDeviceId()).c_str()), 1);
    }
    mskprintf("serviceId:%s.\n", serviceid11.c_str());
    if (serviceid11.compare("parameter_Set") == 0)
    {
        neb::CJsonObject object;
        object.Add("msgType", "deviceRsp");
        object.Add("mid", "5");
        object.Add("errcode", "0");
        object.Add("body", serviceProperties);
        IOTA_ServiceCommandRespense_Qos(commandcmd, HW_FAILED, const_cast<char *>((object.ToString() + GetDeviceId()).c_str()), 1);
    }
    if (serviceid11.compare("parameter_Get") == 0)
    {
        IOTA_ServiceCommandRespense_Qos(commandcmd, HW_SUCCESS, const_cast<char *>((serviceProperties.ToString() + GetDeviceId()).c_str()), 1);
    }

    if (serviceid11.compare("parameter_Activate") == 0)
    {
        IOTA_ServiceCommandRespense_Qos(commandcmd, HW_SUCCESS, const_cast<char *>((serviceProperties.ToString() + GetDeviceId()).c_str()), 1);
    }
}
#define ESN_PATH "/data/app/common/"
bool CTaskManager::UnpackData(mqtt_data_info_s &real_data)
{

    char ESN_check_code[128];
    char currentCode[128] = "";

    neb::CJsonObject oJson;
    if (!oJson.Parse(real_data.msg_send))
    {
        IEC_LOG_RECORD(eErrType, "json_loadb real_data.msg_send failed.");
        return false;
    }

    std::string mssm = oJson.ToString();

    if (mssm.size() > 0)
    {
        mskprintf("unpack mqtt topic:%s.\r\n", real_data.pubtopic);
        mskprintf("unpack mqtt msg:%s.\r\n", mssm.c_str());
    }

    C256String pub_topic;
    std::string appName = GetFirsAppName(real_data.pubtopic); // �????�???? 首字�????
    topic_nodid = GetLastSegment(real_data.pubtopic);

    // 获取token�????

    // const char* tokenStr = NULL;

    // 解析 上线通知
    if (strstr(real_data.pubtopic, "/MQTTIot/JSON/report/notification/terminalStatus/"))
    {
        Unpacklaunch(oJson);
    }
    // 解析 主动上报
    if (strstr(real_data.pubtopic, "/MQTTIot/JSON/report/notification/terminalData/"))
    {
        mskprintf("UnpackData----------------1\n");
        if (login_flag == 1)
        {
            mskprintf("UnpackData----------------2\n");
            Unpackdataup(oJson);
            mskprintf("UnpackData----------------3\n");
        }
    }
    if (strstr(real_data.pubtopic, "acMeter/OS-system/JSON/response/keepAlive"))
    {
        m_AC_T.SetParam(25 * 1000);
        m_AC_T.StartCount();
        AC_flag = 0;
    }
    if (strstr(real_data.pubtopic, "OS-system/MQTTIoT/JSON/response/setSshMode"))
    {
    }
    if (strstr(real_data.pubtopic, "MQTTTrData/MQTTIot/JSON/action/response/terminalCmd/"))
    {
        Replyparsing(oJson);
    }
    if (strstr(real_data.pubtopic, "OS-system/MQTTIoT/JSON/response/setTime"))
    {
    }
    if (strstr(real_data.pubtopic, "OS-system/MQTTIoT/JSON/response/devInfo"))
    {
        neb::CJsonObject body;
        std::string esn;
        oJson.Get("body", body);
        body.Get("esn", esn);
        SCU_ESN = esn;
        esn_flag = 0;
        strcpy(m_esn_sub, esn.c_str());
        CalMachinecode(m_esn_sub, currentCode);
        if (currentCode[0] == '\0')
        {
            return 0;
            //  exit(0);
        }
        else
        {
            strcpy(ESN_check_code, ESN_PATH);
            strcat(ESN_check_code, currentCode);
            FILE *fp = fopen(ESN_check_code, "r");
            if (NULL == fp)
            {
                exit(0);
                fclose(fp);
                memset(currentCode, 0, sizeof(currentCode));
            }
            else
            {
                fclose(fp);
                memset(currentCode, 0, sizeof(currentCode));
                app_enroll_OS();
            }
        }
    }
    // 保活
    if (strstr(real_data.pubtopic, "OS-system/MQTTIoT/JSON/request/keepAlive"))
    {
        mqtt_header_s jsheader = {0};
        int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
        if (ret != MQTT_OK)
        {
            mskprintf("\nJson head fill failed---------.\n");
            return 1;
        }
        neb::CJsonObject oJson;
        oJson.Add("token", 3321);
        oJson.Add("timestamp", jsheader.timestamp);
        oJson.Add("statusCode", 0);
        // oJson.Add("timestamp",jsheader.timestamp);
        std::string mssm = oJson.ToString();
        mskprintf("mssm:%s.\n", mssm.c_str());
        C256String pub_topic;
        pub_topic.Format("MQTTIoT/OS-system/JSON/response/keepAlive");
        if (mssm.size() > 0)
        {
            mqtt_data_info_s item = {0};
            memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
            memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
            item.msg_send_lenth = mssm.size();
            item.retained = 1;
            CMqttClientInterManager::CreateInstance().Push_SendItem(item);
        }
    }

    std::list<CDataObj *>::iterator iter = m_dataObj.begin();
    for (; iter != m_dataObj.end(); ++iter)
    {
        CDataObj *obj = *iter;
        C256String cmdtopic;

        cmdtopic.Format("esdk/terminal/cmdReq/%s/%s/%s/%s/%s",
                        obj->m_manufacturerId.c_str(),
                        obj->m_manufacturerName.c_str(),
                        obj->m_deviceType.c_str(),
                        obj->m_model.c_str(),
                        obj->m_guid.dev.c_str());

        if (obj != NULL)
        {
            mskprintf("obj topic:%s.\r\n", obj->m_topic.c_str());
            if (obj->m_topic.compare(real_data.pubtopic) == 0)
            {
                UnpackRealData(obj, oJson);
            }
            else if (cmdtopic.ToString().compare(real_data.pubtopic) == 0)
            { // 命令处理
              // UnpackCmdData(obj,oJson);
            }
        }
    }

    return true;
}

bool CTaskManager::UnpackRealData(CDataObj *dobj, neb::CJsonObject obj)
{

    bool b(false);
    if (!dobj->m_startFlag)
    {
        dobj->m_startFlag = true;
        dobj->m_startTime = ii_get_current_mtime();
    }

    if (dobj == NULL)
    {
        IEC_LOG_RECORD(eErrType, "UnpackRealData dobj = NULL.");
        return false;
    }
    bool bRet = true;
    neb::CJsonObject obj_body;
    if (!obj.Get("body", obj_body))
    {
        IEC_LOG_RECORD(eErrType, "UnpackRealData body = NULL.");
        return false;
    }
    if (!obj_body.IsArray())
    {
        IEC_LOG_RECORD(eErrType, "UnpackRealData body is not Array.");
        return false;
    }
    int asize = obj_body.GetArraySize();
    // 清空当前的变化name
    dobj->m_SpontObj.m_Spontnames.clear();

    for (int i = 0; i < asize; i++)
    {
        neb::CJsonObject obj_data;
        if (!obj_body.Get(i, obj_data))
        {
            mskprintf("UnpackRealData body data get error i = %d.\n", i);
            return false;
        }
        std::string name;
        std::string val;
        std::string stype;
        bRet &= obj_data.Get("name", name);
        bRet &= obj_data.Get("val", val);

        if (name.size() > 0)
        {
            dobj->m_RealDatas[name] = val;
            obj_data.Get("type", stype);
            if (stype.compare("cycle") == 0)
            { // 周期数据赋�?
                continue;
            }
            mskprintf("yx change name = (%s),val = (%s).\n", name.c_str(), val.c_str());
            // 如果name在遥信中则发送变�???
            std::string serveiceName;
            if (IsFindDiscrete(dobj, name, serveiceName))
            {
                dobj->m_SpontObj.m_Spontnames.push_back(name);
                dobj->m_SpontObj.m_serviceName = serveiceName;
                b = true;
            }
        }
    }
    if (b)
    {
        MqttPackSpontData(dobj);
    }
    return bRet;
}

std::string CTaskManager::UnpackToken(neb::CJsonObject obj)
{
    std::string tokenStr;
    if (obj.Get("token", tokenStr))
    {
        return tokenStr;
    }
    return NULL;
}

void CTaskManager::MqttPackEsn()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;

    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    const char *pub_topic = CFG_APP_NAME "/get/request/esdk/deviceInfo";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

void CTaskManager::Unpacklaunch(neb::CJsonObject obj)
{
    int topic_flag = 0;
    int a = 0;
    int panduan_flag = 0;
    int enroll_type_flag = 0;

    // bool bRet = true;
    neb::CJsonObject obj_body;
    if (!obj.Get("body", obj_body))
    {
        return;
    }
    equipment_Add_s status;
    if (obj_body.Get("manufacturerId", status.manufacturerId))
    {
        mskprintf("manufacturerId = %s.\n\r", status.manufacturerId.c_str());
    }
    if (obj_body.Get("manufacturerName", status.manufacturerName))
    {
        mskprintf("manufacturerName = %s.\n\r", status.manufacturerName.c_str());
    }
    if (obj_body.Get("deviceType", status.deviceType))
    {
        mskprintf("deviceType = %s.\n\r", status.deviceType.c_str());
    }
    if (obj_body.Get("model", status.model))
    {
        mskprintf("model = %s.\n\r", status.model.c_str());
    }
    if (obj_body.Get("protocolType", status.protocolType))
    {
        mskprintf("protocolType = %s.\n\r", status.protocolType.c_str());
    }
    if (obj_body.Get("devName", status.devName))
    {
        mskprintf("devName = %s.\n\r", status.devName.c_str());
    }
    if (obj_body.Get("devDesc", status.devDesc))
    {
        mskprintf("devDesc = %s.\n\r", status.devDesc.c_str());
    }
    if (obj_body.Get("subName", status.subName))
    {
        mskprintf("subName = %s.\n\r", status.subName.c_str());
    }
    if (obj_body.Get("state", status.state))
    {
        mskprintf("state = %s.\n\r", status.state.c_str());
    }
    if (obj_body.Get("event-time", status.event_time))
    {
        mskprintf("event-time = %s.\n\r", status.event_time.c_str());
    }
    topic_dev = topic_nodid;
    if (SCU_ESN.empty())
    {
        return;
    }
    std::string nodID_IOT = SCU_ESN + "_" + status.model;
    nodID_jishu_s zhongjianzhi;
    zhongjianzhi.dev = topic_nodid;
    zhongjianzhi.model = status.model;
    zhongjianzhi.iot_nodid = nodID_IOT;
    if (m_nodID_flag.empty())
    {
        m_nodID_flag.push_back(zhongjianzhi);
    }
    for (const auto &item : m_nodID_flag)
    {
        nodID_jishu_s panduan_nod = item;
        if (panduan_nod.dev != topic_nodid)
        {
            if (panduan_nod.model == status.model)
            {
                panduan_flag = 1;
                a++;
                break;
            }
        }
        else
        {
            nodID_IOT = panduan_nod.iot_nodid;
            panduan_flag = 2;
            break;
        }
    }
    if (panduan_flag == 0)
    {
        m_nodID_flag.push_back(zhongjianzhi);
    }
    if (panduan_flag == 1)
    {
        zhongjianzhi.iot_nodid = SCU_ESN + "_" + status.model + "_" + std::to_string(a);
        nodID_IOT = SCU_ESN + "_" + status.model + "_" + std::to_string(a);
        m_nodID_flag.push_back(zhongjianzhi);
        panduan_flag = 0;
    }

    equipment_login = status.state.c_str();
    topic_cunchu_s zanshi;
    zanshi.name = nodID_IOT;
    zanshi.dev_topic = topic_nodid;
    if (topic_zhuancun.empty())
    {
        topic_zhuancun.push_back(zanshi);
    }
    else
    {
        for (const auto &item : topic_zhuancun)
        {
            topic_cunchu_s topic_linshi = item;
            if (topic_linshi.dev_topic == topic_nodid)
            {
                topic_flag = 1;
                break;
            }
        }
        if (topic_flag == 0)
        {
            topic_zhuancun.push_back(zanshi);
        }
    }
    ST_IOTA_DEVICE_INFO stDeviceInfo = {0};

    stDeviceInfo.pcName = const_cast<char *>(nodID_IOT.c_str());
    stDeviceInfo.pcNodeId = const_cast<char *>(nodID_IOT.c_str());
    stDeviceInfo.pcManufacturerId = const_cast<char *>(status.manufacturerId.c_str());
    stDeviceInfo.pcModel = const_cast<char *>(status.model.c_str());
    enroll_type_s jieshou_type;
    jieshou_type.dev = topic_nodid;
    jieshou_type.model = status.model;
    jieshou_type.iot_nodid = nodID_IOT;
    jieshou_type.manuid = status.manufacturerId;
    if (m_enroll_type.empty())
    {
        m_enroll_type.push_back(jieshou_type);
    }
    else
    {
        for (const auto &item : m_enroll_type)
        {
            enroll_type_s enroll_linshi = item;
            if (enroll_linshi.dev == topic_nodid)
            {
                enroll_type_flag = 1;
                break;
            }
        }
        if (enroll_type_flag == 0)
        {
            m_enroll_type.push_back(jieshou_type);
        }
        else if (enroll_type_flag == 1)
        {
            return;
        }
    }
    if (login_flag == 1)
    {
        IEC_LOG_RECORD(eRunType, "Name:%s.\n", nodID_IOT.c_str());
        IEC_LOG_RECORD(eRunType, "model:%s.\n", status.model.c_str());
        IOTA_HubDeviceAdd(1099, &stDeviceInfo);
    }
}

void CTaskManager::Unpackdataup(neb::CJsonObject obj)
{
    int qos = 1;
    char *deviceId = NULL;
    char *data = NULL;
    char *pcServiceId = NULL;
    std::string mmm;
    std::string time_send_data;
    obj.Get("timestamp", time_send_data);
    neb::CJsonObject obj_body;
    if (!obj.Get("body", obj_body))
    {
        return;
    }
    data_Report_s data_Rt;
    if (obj_body.Get("serviceId", data_Rt.serviceId))
    {
        mskprintf("serviceId = %s.\n\r", data_Rt.serviceId.c_str());
    }
    neb::CJsonObject obj_serviceProperties;
    if (obj_body.Get("serviceProperties", obj_serviceProperties))
    {
        data_Rt.serviceProperties = obj_serviceProperties.ToString();
        mskprintf("serviceProperties = %s.\n\r", data_Rt.serviceProperties.c_str());
    }
    std::string json_key;
    std::string key_data;
    neb::CJsonObject Body_Split;
    uint16_t connet = 0;
    while (obj_serviceProperties.GetKey(json_key))
    {
        obj_serviceProperties.Get(json_key, key_data);

        Body_Split.Add(json_key, key_data);
        connet++;
        if (connet == 50)
        {
            data_Rt.serviceProperties = Body_Split.ToString();
            std::list<Registration_Information_s>::iterator it = m_deviceId_storage.begin();
            for (; it != m_deviceId_storage.end(); it++)
            {
                Registration_Information_s device_old = *it;
                mskprintf("device_old.dev=%s.\n", device_old.dev.c_str());
                mskprintf("topic_nodid=%s.\n", topic_nodid.c_str());
                if (device_old.dev.compare(topic_nodid) == 0)
                {
                    mmm = device_old.Deviceid;
                    deviceId = const_cast<char *>(mmm.c_str());
                    mskprintf("mmm:%s.\n", mmm.c_str());
                    break;
                }
            }
            pcServiceId = const_cast<char *>(data_Rt.serviceId.c_str());
            data = const_cast<char *>(data_Rt.serviceProperties.c_str());
            mskprintf("data_Rt.serviceId.c_str():%s.\n", data_Rt.serviceId.c_str());
            mskprintf("data_Rt.serviceProperties.c_str():%s.\n", data_Rt.serviceProperties.c_str());
            mskprintf("device_old.Deviceid.c_str():%s.\n", mmm.c_str());
            if ((deviceId == NULL) || (pcServiceId == NULL) || (data == NULL))
            {
                mskprintf("deviceId or pcServiceIdor data is null\n ");
                IEC_LOG_RECORD(eErrType, "deviceId or pcServiceIdor data is null");
                IEC_LOG_RECORD(eErrType, "deviceId:%s.\n", mmm.c_str());
                IEC_LOG_RECORD(eErrType, "pcServiceIdor:%s.\n", data_Rt.serviceId.c_str());
                IEC_LOG_RECORD(eErrType, "send dev:%s.\n", topic_nodid.c_str());
                if (deviceId == NULL)
                {
                    for (const auto &item : m_enroll_type)
                    {
                        enroll_type_s enroll_linshi = item;
                        if (enroll_linshi.dev == topic_nodid)
                        {
                            ST_IOTA_DEVICE_INFO stDeviceInfo = {0};

                            stDeviceInfo.pcName = const_cast<char *>(enroll_linshi.iot_nodid.c_str());
                            stDeviceInfo.pcNodeId = const_cast<char *>(enroll_linshi.iot_nodid.c_str());
                            stDeviceInfo.pcManufacturerId = const_cast<char *>(enroll_linshi.manuid.c_str());
                            stDeviceInfo.pcModel = const_cast<char *>(enroll_linshi.model.c_str());
                            IEC_LOG_RECORD(eRunType, "Name:%s.\n", enroll_linshi.iot_nodid.c_str());
                            IEC_LOG_RECORD(eRunType, "model:%s.\n", enroll_linshi.model.c_str());
                            IOTA_HubDeviceAdd(1020, &stDeviceInfo);
                            break;
                        }
                    }
                }
            }
            else
            {
                IOTA_ServiceDataReport_Qos(deviceId, pcServiceId, data, qos);
                IEC_LOG_RECORD(eRunType, "data send time");
                IEC_LOG_RECORD(eRunType, "send dev:%s.\n", topic_nodid.c_str());
            }
            connet = 0;
            Body_Split.Clear();
        }
    }
    if (connet != 0)
    {
        data_Rt.serviceProperties = Body_Split.ToString();
        std::list<Registration_Information_s>::iterator it = m_deviceId_storage.begin();
        for (; it != m_deviceId_storage.end(); it++)
        {
            Registration_Information_s device_old = *it;
            mskprintf("device_old.dev=%s.\n", device_old.dev.c_str());
            mskprintf("topic_nodid=%s.\n", topic_nodid.c_str());
            if (device_old.dev.compare(topic_nodid) == 0)
            {
                mmm = device_old.Deviceid;
                deviceId = const_cast<char *>(mmm.c_str());
                mskprintf("mmm:%s.\n", mmm.c_str());
                break;
            }
        }
        pcServiceId = const_cast<char *>(data_Rt.serviceId.c_str());
        data = const_cast<char *>(data_Rt.serviceProperties.c_str());
        mskprintf("data_Rt.serviceId.c_str():%s.\n", data_Rt.serviceId.c_str());
        mskprintf("data_Rt.serviceProperties.c_str():%s.\n", data_Rt.serviceProperties.c_str());
        mskprintf("device_old.Deviceid.c_str():%s.\n", mmm.c_str());
        if ((deviceId == NULL) || (pcServiceId == NULL) || (data == NULL))
        {
            mskprintf("deviceId or pcServiceIdor data is null\n ");
            IEC_LOG_RECORD(eErrType, "deviceId or pcServiceIdor data is null");
            IEC_LOG_RECORD(eErrType, "deviceId:%s.\n", mmm.c_str());
            IEC_LOG_RECORD(eErrType, "pcServiceIdor:%s.\n", data_Rt.serviceId.c_str());
            IEC_LOG_RECORD(eErrType, "send dev:%s.\n", topic_nodid.c_str());
            if (deviceId == NULL)
            {
                for (const auto &item : m_enroll_type)
                {
                    enroll_type_s enroll_linshi = item;
                    if (enroll_linshi.dev == topic_nodid)
                    {
                        ST_IOTA_DEVICE_INFO stDeviceInfo = {0};

                        stDeviceInfo.pcName = const_cast<char *>(enroll_linshi.iot_nodid.c_str());
                        stDeviceInfo.pcNodeId = const_cast<char *>(enroll_linshi.iot_nodid.c_str());
                        stDeviceInfo.pcManufacturerId = const_cast<char *>(enroll_linshi.manuid.c_str());
                        stDeviceInfo.pcModel = const_cast<char *>(enroll_linshi.model.c_str());
                        IEC_LOG_RECORD(eRunType, "Name:%s.\n", enroll_linshi.iot_nodid.c_str());
                        IEC_LOG_RECORD(eRunType, "model:%s.\n", enroll_linshi.model.c_str());
                        IOTA_HubDeviceAdd(1020, &stDeviceInfo);
                        break;
                    }
                }
            }
        }
        else
        {
            IOTA_ServiceDataReport_Qos(deviceId, pcServiceId, data, qos);
            IEC_LOG_RECORD(eRunType, "data send time");
            IEC_LOG_RECORD(eRunType, "send dev:%s.\n", topic_nodid.c_str());
        }
        connet = 0;
    }
}

void CTaskManager::MqttPackOnline(CDataObj *obj)
{
    if (obj == NULL)
    {
        return;
    }

    if (m_esn.empty())
    {
        return;
    }

    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;

    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);

    neb::CJsonObject oBodyJson;
    oBodyJson.Add("state", "online");
    // obj->m_startTime
    C256String eventTime;
    STimeInfo st = ii_get_current_time();

    // 测试
    eventTime.Format("%04d-%02d-%02dT%02d:%02d:%02dZ",
                     st.nYear,
                     st.nMonth,
                     st.nDay,
                     st.nHour,
                     st.nMinute,
                     st.nSecond);

    oBodyJson.Add("event-time", eventTime.ToString()); // �???一次收到交采数�???�???
    oJson.Add("body", oBodyJson);

    C256String pub_topic;
    pub_topic.Format("%s/notify/event/gwTerminal/status/%s/%s/%s/%s/%s/%s%s",
                     CFG_APP_NAME,
                     obj->m_manufacturerId.c_str(),
                     obj->m_manufacturerName.c_str(),
                     obj->m_deviceType.c_str(),
                     obj->m_model.c_str(),
                     obj->m_protocolType.c_str(),
                     obj->m_guid.dev.c_str(),
                     m_esn.c_str());

    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
        item.msg_send_lenth = mssm.size();
        item.retained = 1;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

void CTaskManager::MqttPackData(CDataObj *obj)
{
    bool bRet = false;
    if (obj == NULL)
    {
        return;
    }

    if (m_esn.empty())
    {
        return;
    }

    std::list<CNameObj>::iterator it = obj->m_SendNameObjs.begin();
    for (; it != obj->m_SendNameObjs.end(); it++)
    {
        bRet = false;
        CNameObj nameObj = *it;
        std::string serviceId = nameObj.m_serviceName;

        if (serviceId.compare("discrete") == 0 && CParamManager::CreateInstance().m_yxcycle == 0)
        {
            continue;
        }

        mqtt_header_s jsheader = {0};
        int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
        if (ret != MQTT_OK)
        {
            MG_LOG_E("\nJson head fill failed.\n");
            return;
        }

        SMTimeInfo currenttime = ::ii_get_current_mtime();
        ret = snprintf(jsheader.timestamp, 32, "%04d-%02d-%02dT%02d:%02d:%02dZ",
                       currenttime.nYear,
                       currenttime.nMonth,
                       currenttime.nDay,
                       currenttime.nHour,
                       currenttime.nMinute,
                       0);

        neb::CJsonObject oJson;

        oJson.Add("token", jsheader.token);
        oJson.Add("timestamp", jsheader.timestamp);

        neb::CJsonObject oBodyJson;
        neb::CJsonObject oServicePropertiesJson;
        // neb::CJsonObject oDataPropertiesJson;

        oBodyJson.Add("serviceId", serviceId);
        std::list<std::string>::iterator itName = nameObj.m_names.begin();
        for (; itName != nameObj.m_names.end(); itName++)
        {
            std::string sname = *itName;
            std::string svalue = obj->m_RealDatas[sname];
            if (svalue.size() > 0)
            {
                oServicePropertiesJson.Add(sname, svalue);
                bRet = true;
            }
        }
        if (bRet)
        {
            oBodyJson.Add("serviceProperties", oServicePropertiesJson);
            oJson.Add("body", oBodyJson);
            C256String pub_topic;
            pub_topic.Format("%s/terminal/dataReport/%s/%s/%s/%s/%s%s",
                             CFG_APP_NAME,
                             obj->m_manufacturerId.c_str(),
                             obj->m_manufacturerName.c_str(),
                             obj->m_deviceType.c_str(),
                             obj->m_model.c_str(),
                             // obj->m_protocolType.c_str(),
                             obj->m_guid.dev.c_str(),
                             m_esn.c_str());

            std::string mssm = oJson.ToString();
            if (mssm.size() > 0 && bRet)
            {
                mqtt_data_info_s item = {0};
                memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
                memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
                item.msg_send_lenth = mssm.size();
                item.retained = 0;
                CMqttClientInterManager::CreateInstance().Push_SendItem(item);
            }
        }

        ii_sleep(100);
    }
}

void CTaskManager::MqttPackSpontData(CDataObj *obj)
{
    bool bRet = false;
    if (obj == NULL)
    {
        return;
    }

    std::list<std::string>::iterator it = obj->m_SpontObj.m_Spontnames.begin();
    for (; it != obj->m_SpontObj.m_Spontnames.end(); it++)
    {
        std::string serviceId = obj->m_SpontObj.m_serviceName;
        CNameObj nameObj = obj->m_SpontObj;
        mqtt_header_s jsheader = {0};
        int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
        if (ret != MQTT_OK)
        {
            MG_LOG_E("\nJson head fill failed.\n");
            return;
        }

        neb::CJsonObject oJson;

        oJson.Add("token", jsheader.token);
        oJson.Add("timestamp", jsheader.timestamp);

        neb::CJsonObject oBodyJson;
        neb::CJsonObject oServicePropertiesJson;
        // neb::CJsonObject oDataPropertiesJson;

        oBodyJson.Add("serviceId", serviceId);
        std::list<std::string>::iterator itName = nameObj.m_Spontnames.begin();
        for (; itName != nameObj.m_Spontnames.end(); itName++)
        {
            std::string sname = *itName;
            std::string svalue = obj->m_RealDatas[sname];
            if (svalue.size() > 0)
            {
                oServicePropertiesJson.Add(sname, svalue);
                bRet = true;
            }
        }
        if (bRet)
        {
            oBodyJson.Add("serviceProperties", oServicePropertiesJson);
            oJson.Add("body", oBodyJson);
            C256String pub_topic;
            pub_topic.Format("%s/terminal/dataReport/%s/%s/%s/%s/%s%s",
                             CFG_APP_NAME,
                             obj->m_manufacturerId.c_str(),
                             obj->m_manufacturerName.c_str(),
                             obj->m_deviceType.c_str(),
                             obj->m_model.c_str(),
                             // obj->m_protocolType.c_str(),
                             obj->m_guid.dev.c_str(),
                             m_esn.c_str());

            std::string mssm = oJson.ToString();
            if (mssm.size() > 0 && bRet)
            {
                mqtt_data_info_s item = {0};
                memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
                memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
                item.msg_send_lenth = mssm.size();
                item.retained = 0;
                CMqttClientInterManager::CreateInstance().Push_SendItem(item);
            }
        }
        ii_sleep(1000);
    }
}

bool CTaskManager::IsFindDiscrete(CDataObj *obj, std::string name, std::string &serveiceName)
{
    bool bRet = false;
    std::string serviceDis = "discrete";
    std::string serviceDis1 = "yx";
    std::string serviceDis2 = "YX";

    std::map<std::string, CNameObj>::iterator iter = obj->m_serviceIds.find(serviceDis);
    if (iter != obj->m_serviceIds.end())
    {
        CNameObj nameObj = obj->m_serviceIds[serviceDis];
        std::list<std::string>::iterator itName = nameObj.m_names.begin();
        for (; itName != nameObj.m_names.end(); itName++)
        {
            std::string sname = *itName;
            if (sname.compare(name.c_str()) == 0)
            {
                serveiceName = serviceDis;
                return true;
            }
        }
    }

    std::map<std::string, CNameObj>::iterator iter1 = obj->m_serviceIds.find(serviceDis1);
    if (iter1 != obj->m_serviceIds.end())
    {
        CNameObj nameObj = obj->m_serviceIds[serviceDis1];
        std::list<std::string>::iterator itName = nameObj.m_names.begin();
        for (; itName != nameObj.m_names.end(); itName++)
        {
            std::string sname = *itName;
            if (sname.compare(name.c_str()) == 0)
            {
                serveiceName = serviceDis1;
                return true;
            }
        }
    }

    std::map<std::string, CNameObj>::iterator iter2 = obj->m_serviceIds.find(serviceDis2);
    if (iter2 != obj->m_serviceIds.end())
    {
        CNameObj nameObj = obj->m_serviceIds[serviceDis2];
        std::list<std::string>::iterator itName = nameObj.m_names.begin();
        for (; itName != nameObj.m_names.end(); itName++)
        {
            std::string sname = *itName;
            if (sname.compare(name.c_str()) == 0)
            {
                serveiceName = serviceDis2;
                return true;
            }
        }
    }

    return bRet;
}

std::string CTaskManager::GetLastSegment(std::string topic)
{
    std::string str;
    size_t index = topic.find_last_of("/");
    if (index != std::string::npos && index != topic.length() - 1)
    {
        str = topic.substr(index + 1);
    }
    return str;
}
std::string CTaskManager::GetFirsAppName(std::string topic)
{
    std::string str;
    size_t index = topic.find_first_of("/", 0);
    if (index != std::string::npos)
    {
        str = topic.substr(0, index);
    }
    return str;
}

bool CTaskManager::GetStringVlaue(neb::CJsonObject obj, const char *key, C64String &str)
{
    std::string s;
    if (obj.Get(key, s))
    {
        str = s;
        return true;
    }
    else
    {
        return false;
    }
}

std::string CTaskManager::CreatMd5(std::string str)
{
    MD5_CTX md5;
    std::string out;
    unsigned char md[16] = {0};

    if (!MD5_Init(&md5))
    {
        mskprintf("MD5_Init error\n");
        return "";
    }
    if (!MD5_Update(&md5, str.c_str(), strlen(str.c_str())))
    {
        mskprintf("MD5_Update error\n");
        return "";
    }
    if (!MD5_Final(md, &md5))
    {
        mskprintf("MD5_Final error\n");
        return "";
    }
    for (int i = 0; i < 16; i++)
    {
        char outc[3] = {0};
        sprintf(outc, "%02X", md[i]);
        std::string outb = outc;
        out = out + outb;
    }
    return out;
}
CTaskManager::CTaskManager()
{
    m_Thread.SetPackageAcquireManager(this);
    m_T.SetParam(120 * 1000); // 默�??120�???
    m_T.StartCount();
    m_ESN_T.SetParam(600 * 1000); // 默�??600�???
    m_ESN_T.StartCount();
    m_Data_T.SetParam(30 * 1000); //
    m_Data_T.StartCount();
}

CTaskManager::~CTaskManager()
{
    std::list<CDataObj *>::iterator iter = m_dataObj.begin();
    for (; iter != m_dataObj.end(); ++iter)
    {
        CDataObj *obj = *iter;
        if (obj != NULL)
        {
            delete obj;
            obj = NULL;
        }
    }
}

CNameObj::CNameObj()
{
}

CNameObj::~CNameObj()
{
}
