{"files.associations": {"string": "cpp", "new": "cpp", "*.tcc": "cpp", "bitset": "cpp", "cstdlib": "cpp", "array": "cpp", "deque": "cpp", "list": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "vector": "cpp", "string_view": "cpp", "initializer_list": "cpp", "cctype": "cpp", "chrono": "cpp", "clocale": "cpp", "cmath": "cpp", "cstdarg": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "ratio": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "fstream": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "memory": "cpp", "ostream": "cpp", "numeric": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "cinttypes": "cpp", "utility": "cpp", "typeinfo": "cpp"}, "C_Cpp.errorSquiggles": "disabled", "code-runner.runInTerminal": true}