{"configurations": [{"type": "cmake", "request": "launch", "name": "CMake: Configure project", "cmakeDebugType": "configure", "clean": false, "configureAll": false}, {"name": "C/C++: make 构建和调试活动文件", "type": "cppdbg", "request": "launch", "program": "${fileDirname}/${fileBasenameNoExtension}", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "environment": [], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "将反汇编风格设置为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "preLaunchTask": "C/C++: g++.exe 生成活动文件", "miDebuggerPath": "gdb"}], "version": "2.0.0"}